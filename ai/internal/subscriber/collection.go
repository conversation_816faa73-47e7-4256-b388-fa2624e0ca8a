package subscriber

import (
	"context"
	"fmt"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	assistantlogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/assistant"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	eventspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events"
	"gorm.io/gorm"
)

// OnCollectionReindex 重建索引
func (a *Ai) OnCollectionReindex(ctx context.Context, evt *eventspb.CollectionReindex) error {
	task, err := model.NewQuery[model.TCollectionReindex](ctx).With("Collection").FindByKey(evt.TaskId)
	if err != nil {
		log.WithContext(ctx).Errorw("query reindex task fail", "task_id", evt.TaskId, "err", err)
		return nil
	}
	if task.State != aipb.ReindexState_REINDEX_STATE_WAITING {
		return nil
	}

	// 更新为运行态
	task.State = aipb.ReindexState_REINDEX_STATE_RUNNING
	task.UpdateDate = time.Now()
	rowsAffected, err := model.NewQuery[model.TCollectionReindex](ctx).UpdateByKey(task.ID, map[string]any{
		"state":       aipb.ReindexState_REINDEX_STATE_RUNNING,
		"update_date": task.UpdateDate,
	})
	if err != nil {
		log.WithContext(ctx).Errorw("update reindex task state failed", "task_id", evt.TaskId, "err", err)
		return nil
	}
	// 已经被其它消费者消费了
	if rowsAffected == 0 {
		return nil
	}

	var taskChanges []string

	// 保存任务状态
	defer func() {
		task.TriedCnt += 1
		task.UpdateDate = time.Now()
		taskChanges = append(taskChanges, "tried_cnt", "update_date")

		if task.TriedCnt >= config.GetUint32Or("assistant.collection.maxReindexTries", 10) {
			task.State = aipb.ReindexState_REINDEX_STATE_FINISHED
			task.Result = aipb.ReindexResult_REINDEX_RESULT_FAILED
			taskChanges = append(taskChanges, "state", "result")
		}

		if err := model.NewQuery[model.TCollectionReindex](ctx).Select(taskChanges).Save(task); err != nil {
			log.WithContext(ctx).Errorw("update reindex task state fail", "task", task, "changes", taskChanges, "err", err)
		}
	}()

	// 初始化新索引
	collection := task.Collection
	collection.IndexName = task.Dest
	if err = assistantlogic.InitCollectionIndex(ctx, collection, true); err != nil {
		log.WithContext(ctx).Errorw("init new collection index failed",
			"task_id", evt.TaskId, "new_index", task.Dest, "err", err)

		task.State = aipb.ReindexState_REINDEX_STATE_WAITING
		task.LastError = "init dest index: " + err.Error()
		taskChanges = append(taskChanges, "state", "last_error")

		return nil
	}

	// 创建reindex任务
	esTaskID, err := assistantlogic.CreateEsReindexTask(ctx, task.Source, task.Dest)
	if err != nil {
		log.WithContext(ctx).Errorw("create collection reindex task failed", "task_id", evt.TaskId, "err", err)

		task.State = aipb.ReindexState_REINDEX_STATE_WAITING
		task.LastError = "create reindex task: " + err.Error()
		taskChanges = append(taskChanges, "state", "last_error")

		return nil
	}

	// 启动轮询任务结果协程
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		if err := pollReindexTaskResult(ctx, task); err != nil {
			log.WithContext(ctx).Errorw("poll reindex task result fail", "task_id", task.ID, "err", err)
		}
		return nil
	}, boot.TraceGo(ctx))

	task.EsTaskID = esTaskID
	taskChanges = append(taskChanges, "es_task_id")

	return nil
}

func pollReindexTaskResult(ctx context.Context, task *model.TCollectionReindex) error {
	maxErrs := config.GetIntOr("assistant.collection.reindexTaskMaxErrs", 10)
	retryAfter := config.GetDurationOr("assistant.collection.reindexTaskRetryAfter", time.Second*10)

	err := assistantlogic.PollReindexTaskResultUntilCompleted(ctx, task.EsTaskID, maxErrs, retryAfter)
	if err != nil {
		_, err = model.NewQuery[model.TCollectionReindex](ctx).UpdateByKey(task.ID, map[string]any{
			"state":       aipb.ReindexState_REINDEX_STATE_FINISHED,
			"result":      aipb.ReindexResult_REINDEX_RESULT_FAILED,
			"last_error":  err.Error(),
			"update_date": time.Now(),
		})
	} else {
		err = model.Transaction(ctx, func(tx *gorm.DB) error {
			err := tx.Model(&model.TCollectionReindex{}).Where("id = ?", task.ID).Updates(map[string]any{
				"state":       aipb.ReindexState_REINDEX_STATE_FINISHED,
				"result":      aipb.ReindexResult_REINDEX_RESULT_SUCCESS,
				"update_date": time.Now(),
			}).Error
			if err != nil {
				return fmt.Errorf("update collection_reindex result: %w", err)
			}

			task.Collection.IndexName = task.Dest
			if err = tx.Select("index_name").Save(task.Collection).Error; err != nil {
				return fmt.Errorf("save collection index_name: %w", err)
			}

			return nil
		})
	}
	if err != nil {
		return fmt.Errorf("update reindex task result: %w", err)
	}

	// 删除原索引
	if err = assistantlogic.DeleteEsIndex(ctx, task.Source); err != nil {
		return fmt.Errorf("delete source index: %w", err)
	}

	return nil
}
