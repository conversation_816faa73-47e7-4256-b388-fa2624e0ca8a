package model

import (
	"time"

	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TCollectionReindex [...]
type TCollectionReindex struct {
	ID           uint64             `gorm:"primaryKey;column:id" json:"id"`                                 // 任务ID
	CollectionID uint64             `gorm:"column:collection_id" json:"collectionId"`                       // 知识库ID
	Source       string             `gorm:"column:source" json:"source"`                                    // 源索引
	Dest         string             `gorm:"column:dest" json:"dest"`                                        // 目标索引
	RunDate      string             `gorm:"column:run_date" json:"runDate"`                                 // 执行日期
	State        aipb.ReindexState  `gorm:"column:state" json:"state"`                                      // 状态：1等待执行，2执行中，3已完成
	EsTaskID     string             `gorm:"column:es_task_id;default:''" json:"esTaskId"`                   // ES任务ID
	TriedCnt     uint32             `gorm:"column:tried_cnt;default:0" json:"triedCnt"`                     // 尝试次数
	LastError    string             `gorm:"column:last_error" json:"lastError"`                             // 上次尝试的错误
	Result       aipb.ReindexResult `gorm:"column:result;default:0" json:"result"`                          // 结果：1成功，2失败
	CreateDate   time.Time          `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"` // 创建时间
	UpdateDate   time.Time          `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate"` // 更新时间

	Collection *TCollection `gorm:"foreignKey:collection_id" json:"collection,omitempty"` // 所属知识库
}

// TableName get sql table name.获取数据库表名
func (m *TCollectionReindex) TableName() string {
	return "t_collection_reindex"
}

// TCollectionReindexColumns get sql column name.获取数据库列名
var TCollectionReindexColumns = struct {
	ID           string
	CollectionID string
	Source       string
	Dest         string
	RunDate      string
	State        string
	EsTaskID     string
	TriedCnt     string
	LastError    string
	Result       string
	CreateDate   string
	UpdateDate   string
}{
	ID:           "id",
	CollectionID: "collection_id",
	Source:       "source",
	Dest:         "dest",
	RunDate:      "run_date",
	State:        "state",
	EsTaskID:     "es_task_id",
	TriedCnt:     "tried_cnt",
	LastError:    "last_error",
	Result:       "result",
	CreateDate:   "create_date",
	UpdateDate:   "update_date",
}
