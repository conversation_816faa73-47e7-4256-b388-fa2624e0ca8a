package model

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
)

const ConnNameV1 = "v1"

const (
	ConnNameV2         = "v2"
	ConnNameV1Mgmt     = "v1_op"
	ConnNameBI         = "metric"
	ConnNameV2ReadOnly = "v2_readonly"
)

// 上下文键，用于标记当前请求是否应该使用只读库
type contextKey string

const (
	ContextKeyUseReadOnly contextKey = "use_readonly"
)

// NewQuery 新建查询 - 自动检测只读库标记并路由到合适的数据库
func NewQuery[Model any](ctx context.Context) *xorm.Query[Model] {
	// 检查上下文中是否标记了使用只读库
	if ShouldUseReadOnlyInContext(ctx) {
		return xorm.NewQueryWithDB[Model](NewConnectionV2ReadOnlyFallback(ctx))
	}
	return xorm.NewQueryUsing[Model](ctx, ConnNameV2)
}

// NewConnectionV1 新建connection
func NewConnectionV1(ctx context.Context) *gorm.DB {
	db := xorm.Use(ctx, ConnNameV1)
	return db
}

// NewConnection 新建connection
func NewConnection(ctx context.Context) *gorm.DB {
	db := xorm.Use(ctx, ConnNameV2)
	return db
}

// NewConnectionV1Mgmt 新建connection
func NewConnectionV1Mgmt(ctx context.Context) *gorm.DB {
	db := xorm.Use(ctx, ConnNameV1Mgmt)
	return db
}

// NewConnectionV2ReadOnly 新建备库连接 - 用于重查询操作
func NewConnectionV2ReadOnlyFallback(ctx context.Context) *gorm.DB {
	readDB := xorm.DefaultManager.Use(ConnNameV2ReadOnly)
	if readDB == nil {
		return xorm.Use(ctx, ConnNameV2)
	}
	return readDB.WithContext(ctx)
}

// Transaction 事务
func Transaction(ctx context.Context, f func(tx *gorm.DB) error) error {
	if ShouldUseReadOnlyInContext(ctx) {
		return NewConnectionV2ReadOnlyFallback(ctx).Transaction(f)
	}
	return xorm.Use(ctx, ConnNameV2).Transaction(f)
}

// WithReadOnlyContext 在上下文中标记当前请求应该使用只读库
func WithReadOnlyContext(ctx context.Context) context.Context {
	return context.WithValue(ctx, ContextKeyUseReadOnly, true)
}

// ShouldUseReadOnlyInContext 检查上下文中是否标记了使用只读库
func ShouldUseReadOnlyInContext(ctx context.Context) bool {
	if useReadOnly, ok := ctx.Value(ContextKeyUseReadOnly).(bool); ok {
		return useReadOnly
	}
	return false
}

// WithTQLContext 在上下文中标记当前请求包含TQL查询（兼容性函数）
// 内部会检查配置决定是否使用只读库
func WithTQLContext(ctx context.Context, hasTQL bool) context.Context {
	if hasTQL && config.GetBoolOr("db.tql_use_readonly", true) {
		return WithReadOnlyContext(ctx)
	}
	return ctx
}

// ParseOrder 处理排序
func ParseOrder[Model any](orderBy []*basepb.OrderBy, whitelist map[string]bool,
	query *xorm.Query[Model], parse func(string) string,
) {
	if len(orderBy) == 0 {
		return
	}

	for _, v := range orderBy {
		if whitelist[v.Column] {
			if parse != nil {
				query.OrderBy(parse(v.Column), v.Desc)
			} else {
				query.OrderBy(v.Column, v.Desc)
			}
		}
	}
}
