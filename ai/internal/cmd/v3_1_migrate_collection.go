package cmd

import (
	"context"
	"fmt"
	"os"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	assistantlogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/assistant"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/publisher"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	eventspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events"
)

func v3d1CreateAllCollectionIndexAlias() {
	if !config.GetBool("tanlive.cmd.V3D1CREATEALLCOLLECTIONINDEXALIAS") {
		return
	}
	defer os.Exit(0)

	fmt.Printf("开始创建助手索引别名\n")

	var (
		ctx       = context.Background()
		batchSize = 50
		parallel  = 2
		lastID    = uint64(0)
	)

	for {
		assistants, err := model.NewQuery[model.TAssistant](ctx).
			With("AssistantCollections.Collection").
			Where("id > ?", lastID).
			Where("is_draft = 0").
			Where("EXISTS (SELECT 1 FROM t_assistant_collection a INNER JOIN t_collection b ON a.collection_id = b.id WHERE a.assistant_id = t_assistant.id AND b.index_created = 0)").
			Limit(batchSize).OrderBy("id", false).Get()
		if err != nil {
			fmt.Printf("query assistants error: %v\n", err)
			return
		}
		if len(assistants) == 0 {
			break
		}

		g := xsync.NewGroup(ctx, xsync.GroupQuota(parallel))
		for _, assistant := range assistants {
			lastID = assistant.ID
			if len(assistant.AssistantCollections) == 0 || assistant.AssistantCollections[0].Collection == nil {
				continue
			}
			assistant := assistant
			collection := assistant.AssistantCollections[0].Collection
			g.Go(func(ctx context.Context) error {
				existed, err := assistantlogic.IsEsIndexExist(ctx, collection.RagName)
				if err != nil {
					fmt.Printf("检查助手[%s]索引时出错: %v\n", assistant.Name, err)
					return nil
				}

				// 索引不存在时，跳过
				if !existed {
					return nil
				}

				// 索引存在时，就创建别名
				collection.IndexName = collection.RagName
				collection.RagName = assistantlogic.NewRagName(assistant.NameEn, collection.Lang)

				if err = assistantlogic.CreateEsAlias(ctx, collection.IndexName, collection.RagName); err != nil {
					fmt.Printf("创建助手[%s]索引别名失败：%v\n", assistant.Name, err)
					return nil
				}

				collection.IndexCreated = true
				if err = model.NewQuery[model.TCollection](ctx).
					Select("index_name", "rag_name", "index_created").Save(collection); err != nil {
					fmt.Printf("保存助手[%s]索引别名失败：%v\n", assistant.Name, err)
					return nil
				}

				return nil
			})
		}
		g.Wait()
	}

	fmt.Printf("创建助手索引别名完成\n")
}

func v3d1ReindexAllCollectionIndex() {
	if !config.GetBool("tanlive.cmd.V3D1REINDEXALLCOLLECTIONINDEX") {
		return
	}
	defer os.Exit(0)

	fmt.Printf("开始创建reindex任务\n")

	var (
		ctx       = context.Background()
		batchSize = 50
		lastID    = uint64(0)
		failed    []uint64
	)

	for {
		assistants, err := model.NewQuery[model.TAssistant](ctx).
			With("AssistantCollections.Collection").
			Where("id > ?", lastID).
			Where("is_draft = 0").
			Limit(batchSize).OrderBy("id", false).Get()
		if err != nil {
			fmt.Printf("query assistants error: %v\n", err)
			return
		}
		if len(assistants) == 0 {
			break
		}

		for _, assistant := range assistants {
			lastID = assistant.ID
			if len(assistant.AssistantCollections) == 0 || assistant.AssistantCollections[0].Collection == nil {
				continue
			}
			collection := assistant.AssistantCollections[0].Collection

			if !collection.IndexCreated {
				continue
			}

			reindexTask := &model.TCollectionReindex{
				CollectionID: collection.ID,
				Source:       collection.IndexName,
				Dest:         assistantlogic.NewIndexName(collection.RagName),
				RunDate:      time.Now().Format("2006-01-02"),
				State:        aipb.ReindexState_REINDEX_STATE_WAITING,
				CreateDate:   time.Now(),
				UpdateDate:   time.Now(),
			}

			err = model.NewQuery[model.TCollectionReindex](ctx).Create(reindexTask)
			if err != nil {
				failed = append(failed, assistant.ID)
				fmt.Printf("创建reindex任务失败：助手[%d]，知识库[%d]，错误[%v]\n", assistant.ID, collection.ID, err)
			} else {
				publisher.AiTopicPublisher.PublishCollectionReindexEvent(ctx, &eventspb.CollectionReindex{
					TaskId: reindexTask.ID,
				})
			}
		}
	}

	fmt.Printf("创建reindex任务结束\n")
	if len(failed) > 0 {
		fmt.Printf("失败助手列表：%v\n", failed)
	}
}

func v3d1InitCollectionIndex() {
	if !config.GetBool("tanlive.cmd.V3D1INITCOLLECTIONINDEX") {
		return
	}
	defer os.Exit(0)

	fmt.Printf("初始化未创建索引的助手\n")

	var (
		ctx       = context.Background()
		batchSize = 50
		parallel  = 2
		lastID    = uint64(0)
	)

	for {
		assistants, err := model.NewQuery[model.TAssistant](ctx).
			With("AssistantCollections.Collection").
			Where("id > ?", lastID).
			Where("is_draft = 0").
			Where("EXISTS (SELECT 1 FROM t_assistant_collection a INNER JOIN t_collection b ON a.collection_id = b.id WHERE a.assistant_id = t_assistant.id AND b.index_created = 0)").
			Limit(batchSize).OrderBy("id", false).Get()
		if err != nil {
			fmt.Printf("query assistants error: %v\n", err)
			return
		}
		if len(assistants) == 0 {
			break
		}

		g := xsync.NewGroup(ctx, xsync.GroupQuota(parallel))
		for _, assistant := range assistants {
			lastID = assistant.ID
			if len(assistant.AssistantCollections) == 0 || assistant.AssistantCollections[0].Collection == nil {
				continue
			}
			assistant := assistant
			collection := assistant.AssistantCollections[0].Collection
			g.Go(func(ctx context.Context) error {
				existed, err := assistantlogic.IsEsIndexExist(ctx, collection.RagName)
				if err != nil {
					fmt.Printf("检查助手[%s]索引时出错: %v\n", assistant.Name, err)
					return nil
				}

				// 索引已存在就跳过
				if existed {
					return nil
				}

				fmt.Printf("助手[%s]索引不存在，重新格式化rag_name，并新建索引\n", assistant.Name)

				collection.RagName = assistantlogic.NewRagName(assistant.NameEn, collection.Lang)
				if err = model.NewQuery[model.TCollection](ctx).Select("rag_name").Save(collection); err != nil {
					fmt.Printf("保存助手助手[%s]索引别名: %v\n", assistant.Name, err)
					return nil
				}
				if err = assistantlogic.InitCollectionIndex(ctx, collection, false); err != nil {
					fmt.Printf("初始化助手[%s]索引: %v\n", assistant.Name, err)
					return nil
				}

				return nil
			})
		}
		g.Wait()
	}

	fmt.Printf("创建助手索引别名完成\n")
}
