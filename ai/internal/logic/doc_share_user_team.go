package logic

import (
	"context"
	"errors"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"

	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
)

// DocStateWhensShareToUserTeam 知识分享给个人或者团队后，默认的状态
const DocStateWhensShareToUserTeam = aipb.DocShareState_DOC_SHARE_STATE_DISABLED

// 知识分享发送方
type DocShareSender struct {
	Type base.IdentityType // 类型
	ID   uint64            // id
}

// SyncDocShareUserTeam 分享doc至个人和团队
func SyncDocShareUserTeam(ctx context.Context, sender *DocShareSender, receivers []*aipb.DocShareReceiver, docIDs []uint64) error {
	// 参数校验
	if len(docIDs) == 0 {
		return nil
	}

	// 如果receivers为空，表示取消所有分享
	if len(receivers) == 0 {
		// 删除所有分享关系
		err := model.Transaction(ctx, func(tx *gorm.DB) error {
			return tx.
				Where("share_type in ?",
					[]interface{}{aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER, aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM}).
				Where("doc_id IN (?) AND create_by = ? AND create_by_type = ?",
					docIDs, sender.ID, sender.Type).
				Delete(&model.TDocShare{}).Error
		})
		if err != nil {
			log.WithContext(ctx).Errorf("SyncDocShareUserTeam delete all shares failed: %v", err)
			return err
		}
		return nil
	}

	// 获取接收方设置
	userSetting, teamSetting, err := getReceiverSettings(ctx, sender, receivers)
	if err != nil {
		log.WithContext(ctx).Errorf("SyncDocShareUserTeam getReceiverSettings failed: %v", err)
		return err
	}

	// 构建分享关系
	var docShares []*model.TDocShare
	for _, docID := range docIDs {
		for _, receiver := range receivers {
			var state aipb.DocShareState
			var ok bool
			// 检查接收方设置，判断是否可以接收分享
			if receiver.ShareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER {
				if state, ok = userSetting[receiver.Id]; !ok || state == aipb.DocShareState_DOC_SHARE_STATE_NOT_ACCEPTED {
					continue
				}
			}
			if receiver.ShareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM {
				if state, ok = teamSetting[receiver.Id]; !ok || state == aipb.DocShareState_DOC_SHARE_STATE_NOT_ACCEPTED {
					continue
				}
			}

			// 创建分享关系
			docShares = append(docShares, &model.TDocShare{
				DocID:        docID,
				ShareType:    receiver.ShareType,
				TargetID:     receiver.Id,
				State:        uint32(state),
				CreateBy:     sender.ID,
				CreateByType: sender.Type,
			})
		}
	}

	// 事务处理
	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		// 删除旧的分享关系
		if err := tx.Where("doc_id IN (?) AND create_by = ? AND create_by_type = ?",
			docIDs, sender.ID, sender.Type).Delete(&model.TDocShare{}).Error; err != nil {
			return err
		}

		// 创建新的分享关系
		if len(docShares) > 0 {
			if err := tx.CreateInBatches(docShares, 100).Error; err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		log.WithContext(ctx).Errorf("SyncDocShareUserTeam transaction failed: %v", err)
		return err
	}

	return nil
}

// 获取接收方设置, 返回用户和团队设置
func getReceiverSettings(ctx context.Context, sender *DocShareSender, receivers []*aipb.DocShareReceiver) (
	userSetting map[uint64]aipb.DocShareState, teamSetting map[uint64]aipb.DocShareState, err error,
) {
	var userReceivers, teamReceivers []*aipb.DocShareReceiver
	for _, v := range receivers {
		if v.ShareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM {
			teamReceivers = append(teamReceivers, v)
		}
		if v.ShareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER {
			userReceivers = append(userReceivers, v)
		}
	}

	if len(userReceivers) != 0 {
		userSetting, err = getReceiverSettingsByType(ctx, aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER, sender, userReceivers)
		if err != nil {
			return
		}
	}
	if len(teamReceivers) != 0 {
		teamSetting, err = getReceiverSettingsByType(ctx, aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM, sender, teamReceivers)
		if err != nil {
			return
		}
	}
	return
}

// getReceiverSettings 获取接收方设置, 返回接收方id和状态(已排除了不接收的)
// 只能同一种类型的接收方设置
func getReceiverSettingsByType(ctx context.Context, shareType aipb.DocShareReceiverType,
	sender *DocShareSender, receivers []*aipb.DocShareReceiver,
) (map[uint64]aipb.DocShareState, error) {
	// 参数校验
	if len(receivers) == 0 {
		return nil, errors.New("receivers is empty")
	}

	receiverSettings := make(map[uint64]aipb.DocShareState)

	var receiverIDs []uint64
	for _, receiver := range receivers {
		receiverIDs = append(receiverIDs, receiver.Id)
	}
	if shareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER ||
		shareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM {

		// 默认都是可接收的, 状态为禁用
		for _, v := range receiverIDs {
			receiverSettings[v] = DocStateWhensShareToUserTeam
		}

		// 分享到个人或团队
		var adminType basepb.IdentityType
		if shareType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER {
			adminType = basepb.IdentityType_IDENTITY_TYPE_USER
		} else {
			adminType = basepb.IdentityType_IDENTITY_TYPE_TEAM
		}

		// 批量查询接收设置,查出可能接收的用户或团队
		var properlyReceivers []*model.TShareReceiver
		err := model.NewQuery[model.TShareReceiver](ctx).DB().
			Where("create_by IN (?) AND admin_type = ?", receiverIDs, adminType).
			Where("admin_assistant_id = 0").
			Where("receiver_state = ?", 1). // 只查接收的
			Find(&properlyReceivers).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		if len(properlyReceivers) == 0 {
			return receiverSettings, nil
		}
		// 可能接收的接收方id,需要在TShareReceiverUser表进行验证是否真的接收
		properlyReceiverIDs := make([]uint64, 0, len(properlyReceivers))
		for _, receiver := range properlyReceivers {
			properlyReceiverIDs = append(properlyReceiverIDs, receiver.ID)
		}

		// 查询明确接收的接收方设置
		query := model.NewQuery[model.TShareReceiverUser](ctx).
			Where("receiver_id IN (?)", properlyReceiverIDs)
		if sender.Type == base.IdentityType_IDENTITY_TYPE_USER {
			query.Where("user_id = ?", sender.ID)
		} else {
			query.Where("team_id = ?", sender.ID)
		}
		query.Where("state = ?", 3) // 查出显示不接收的接收方
		blackListReceivers, err := query.Get()
		if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
			return nil, err
		}

		// 明确设置了不接收
		for _, v := range blackListReceivers {
			for _, vv := range properlyReceivers {
				if v.ReceiverID == vv.ID {
					receiverSettings[vv.CreateBy] = aipb.DocShareState_DOC_SHARE_STATE_NOT_ACCEPTED
				}
			}
		}
	}

	return receiverSettings, nil
}

// DocShareConfigReceiverLogic 文档分享配置接收方逻辑
type DocShareConfigReceiverLogic struct{}

func NewDocShareConfigReceiverLogic() *DocShareConfigReceiverLogic {
	return &DocShareConfigReceiverLogic{}
}

// CreateUserTeamConfig 创建个人/团队接收方设置
func (d *DocShareConfigReceiverLogic) CreateUserTeamConfig(ctx context.Context,
	create_by uint64, adminType basepb.IdentityType,
	receiveState aipb.DocShareAcceptState, otherState aipb.DocShareState, detail []*aipb.DocReceiveConfigBySender,
) error {
	// 查出原始数据
	oldReceiver, err := model.NewQuery[model.TShareReceiver](ctx).
		Where("admin_assistant_id = 0 AND admin_type = ? AND create_by = ?", adminType, create_by).Find()

	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return err
	}

	if oldReceiver != nil && oldReceiver.ID != 0 {
		// 旧数据删除
		if err := model.NewQuery[model.TShareReceiver](ctx).DeleteByKey(oldReceiver.ID); err != nil {
			return err
		}
	}

	receiver := &model.TShareReceiver{
		AdminAssistantID: 0,
		AdminType:        adminType,
		CreateBy:         create_by,
		ReceiverState:    receiveState,
		OtherState:       otherState,
	}
	if err := model.NewQuery[model.TShareReceiver](ctx).Create(receiver); err != nil {
		return err
	}

	// 关联数据删除
	if oldReceiver != nil && oldReceiver.ID != 0 {
		if _, err := model.NewQuery[model.TShareReceiverUser](ctx).DeleteBy(" receiver_id = ?",
			oldReceiver.ID); err != nil {
			return err
		}
	}

	// 设置数据少for循环依次处理
	for groupId, userShare := range detail {

		// groupId 用于分块显示
		groupId += 1

		var receiverUsers []*model.TShareReceiverUser

		for _, u := range userShare.UserId {
			receiverUser := &model.TShareReceiverUser{
				ReceiverID:       receiver.ID,
				AdminAssistantID: 0,
				UserID:           u,
				State:            userShare.State,
				GroupID:          uint64(groupId),
			}

			receiverUsers = append(receiverUsers, receiverUser)
		}

		for _, t := range userShare.TeamId {
			receiverUser := &model.TShareReceiverUser{
				ReceiverID:       receiver.ID,
				AdminAssistantID: 0,
				TeamID:           t,
				State:            userShare.State,
				GroupID:          uint64(groupId),
			}

			receiverUsers = append(receiverUsers, receiverUser)
		}

		if err := model.NewQuery[model.TShareReceiverUser](ctx).BatchInsert(receiverUsers, 100); err != nil {
			return err
		}

	}
	return nil
}

// TeamStateCanShareDoc 已认证或者发布的正式团队
func TeamStateCanShareDoc(db *gorm.DB) *gorm.DB {
	db.Where("in_lib = 0 and is_draft = 0 and is_disbanded = 0 and (is_verified = 1 OR ugc_state = ?)",
		basepb.UgcState_UGC_STATE_PUBLISHED)
	return db
}

// GetTeamCanShareDoc 获取可以分享至的团队
// 已认证或者发布的团队才行
func (d *DocShareConfigReceiverLogic) GetTeamCanShareDoc(ctx context.Context,
	sender *DocShareSender, name string, offset uint64, limit uint64,
) ([]*aipb.DocShareReceiver, error) {
	// 在TShareReceiver表中没有明确设置，默认都是可接收的
	// 否则过滤在TShareReceiverUser表中查出明确拒绝的
	// 由于涉及跨数据库查询，需要分步进行

	adminType := basepb.IdentityType_IDENTITY_TYPE_TEAM

	// 第一步：从 t_share_receiver 相关表中查询出明确拒绝接收的团队ID列表
	q2 := model.NewQuery[model.TShareReceiver](ctx).
		Join("inner join t_share_receiver_user on t_share_receiver.id = t_share_receiver_user.receiver_id").
		Where("t_share_receiver.admin_assistant_id = 0 AND t_share_receiver.admin_type = ?", adminType).
		Where("t_share_receiver.receiver_state = ?", 1).
		Where("t_share_receiver_user.state = ?", 3)
	if sender.Type == base.IdentityType_IDENTITY_TYPE_USER {
		q2.Where("t_share_receiver_user.user_id = ?", sender.ID)
	}
	if sender.Type == base.IdentityType_IDENTITY_TYPE_TEAM {
		q2.Where("t_share_receiver_user.team_id = ?", sender.ID)
	}
	q2.Select("t_share_receiver.create_by")

	// 执行查询获取被拒绝的团队ID列表
	var excludedTeamIDs []uint64
	err := q2.DB().Pluck("t_share_receiver.create_by", &excludedTeamIDs).Error
	if err != nil {
		return nil, err
	}

	// 第二步：查询 t_team_info 表，排除被拒绝的团队
	var query *gorm.DB
	query = model.NewConnectionV1(ctx).Table("t_team_info")
	query.Scopes(TeamStateCanShareDoc)
	query.Where(query.Session(&gorm.Session{NewDB: true}).
		Where("short_name like ? or full_name like ?", "%"+name+"%", "%"+name+"%"))
	// Or("short_name_en like ?", "%"+name+"%"))
	query.Select("id, short_name as name, short_name_en as name_en")

	// 如果有需要排除的团队ID，则添加 NOT IN 条件
	if len(excludedTeamIDs) > 0 {
		query.Where("id NOT IN (?)", excludedTeamIDs)
	}

	query.Offset(int(offset)).Limit(int(limit))

	type Row struct {
		Id     uint64 `gorm:"column:id"`
		Name   string `gorm:"column:name"`
		NameEn string `gorm:"column:name_en"`
	}
	mp := make([]*Row, 0, limit)
	err = query.Find(&mp).Error
	if err != nil {
		return nil, err
	}

	ret := make([]*aipb.DocShareReceiver, 0)
	for _, row := range mp {
		ret = append(ret, &aipb.DocShareReceiver{
			Id:        row.Id,
			Name:      row.Name,
			NameEn:    row.NameEn,
			ShareType: aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM,
		})
	}

	return ret, nil
}

// GetUserCanShareDoc 获取可以分享至的个人
func (d *DocShareConfigReceiverLogic) GetUserCanShareDoc(ctx context.Context,
	sender *DocShareSender, userId uint64,
) ([]*aipb.DocShareReceiver, error) {
	adminType := basepb.IdentityType_IDENTITY_TYPE_USER
	// q2为明确拒绝接收的
	q2 := model.NewQuery[model.TShareReceiver](ctx).
		Join("inner join t_share_receiver_user on t_share_receiver.id = t_share_receiver_user.receiver_id").
		Where("t_share_receiver.admin_assistant_id = 0 AND t_share_receiver.admin_type = ?", adminType).
		Where("t_share_receiver.receiver_state = ?", 1).
		Where("t_share_receiver_user.state = ?", 3)
	// 指定接收方个人 id
	q2.Where("t_share_receiver.create_by = ?", userId)

	if sender.Type == base.IdentityType_IDENTITY_TYPE_USER {
		q2.Where("t_share_receiver_user.user_id = ?", sender.ID)
	}
	if sender.Type == base.IdentityType_IDENTITY_TYPE_TEAM {
		q2.Where("t_share_receiver_user.team_id = ?", sender.ID)
	}
	cnt, err := q2.Count()
	if err != nil {
		return nil, err
	}
	if cnt != 0 {
		return nil, nil
	}
	ret := make([]*aipb.DocShareReceiver, 0)
	ret = append(ret, &aipb.DocShareReceiver{
		Id:        userId,
		ShareType: aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER,
	})
	return ret, nil
}

// GetSharedTeamList 获取已分享的团队列表，用于表头筛选
func GetSharedTeamList(ctx context.Context, contributor *aipb.Contributor,
	filterType aipb.ListDocFilterType, dataSource aipb.DocDataSource,
) ([]uint64, error) {
	return GetSharedReceiverList(ctx, contributor, filterType, dataSource, aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM)
}

// GetSharedReceiverList 获取已分享的接收者列表，支持动态接收者类型查询
func GetSharedReceiverList(ctx context.Context, contributor *aipb.Contributor,
	filterType aipb.ListDocFilterType, dataSource aipb.DocDataSource,
	receiverType aipb.DocShareReceiverType,
) ([]uint64, error) {
	// 构建主查询，检查是否存在已分享的文档
	mainQuery := model.NewQuery[model.TDocShare](ctx).DB().
		Where("share_type = ?", receiverType).
		Where("state = ?", DocStateWhensShareToUserTeam).
		Select("1")

	// 添加贡献者过滤条件
	if contributor != nil {
		contributorQuery := model.NewQuery[model.TDocContributor](ctx).DB().
			Select("1").
			Where("t_doc_share.doc_id = t_doc_contributor.doc_id")
		if contributor.Id != 0 {
			contributorQuery.Where("contributor_id = ? and contributor_type = ?", contributor.Id, contributor.Type)
		} else if len(contributor.Text) != 0 {
			contributorQuery.Where("contributor_text = ? and contributor_type = ?", contributor.Text, contributor.Type)
		}
		mainQuery = mainQuery.Where("EXISTS (?)", contributorQuery)
	}

	// 添加文档类型过滤
	docQuery := model.NewQuery[model.TDoc](ctx).DB().
		Select("1").
		Where("t_doc_share.doc_id = t_doc.id")
	if filterType != aipb.ListDocFilterType_LIST_DOC_FILTER_TYPE_UNSPECIFIED {
		switch filterType {
		case aipb.ListDocFilterType_LIST_DOC_FILTER_TYPE_QA:
			docQuery = docQuery.Where("data_type = ?", aipb.DocType_DOCTYPE_QA)
		case aipb.ListDocFilterType_LIST_DOC_FILTER_TYPE_TEXTFILE:
			docQuery = docQuery.Where("data_type in ?", []aipb.DocType{aipb.DocType_DOCTYPE_TEXT, aipb.DocType_DOCTYPE_FILE})
			// 添加数据源过滤
			if dataSource != aipb.DocDataSource_DOC_DATA_SOURCE_UNSPECIFIED {
				docQuery = docQuery.Where("data_source = ?", dataSource)
			}
		case aipb.ListDocFilterType_LIST_DOC_FILTER_TYPE_SYSTEM:
			docQuery.Where("data_source = ?", aipb.DocDataSource_DOC_DATA_SOURCE_UGC)
		}
	}
	mainQuery = mainQuery.Where("EXISTS (?)", docQuery)

	var receiverIds []uint64
	err := mainQuery.Select("target_id").Pluck("target_id", &receiverIds).Error
	if err != nil {
		return nil, err
	}

	return receiverIds, nil
}

// ShareDocToUserTeam 分享文档到个人和团队
func ShareDocToUserTeam(ctx context.Context, adminType basepb.IdentityType, createBy uint64, docIds []uint64, userIds []uint64, teamIds []uint64) error {
	if len(docIds) == 0 || (len(userIds) == 0 && len(teamIds) == 0) {
		return nil
	}

	sender := &DocShareSender{
		Type: adminType,
		ID:   createBy,
	}

	var receivers []*aipb.DocShareReceiver

	// 添加个人接收者
	for _, userID := range userIds {
		receivers = append(receivers, &aipb.DocShareReceiver{
			Id:        userID,
			ShareType: aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER,
		})
	}

	// 添加团队接收者
	for _, teamID := range teamIds {
		receivers = append(receivers, &aipb.DocShareReceiver{
			Id:        teamID,
			ShareType: aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM,
		})
	}

	return SyncDocShareUserTeam(ctx, sender, receivers, docIds)
}
