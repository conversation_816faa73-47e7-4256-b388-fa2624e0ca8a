package logic

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"code.sajari.com/docconv/v2"
	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	tablechunk "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk/table"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	taskClient "e.coding.net/tencent-ssv/tanlive/services/ai/internal/tasks/client"
	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot/microwrapper"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/metric"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/ratelimiter"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/asr"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/xcos"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"e.coding.net/tencent-ssv/tanlive/util/ocr"
	"github.com/go-redis/redis_rate/v10"
	"github.com/hibiken/asynq"
	pdfapi "github.com/pdfcpu/pdfcpu/pkg/api"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"

	"github.com/tencentyun/cos-go-sdk-v5"
)

const (
	DocFileTypeUnsupported = iota
	// DocFileTypeTxt txt文件
	DocFileTypeTxt
	// DocFileTypePdf pdf文件
	DocFileTypePdf
	// DocFileTypeDocx docx文档
	DocFileTypeDocx
	// DocFileTypePpt ppt/pptx文档
	DocFileTypePpt
	// DocFileTypeAudio 音频
	DocFileTypeAudio
	// DocFileTypeVideo 视频
	DocFileTypeVideo
	// DocFileTypePicture 图片
	DocFileTypePicture
	// DocFileTypeEpub 电子书
	DocFileTypeEpub
	// DocFileTypeXlsx excel
	DocFileTypeXlsx
	// DocFileTypeCsv csv
	DocFileTypeCsv
)

const (
	PicturePNG  = ".png"
	PictureJPG  = ".jpg"
	PictureJPEG = ".jpeg"
)

// extractFileExtension 安全地提取文件扩展名，支持包含特殊字符的文件名
func extractFileExtension(path string) string {
	// 首先移除查询参数
	if idx := strings.Index(path, "?"); idx != -1 {
		path = path[:idx]
	}

	// 使用filepath.Ext获取扩展名
	ext := filepath.Ext(path)
	return strings.ToLower(ext)
}

// DocFileTypeBySuffix 通过文件后缀判断文件类型
func DocFileTypeBySuffix(path string) (int, string) {
	suffix := extractFileExtension(path)
	switch suffix {
	case ".txt":
		return DocFileTypeTxt, suffix
	case ".pdf":
		return DocFileTypePdf, suffix
	case ".docx", ".doc":
		return DocFileTypeDocx, suffix
	case ".mp3", ".wav", ".m4a", ".wma", ".amr", ".aac", ".flac":
		return DocFileTypeAudio, suffix
	case ".mp4", ".3gp", ".flv":
		return DocFileTypeVideo, suffix
	case ".csv":
		return DocFileTypeCsv, suffix
	case ".epub":
		return DocFileTypeEpub, suffix
	case ".xlsx", ".xls":
		return DocFileTypeXlsx, suffix
	case ".ppt", ".pptx":
		return DocFileTypePpt, suffix
	case PicturePNG, PictureJPG, PictureJPEG:
		return DocFileTypePicture, suffix
	}
	return DocFileTypeUnsupported, suffix
}

// GetDocFileType 获取知识库文档类型
func GetDocFileType(ctx context.Context, doc *model.TDoc) (int, error) {
	if doc.Ref != nil {
		ft, _ := DocFileTypeBySuffix(doc.Ref.Url)
		return ft, nil
	}
	var tmpDoc model.TDoc
	err := model.NewQuery[model.TDoc](ctx).DB().Where("id = ?", doc.ID).Select("ref").Find(&tmpDoc).Error
	if err != nil {
		return 0, err
	}
	doc.Ref = tmpDoc.Ref
	if doc.Ref != nil {
		ft, _ := DocFileTypeBySuffix(doc.Ref.Url)
		return ft, nil
	}
	return 0, nil
}

// OcrIfRuneTooLess ocr如果读取的字符太少
func OcrIfRuneTooLess(runeCount int) bool {
	minSize := config.GetIntOr("llm.collection.doc_parse.ocr_if_rune_count_below", 300) // 300 字符
	return runeCount < minSize
}

// OcrIfPageRuneTooLess ocr如果读取的平均每一页字符太少
func OcrIfPageRuneTooLess(lang string, runeCount, page int) bool {
	// 避免除 0
	if page == 0 {
		page = 1
	}

	// 根据文本语言设置不同的阈值
	var minSize int
	if lang == "en" {
		minSize = config.GetIntOr("llm.collection.doc_parse.ocr_if_page_rune_count_below_en", 150)
	} else {
		minSize = config.GetIntOr("llm.collection.doc_parse.ocr_if_page_rune_count_below_zh", 80)
	}

	ratio := runeCount / page
	return ratio < minSize
}

// UsingFallbackLMParse 是否使用大模型作为兜底解析
func UsingFallbackLMParse(args ...bool) bool {
	for _, arg := range args { // 如果有入参不允许走大模型解析，则返回false
		if !arg {
			return false
		}
	}
	return config.GetBoolOr("llm.collection.parse.fallback_lm", true)
}

type DocParseProgress func(float32)

// DocParser 文档解析器
type DocParser interface {
	// Parse 先尝试本地解析，失败后继续使用url解析
	Parse(ctx context.Context, path string, url string, record DocParseProgress) (string, error)
}

// TxtParser 纯文本解析器，直接读区文本内容
// 支持多种编码格式
type TxtParser struct {
	mode *DocParserByMode
}

// Parse 解析
// 优先走tika，如果失败了则utf8编码读取
func (p *TxtParser) Parse(ctx context.Context, path string, url string, record DocParseProgress) (string, error) {
	if len(path) == 0 {
		return "", errors.New("TxtParser not support url")
	}
	if p.mode.DefaultModeParse(ctx, path, url) {
		return p.mode.GetModeParseResult()
	}
	_ = p.mode.modeParseFileByTika(path, false)
	result, err := p.mode.GetModeParseResult()
	if err != nil {
		log.WithContext(ctx).Errorw("modeParseFileByTika error", "err", err, "path", path)
		// content, err := os.ReadFile(path)
		// if err != nil {
		//	return "", err
		// }
		// content, _, err = xcharset.DecodeToUTF8(content)
		// if err != nil {
		//	return "", err
		// }
		// text := string(content)
		// return text, nil
		if UsingFallbackLMParse() {
			goto USELM
		}
	}
	return result, nil
USELM:
	return p.mode.modeParseByLLM(ctx, url).GetModeParseResult()
}

type DocxParser struct {
	mode *DocParserByMode
}

func (p *DocxParser) ParseFile(ctx context.Context, path string, record DocParseProgress) (string, error) {
	var docRsp *docconv.Response
	var err error
	if docRsp, err = docconv.ConvertPath(path); err != nil {
		return "", err
	}
	return onlyUtf8(docRsp.Body), nil
}

// Parse dox解析
// 1. 先使用tike解析
// 2. 如果tika解析失败，或者字符数太少，使用大模型
func (p *DocxParser) Parse(ctx context.Context, path string, url string, record DocParseProgress) (string, error) {
	if p.mode.DefaultModeParse(ctx, path, url) {
		return p.mode.GetModeParseResult()
	}
	// tika, page, err := ParseFileByTika(path, true)
	page := p.mode.modeParseFileByTika(path, true)
	tika, err := p.mode.GetModeParseResult()
	cleand := cleanText(tika)
	if err != nil {
		log.WithContext(ctx).Infof("DocxParser parse file using tika error: %v", err)
		if UsingFallbackLMParse() {
			goto USELM
		}
		return "", err
	}

	if OcrIfPageRuneTooLess(util.DetectLanguage(cleand), utf8.RuneCountInString(cleand), page) && UsingFallbackLMParse() {
		goto USELM
	}
	return tika, nil
USELM:
	return p.mode.modeParseByLLM(ctx, url).GetModeParseResult()
}

type PptParser struct {
	mode *DocParserByMode
}

// Parse dox解析
// 1. 先使用tike解析
// 2. 如果tika解析失败，或者字符数太少，使用大模型
func (p *PptParser) Parse(ctx context.Context, path string, url string, record DocParseProgress) (string, error) {
	if p.mode.DefaultModeParse(ctx, path, url) {
		return p.mode.GetModeParseResult()
	}
	// tika, page, err := ParseFileByTika(path, true)
	page := p.mode.modeParseFileByTika(path, true)
	tika, err := p.mode.GetModeParseResult()
	cleand := cleanText(tika)
	if err != nil {
		log.WithContext(ctx).Infof("PptParser parse file using tika error: %v", err)
		if UsingFallbackLMParse() {
			goto USELM
		}
		return "", err
	}
	if OcrIfPageRuneTooLess(util.DetectLanguage(cleand), utf8.RuneCountInString(cleand), page) && UsingFallbackLMParse() {
		goto USELM
	}
	return tika, nil
USELM:
	return p.mode.modeParseByLLM(ctx, url).GetModeParseResult()
}

type PdfParser struct {
	mode *DocParserByMode
}

func (p *PdfParser) Parse(ctx context.Context, path string, url string, record DocParseProgress) (string, error) {
	if p.mode.PdfModeParse(ctx, path, url, record) {
		return p.mode.GetModeParseResult()
	}
	// tika, page, err := ParseFileByTika(path, true)
	page := p.mode.modeParseFileByTika(path, true)
	tika, err := p.mode.GetModeParseResult()
	cleand := cleanText(tika)
	if err != nil {
		log.WithContext(ctx).Infof("PdfParser parse file using tika error: %v", err)
		goto USEOCR
	}
	if OcrIfPageRuneTooLess(util.DetectLanguage(cleand), utf8.RuneCountInString(cleand), page) && UsingFallbackLMParse() {
		goto USEOCR
	}
	return tika, nil

USEOCR:
	return p.mode.modeParsePdfByOcr(ctx, path, url, record, true).GetModeParseResult()
}

type AudioParser struct {
	mode *DocParserByMode
}

func (p *AudioParser) Parse(ctx context.Context, path string, url string, record DocParseProgress) (string, error) {
	if len(url) == 0 {
		return "", errors.New("AudioParser only support url")
	}
	if !p.mode.IsOnlyFileModeParse() {
		return p.mode.GetModeParseResult()
	}
	text, err := asr.DescribeRecTaskByUrl(ctx, "", url, 120)
	if err != nil {
		log.WithContext(ctx).Infof("VideoParser parse file using asr error: %v", err)
		return "", err
	}
	return text, nil
}

type VideoParser struct {
	mode *DocParserByMode
}

func (p *VideoParser) Parse(ctx context.Context, path string, url string, record DocParseProgress) (string, error) {
	if len(url) == 0 {
		return "", errors.New("VideoParser only support video url")
	}
	if !p.mode.IsOnlyFileModeParse() {
		return p.mode.GetModeParseResult()
	}
	text, err := asr.DescribeVideoRecTaskByUrl(ctx, "", url, 120)
	if err != nil {
		log.WithContext(ctx).Infof("VideoParser parse file using asr error: %v", err)
		return "", err
	}
	return text, nil
}

type PictureParser struct {
	PictureType string
	mode        *DocParserByMode
}

func (p *PictureParser) Parse(ctx context.Context, path string, url string, record DocParseProgress) (string, error) {
	if len(url) == 0 {
		return "", errors.New("PictureParser only support url")
	}
	if p.mode.PictureModeParse(ctx, url) {
		return p.mode.GetModeParseResult()
	}
	str, err := p.mode.modeParseImageByOcr(ctx, url).GetModeParseResult()
	if err != nil {
		log.WithContext(ctx).Infof("PictureParser parse file using ocr error: %v", err)
		if UsingFallbackLMParse() {
			goto USELM
		}
		return "", err
	}
	return str, nil
USELM:
	return p.mode.modeParseByLLM(ctx, url).GetModeParseResult()
}

type EpubParser struct {
	mode *DocParserByMode
}

// Parse EpubParser 走大模型不支持，暂时取消大模型
func (p *EpubParser) Parse(ctx context.Context, path string, url string, _ DocParseProgress) (string, error) {
	if len(path) == 0 {
		return "", errors.New("EpubParser only support local file path")
	}
	_ = p.mode.modeParseFileByTika(path, false)
	return p.mode.GetModeParseResult()
	/*page := p.mode.modeParseFileByTika(path, true)
		tika, err := p.mode.GetModeParseResult()
		cleand := cleanText(tika)
		if err != nil {
			log.WithContext(ctx).Infof("EpubParser parse file using tika error: %v", err)
			if UsingFallbackLMParse() {
				goto USELM
			}
		}
		if OcrIfPageRuneTooLess(util.DetectLanguage(cleand), utf8.RuneCountInString(cleand), page) && UsingFallbackLMParse() {
			goto USELM
		}
		return tika, err

	USELM:
		return p.mode.modeParseByLLM(ctx, url).GetModeParseResult()*/
}

type XlsxParser struct {
	mode *DocParserByMode
}

func (p *XlsxParser) Parse(ctx context.Context, path string, url string, record DocParseProgress) (string, error) {
	if len(path) == 0 {
		return "", errors.New("XlsxParser only support local file path")
	}
	if p.mode.DefaultModeParse(ctx, path, url) {
		return p.mode.GetModeParseResult()
	}

	_ = p.mode.modeParseFileByTika(path, false)
	tika, err := p.mode.GetModeParseResult()
	if err != nil {
		log.WithContext(ctx).Infof("XlsxParser parse file using tika error: %v", err)
		if UsingFallbackLMParse() {
			goto USELM
		}
		return "", err
	}
	// TODO： xlsx文件如果文字少，是否不走大模型
	// if OcrIfRuneTooLess(utf8.RuneCountInString(tika)) && UsingFallbackLMParse() {
	// 	goto USELM
	// }
	return tika, nil
USELM:
	return p.mode.modeParseByLLM(ctx, url).GetModeParseResult()
}

type CsvParser struct {
	mode *DocParserByMode
}

func (p *CsvParser) Parse(ctx context.Context, path string, url string, record DocParseProgress) (string, error) {
	if len(path) == 0 {
		return "", errors.New("CsvParser only support local file path")
	}
	if p.mode.DefaultModeParse(ctx, path, url) {
		return p.mode.GetModeParseResult()
	}

	_ = p.mode.modeParseFileByTika(path, false)
	tika, err := p.mode.GetModeParseResult()
	if err != nil {
		log.WithContext(ctx).Infof("CsvParser parse file using tika error: %v", err)
		if UsingFallbackLMParse() {
			goto USELM
		}
		return "", err
	}
	// TODO： csv文件如果文字少，是否不走大模型
	// if OcrIfRuneTooLess(utf8.RuneCountInString(tika)) && UsingFallbackLMParse() {
	// 	goto USELM
	// }
	return tika, nil
USELM:
	return p.mode.modeParseByLLM(ctx, url).GetModeParseResult()
}

// DoParseDoc 解析doc封装
// localPath 本地文件路径
// cosUrl cos的地址
// 如果只填了localPath，那么就读取本地文件进行解析
// 如果只填了cosUrl，那么就使用cos文件进行解析
// 如果都填了，会使用混合解析
func DoParseDoc(ctx context.Context, localPath string, cosUrl string,
	record DocParseProgress, extend *model.TDocExtend) (string, error) {
	var docType int
	var suffix string
	if len(localPath) != 0 {
		docType, suffix = DocFileTypeBySuffix(localPath)
	}
	if len(cosUrl) != 0 {
		docType, suffix = DocFileTypeBySuffix(cosUrl)
	}
	mode := &DocParserByMode{parseMode: extend.ParseMode}

	var parser DocParser

	switch docType {
	case DocFileTypeTxt:
		parser = &TxtParser{mode: mode}
	case DocFileTypeEpub:
		parser = &EpubParser{mode: mode}
	case DocFileTypeXlsx:
		parser = &XlsxParser{mode: mode}
	case DocFileTypeCsv:
		parser = &CsvParser{mode: mode}
	case DocFileTypePdf:
		parser = &PdfParser{mode: mode}
	case DocFileTypeDocx:
		parser = &DocxParser{mode: mode}
	case DocFileTypeAudio:
		parser = &AudioParser{mode: mode}
	case DocFileTypeVideo:
		parser = &VideoParser{mode: mode}
	case DocFileTypePicture:
		parser = &PictureParser{PictureType: suffix, mode: mode}
	case DocFileTypePpt:
		parser = &PptParser{mode: mode}
	default:
		return "", fmt.Errorf("doc file type not support: %s", suffix)
	}
	parse, err := parser.Parse(ctx, localPath, cosUrl, record)
	extend.ParseMode = mode.GetParseMode()

	return parse, err
}

const (
	// DocParseLimiterKeyGeneralOcrPdf 通用ocr识别
	DocParseLimiterKeyGeneralOcrPdf = "GeneralOcrPdf"
	// DocParseLimiterKeyLmOcrPdf 大模型ocr识别
	DocParseLimiterKeyLmOcrPdf = "LmOcr"
)

var (
	docParseLimiter     *DocParseLimiterManager
	docParseLimiterOnce sync.Once
)

type DocParseLimiterManager struct {
	limits map[string]redis_rate.Limit
}

// GetDocParseLimiterManager 获取限流器
func GetDocParseLimiterManager() *DocParseLimiterManager {
	docParseLimiterOnce.Do(func() {
		docParseLimiter = &DocParseLimiterManager{
			limits: make(map[string]redis_rate.Limit),
		}
		docParseLimiter.limits[DocParseLimiterKeyGeneralOcrPdf] = redis_rate.PerSecond(config.GetIntOr("llm.collection.doc_parse.general_ocr_limit", 1))
		docParseLimiter.limits[DocParseLimiterKeyLmOcrPdf] = redis_rate.PerSecond(config.GetIntOr("llm.collection.doc_parse.lm_ocr_limit", 1))
	})
	return docParseLimiter
}

func (h *DocParseLimiterManager) genRedisKey(key string) string {
	return fmt.Sprintf("ai:%s:limit", key)
}

// WaitAllow 等待限流器允许
func (h *DocParseLimiterManager) WaitAllow(ctx context.Context, key string) error {
	for {
		r, err := ratelimiter.DefaultRateLimiter.Allow(ctx, h.genRedisKey(key), h.limits[key])
		if err != nil {
			return err
		}
		if r.Allowed != 0 {
			return nil
		}
		time.Sleep(time.Millisecond * 100)
	}
}

// OcrHelper AI ocr接口速率限制
type OcrHelper struct {
	fileParallelism chan struct{}
}

var (
	OcrInstance *OcrHelper
	onceOcr     sync.Once
)

// GetOcrHelper 返回单例的实例
func GetOcrHelper() *OcrHelper {
	onceOcr.Do(func() {
		OcrInstance = &OcrHelper{}

		OcrInstance.fileParallelism = make(chan struct{}, config.GetIntOr("llm.collection.ocr_limit", 1))
	})
	return OcrInstance
}

// OcrPdf 解析pdf
// 如果传入了cosUrl，在ocr识别出错之后，会使用大模型解析
func (o *OcrHelper) OcrPdf(ctx context.Context, path, cosUrl string,
	record DocParseProgress, useLLM, parseTable bool) (string, ai.DocParseMode, error) {
	return new(PdfFileHelper).OcrFile(ctx, path, cosUrl, record, useLLM, parseTable)
}

// OcrPictureUrl 解析图片,从url读区
func (o *OcrHelper) OcrPictureUrl(ctx context.Context, cosUrl string) (string, error) {
	u, _ := url.Parse(cosUrl)
	jpg, err := new(CosHelper).ImageSizeLimit(ctx, u.Path)
	if err != nil {
		return "", err
	}
	signedUrl, err := new(CosHelper).GenCosSignedUrl(ctx, jpg)
	if err != nil {
		return "", err
	}
	err = GetDocParseLimiterManager().WaitAllow(ctx, DocParseLimiterKeyGeneralOcrPdf)
	if err != nil {
		// 不报错继续执行
		log.WithContext(ctx).Warnf("OcrPicture wait ratelimiter err:%v", err.Error())
	}

	return ocr.GteInstance().File2TextWithUrl(ctx, signedUrl, false, 1)
}

// ReconstructFileUrlLM 大模型解析文档
// 支持的文档类型见 https://cloud.tencent.com/document/product/1759/107506
func (o *OcrHelper) ReconstructFileUrlLM(ctx context.Context, url string) (string, error) {
	err := GetDocParseLimiterManager().WaitAllow(ctx, DocParseLimiterKeyLmOcrPdf)
	if err != nil {
		log.WithContext(ctx).Warnf("ReconstructFileUrlLM wait ratelimiter err:%v", err.Error())
	}
	return ocr.GteInstance().File2MarkdownStrWithUrlLm(ctx, url, &ocr.File2MarkdownOption{
		StartPageNumber: 1,
		EndPageNumber:   1000,
	})
}

// PdfFileHelper pdf文件工具包装
type PdfFileHelper struct {
	MaxSize int64 // 文件的最大大小 单位:byte
}

// IsLargeFile 是否为大文件
func (f *PdfFileHelper) IsLargeFile(localPath string) (bool, error) {
	if f.MaxSize == 0 {
		f.MaxSize = int64(5 * 1000 * 1024) // 5MB in bytes，经base64编码后约为7MB
	}
	file, err := os.Open(localPath)
	if err != nil {
		if os.IsNotExist(err) {
			return false, fmt.Errorf("file %s does not exist", localPath)
		}
		return false, err
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		return false, err
	}

	return fileInfo.Size() > f.MaxSize, nil
}

// SplitFile 切割文件，每一页产生新的pdf文件
// forceSplit 强制分割，无论文件是否为大文件
func (f *PdfFileHelper) SplitFile(localPath string, span int, forceSplit bool) (string, []string, error) {
	var large bool
	var err error
	var outDir string

	if !forceSplit {
		large, err = f.IsLargeFile(localPath)
		if err != nil {
			return "", nil, err
		}
	}
	paths := make([]string, 0)
	if forceSplit || large {
		root := filepath.Dir(localPath)
		outDir, err = os.MkdirTemp(root, fileSplitPattern)
		if err != nil {
			return "", nil, err
		}
		err = pdfapi.SplitFile(localPath, outDir, span, nil)
		if err != nil {
			return outDir, nil, err
		}
		err = filepath.Walk(outDir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				fmt.Printf("Error accessing path %q: %v\n", path, err)
				return err
			}
			if !info.IsDir() && filepath.Ext(path) == ".pdf" {
				paths = append(paths, path)
			}
			return nil
		})
		if err != nil {
			return outDir, nil, err
		}

		extractPno := func(path string) int {
			parts := strings.Split(strings.TrimSuffix(filepath.Base(path), ".pdf"), "_")
			numStr := parts[len(parts)-1]
			num, _ := strconv.Atoi(numStr)
			return num
		}
		sort.Slice(paths, func(i, j int) bool {
			return extractPno(paths[i]) < extractPno(paths[j])
		})
	} else {
		paths = append(paths, localPath)
	}
	return outDir, paths, nil
}

// OcrFile 获取ocr内容
func (f *PdfFileHelper) OcrFile(ctx context.Context, filePath, cosUrl string,
	recordParseProgress func(float32), useLLM, parseTable bool) (string, ai.DocParseMode, error) {
	var text bytes.Buffer
	var chunkText string
	var err error
	page := 1

	tmpDir, files, err := f.SplitFile(filePath, 1, true)
	// 清除产生的临时文件
	defer func() {
		if tmpDir != "" {
			os.RemoveAll(tmpDir)
		}
	}()
	if err != nil {
		err = fmt.Errorf("pdf split file failed: %w", err)
		if len(cosUrl) == 0 {
			return "", ai.DocParseMode_DOC_PARSE_MODE_IMAGE, err
		}
		if UsingFallbackLMParse(useLLM) {
			goto USELM
		}
		return "", ai.DocParseMode_DOC_PARSE_MODE_IMAGE, err
	}

	for i, file := range files {
		err = GetDocParseLimiterManager().WaitAllow(ctx, DocParseLimiterKeyGeneralOcrPdf)
		if err != nil {
			log.WithContext(ctx).Warnf("OcrPdf wait ratelimiter err:%v", err.Error())
			// return "", fmt.Errorf("OcrPdf wait ratelimiter err:%w", err)
		}

		if parseTable {
			chunkText, err = ocr.GteInstance().File2TableMarkdown(ctx, file, "", 0)
		} else {
			chunkText, err = ocr.GteInstance().PDF2Text(ctx, file)
		}
		if err != nil {
			err = fmt.Errorf("ocr file call PDF2Text failed: %w", err)
			if len(cosUrl) == 0 {
				return "", ai.DocParseMode_DOC_PARSE_MODE_IMAGE, err
			}
			if UsingFallbackLMParse(useLLM) {
				goto USELM
			}
			return "", ai.DocParseMode_DOC_PARSE_MODE_IMAGE, err
		}
		// 更新进度
		// 将当前文件的进度按比例计算，并通知进度记录函数
		progress := float32(i+1) / float32(len(files))
		recordParseProgress(progress)
		text.WriteString(chunkText)
		page++
	}
	return text.String(), ai.DocParseMode_DOC_PARSE_MODE_IMAGE, nil

USELM:
	err = GetDocParseLimiterManager().WaitAllow(ctx, DocParseLimiterKeyLmOcrPdf)
	if err != nil {
		log.WithContext(ctx).Warnf("OcrLm wait ratelimiter err:%v", err.Error())
	}
	// 使用大模型解析
	chunkText, err = ocr.GteInstance().File2MarkdownStrWithUrlLm(ctx, cosUrl, &ocr.File2MarkdownOption{
		StartPageNumber: int64(page),
		EndPageNumber:   1000, // 设置最大1000页
	})
	if err != nil {
		return "", ai.DocParseMode_DOC_PARSE_MODE_SMART, fmt.Errorf("ocr file call File2MarkdownStrWithUrlLm: %w", err)
	}
	text.WriteString(chunkText)
	return text.String(), ai.DocParseMode_DOC_PARSE_MODE_SMART, nil
}

// RegRemoveMdImages 移除md图片标签
var RegRemoveMdImages = regexp.MustCompile(`!\[.*?]\(.*?\)`)

func RemoveMarkdownImages(input string) string {
	// 匹配 Markdown 图片的正则表达式
	return RegRemoveMdImages.ReplaceAllString(input, "")
}

const AiParseDocTask = "ai:parse_doc"

// AiParseDocTaskPayload 解析任务的结构
type AiParseDocTaskPayload struct {
	model.TDoc
	Ctx     DocParseTaskCtx `json:"ctx"`
	Reparse bool            `json:"reparse"`
}

// DocParseTaskCtx ...
type DocParseTaskCtx struct {
	CreateBy     uint64            `json:"create_by"`
	CreateByType base.IdentityType `json:"create_by_type"`
	CreateAt     time.Time         `json:"create_at"`
	TraceId      string            `json:"trace-id,omitempty"`
}

// ParseDocLogic 解析doc逻辑
type ParseDocLogic struct {
	reparse bool
}

// NewParseDocLogic ...
func NewParseDocLogic() *ParseDocLogic {
	return &ParseDocLogic{}
}

// NewReparseDocLogic ...
func NewReparseDocLogic() *ParseDocLogic {
	return &ParseDocLogic{reparse: true}
}

// StartParse 提交解析任务
func (l *ParseDocLogic) StartParse(ctx context.Context, doc *model.TDoc) {
	task := NewParseDocTask(ctx, doc, l.reparse)
	// TODO: 先设置一个较大的超时时间？
	err := taskClient.SubmitTask(task, asynq.Timeout(time.Hour*5), asynq.MaxRetry(0))
	if err != nil {
		log.WithContext(ctx).Error("submit doc parse task err:%v", err, "task", task)
		NewDocFileManager(ctx, doc).markDocParseFailed(err)
	}
}

// NewParseDocTask 新建任务对象
func NewParseDocTask(ctx context.Context, doc *model.TDoc, reparse bool) *asynq.Task {
	taskCtx := DocParseTaskCtx{
		CreateBy:     doc.CreateBy,
		CreateByType: doc.CreateByType,
		CreateAt:     time.Now(),
		TraceId:      trace.SpanFromContext(ctx).SpanContext().TraceID().String(),
	}
	if taskCtx.CreateBy == 0 {
		taskCtx.CreateBy = doc.UpdateBy
		taskCtx.CreateByType = doc.UpdateByType
	}
	task := &AiParseDocTaskPayload{
		TDoc: model.TDoc{
			ID: doc.ID, Ref: doc.Ref, DataType: doc.DataType, DocExtend: doc.DocExtend,
		},
		Ctx:     taskCtx,
		Reparse: reparse,
	}
	payload, _ := json.Marshal(task)
	return asynq.NewTask(AiParseDocTask, payload)
}

// HandleParseDocTask 解析任务处理逻辑
func HandleParseDocTask(ctx context.Context, t *asynq.Task) error {
	var p AiParseDocTaskPayload
	if err := json.Unmarshal(t.Payload(), &p); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
	}
	var span trace.Span
	ctx, span = microwrapper.StartSpanFromContext(ctx, nil, "")
	traceID, _ := trace.TraceIDFromHex(p.Ctx.TraceId)
	spanCtx := trace.NewSpanContext(trace.SpanContextConfig{
		TraceID: traceID,
		SpanID:  trace.SpanID{}, // 新生成一个SpanID
		Remote:  true,
	})
	ctx = trace.ContextWithSpanContext(ctx, spanCtx)
	defer span.End()
	log.WithContext(ctx).Infof("Parsing doc: doc_id=%d, ref=%v,extend=%v", p.ID, p.Ref, p.DocExtend)
	ctx = metric.ContextWithApiIdentity(ctx, &base.Identity{
		IdentityType: p.Ctx.CreateByType,
		IdentityId:   p.Ctx.CreateBy,
	})
	f := &DocFileManager{
		ctx: ctx,
		doc: &p.TDoc,
	}
	defer f.Clean()
	err := f.ParseDocToDB(ParseDocParam{p.Reparse, false})
	if err != nil {
		return err
	}

	if p.Reparse { // 如果是重新解析，则需要对解析完成之后 处于启用状态到数据同步向量状态
		docs, err := model.LoadDoc(ctx, []uint64{p.TDoc.ID}, nil)
		if err != nil {
			return err
		}

		return model.Transaction(ctx, func(tx *gorm.DB) error {
			_ = model.DeleteDocChunks(tx, p.TDoc.ID, nil)
			return SyncDocToDBLogBulk(ctx, tx, nil, docs)
		})
	}

	// 解析完成后，进行表格提取
	xsync.Go(context.Background(), func(ctx context.Context) error {
		doc, err := model.NewQuery[model.TDoc](ctx).
			Where("id = ? and state <> ?", p.TDoc.ID, ai.DocState_DOC_STATE_PARES_FAILED).
			Select("id").
			Find()
		if err != nil {
			log.WithContext(ctx).Errorf("doc tabele chunk db error:%v", err)
		}
		if doc == nil || doc.ID == 0 {
			return nil
		}
		docTableLogic := tablechunk.NewDocTableLogic(model.NewConnection(ctx))
		if err := docTableLogic.StoreOversizedTablesAllAssistant(ctx, doc.ID); err != nil {
			log.WithContext(ctx).Errorf("StoreOversizedTablesAllAssistant failed. docId: %v, err: %v", doc.ID, err.Error())
			return err
		}
		return nil
	}, boot.TraceGo(ctx))
	return nil
}

type CosHelper struct{}

// GenCosSignedUrl cos路径生成url链接
func (h *CosHelper) GenCosSignedUrl(ctx context.Context, path string) (string, error) {
	singed, err := GetAiDocCosClient().Object.GetPresignedURL2(ctx, http.MethodGet, path, time.Hour*24, nil)
	if err != nil {
		return "", err
	}
	return singed.String(), nil
}

// ImageSizeLimit 生成cos转换后的图片
func (h *CosHelper) ImageSizeLimit(ctx context.Context, key string) (string, error) {
	key = strings.TrimPrefix(key, "/")
	newPath := strings.TrimSuffix(key, filepath.Ext(key)) + "_l7m" + ".jpg"
	_, _, err := GetAiDocCosClient().CI.ImageProcess(ctx, key, &cos.ImageProcessOptions{
		Rules: []cos.PicOperationsRules{
			{
				FileId: newPath,
				Rule:   "imageMogr2/strip/format/jpg/size-limit/7m!",
			},
		},
	})
	if err != nil {
		return "", fmt.Errorf("GetAiDocCosClient().ImageProcess err:%w", err)
	}
	return newPath, nil
}

// GetFile 获取cos文件
func (h *CosHelper) GetFile(ctx context.Context, path, localPath string) error {
	rsp, err := GetAiDocCosClient().Object.GetToFile(ctx, path, localPath, nil)
	if err != nil {
		return err
	}
	if rsp.StatusCode != http.StatusOK {
		return fmt.Errorf("get cos file failed: status code %d", rsp.StatusCode)
	}
	return nil
}

// GetAiDocCosClient 获取知识存储的cos客户端
func GetAiDocCosClient() *cos.Client {
	return xcos.Client("public")
}
