package logic

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"golang.org/x/sync/errgroup"
)

// ShareDocAfterCreate 创建后分享知识
func ShareDocAfterCreate(ctx context.Context, adminType basepb.IdentityType, createBy uint64,
	docIds []uint64, customShareToAssistant ...[]uint64,
) error {
	// 分享给助手
	err := ShareDocToAssistantAfterCreate(ctx, adminType, createBy, docIds, customShareToAssistant...)
	if err != nil {
		return err
	}

	// 分享给个人和团队
	err = ShareDocToUserTeamAfterCreate(ctx, adminType, createBy, docIds)
	if err != nil {
		return err
	}
	return nil
}

// ShareDocToUserTeamAfterCreate 创建后分享知识给个人和团队(咱不支持自定义的分享个人或者团队设置)
func ShareDocToUserTeamAfterCreate(ctx context.Context, adminType basepb.IdentityType, createBy uint64, docIds []uint64) error {
	// 获取默认分享设置
	shareSender, err := model.NewQuery[model.TShareSender](ctx).
		Where("admin_type = ? and  create_by = ?", adminType, createBy).Get()
	if err != nil {
		return err
	}
	if len(shareSender) == 0 {
		return nil
	}
	var shareUser, shareTeam []uint64
	for _, v := range shareSender {
		if v.ReceiverType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER {
			shareUser = append(shareUser, v.ReceiverID)
		}
		if v.ReceiverType == aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM {
			shareTeam = append(shareTeam, v.ReceiverID)
		}
	}
	if len(shareUser) == 0 && len(shareTeam) == 0 {
		return nil
	}

	err = ShareDocToUserTeam(ctx, adminType, createBy, docIds, shareUser, shareTeam)
	if err != nil {
		return err
	}
	return nil
}

// ShareDocToAssistantAfterCreate 创建后分享知识（支持助手、个人、团队）
func ShareDocToAssistantAfterCreate(ctx context.Context, adminType basepb.IdentityType, createBy uint64, docIds []uint64, customShare ...[]uint64) error {
	// 获取默认分享的助手列表
	listToShare, err := GetCreateDocShareAssistantList(ctx, adminType, createBy)
	if err != nil {
		log.WithContext(ctx).Errorf("ShareDocAfterCreate err: %s", err.Error())
		return err
	}
	if len(listToShare) == 0 && len(customShare) == 0 {
		return nil
	}

	wg := errgroup.Group{}
	for i, v := range docIds {
		var share []uint64
		if len(customShare) != 0 && len(customShare[i]) > 0 {
			share = customShare[i]
		} else {
			share = listToShare
		}
		if len(share) == 0 {
			continue
		}
		v := v
		wg.Go(func() error {
			err := SyncDocShareAssistant(ctx, adminType, createBy, []uint64{v}, share)
			if err != nil {
				return err
			}
			return nil
		})
	}
	return wg.Wait()
}

// ShareDocAfterCreateOp 创建后分享知识-用于运营端
func ShareDocAfterCreateOp(ctx context.Context, docId []uint64, customShare ...[]uint64) error {
	return ShareDocAfterCreate(ctx, basepb.IdentityType_IDENTITY_TYPE_TEAM, config.GetUint64("ai.share.team_id"),
		docId, customShare...)
}

// DoCreateDocShare 创建文档分享至助手、个人、团队
func DoCreateDocShare(ctx context.Context, req *aipb.ReqCreateDocShare) error {
	if req.DocId != 0 {
		req.DocIds = append(req.DocIds, req.DocId)
	}

	if req.IsMgmt {
		// 运营端操作，以贡献者的身份处理
		docContributors, err := model.NewQuery[model.TDocContributor](ctx).Where("doc_id = ?", req.DocId).Get()
		if err != nil {
			return err
		}
		for _, docContributor := range docContributors {
			sender := &DocShareSender{
				Type: basepb.IdentityType(docContributor.ContributorType),
				ID:   docContributor.ContributorID,
			}
			if err := doCreateDocShareInternal(ctx, sender, req); err != nil {
				return err
			}
		}
	} else {
		sender := &DocShareSender{
			Type: req.AdminType,
			ID:   req.CreateBy,
		}
		if err := doCreateDocShareInternal(ctx, sender, req); err != nil {
			return err
		}
	}
	return nil
}

// doCreateDocShareInternal 内部分享逻辑
func doCreateDocShareInternal(ctx context.Context, sender *DocShareSender, req *aipb.ReqCreateDocShare) error {
	// 分享给助手
	// if len(req.AssistantId) > 0 {
	// 先不判断
	if err := SyncDocShareAssistant(ctx, sender.Type, sender.ID, req.DocIds, req.AssistantId); err != nil {
		return err
	}
	// }

	// 分享给个人和团队
	// if len(req.UserId) > 0 || len(req.TeamId) > 0 {
	var receivers []*aipb.DocShareReceiver

	// 添加个人接收者
	for _, userID := range req.UserId {
		receivers = append(receivers, &aipb.DocShareReceiver{
			Id:        userID,
			ShareType: aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER,
		})
	}

	// 添加团队接收者
	for _, teamID := range req.TeamId {
		receivers = append(receivers, &aipb.DocShareReceiver{
			Id:        teamID,
			ShareType: aipb.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM,
		})
	}

	if err := SyncDocShareUserTeam(ctx, sender, receivers, req.DocIds); err != nil {
		return err
	}
	// }

	return nil
}
