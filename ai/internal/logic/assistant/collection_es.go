package assistant

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xes"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/helper"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/typedapi/indices/create"
	"github.com/elastic/go-elasticsearch/v8/typedapi/indices/updatealiases"
	"github.com/elastic/go-elasticsearch/v8/typedapi/types"
	"github.com/go-redsync/redsync/v4"
)

var (
	boolTrue  = true
	boolFalse = false
	asterisk  = "*"
)

// InitCollectionIndex 初始化知识库索引
func InitCollectionIndex(ctx context.Context, collection *model.TCollection, newDest bool) error {
	if collection == nil {
		return nil
	}
	saveCollection := func(collection *model.TCollection, changes []string) error {
		if len(changes) == 0 {
			return nil
		}
		return model.NewQuery[model.TCollection](ctx).Select(changes).Save(collection)
	}

	// 使用分布式锁，避免重复生成
	lockKey := "initCollectionIndex:" + strconv.FormatUint(collection.ID, 10)
	if newDest {
		lockKey += ":dest"
	}
	mu := helper.NewDistributedLock(lockKey, redsync.WithExpiry(10*time.Second))
	if err := mu.TryLockContext(ctx); err != nil {
		return fmt.Errorf("new lock: %w", err)
	}
	defer mu.UnlockContext(ctx)

	// 创建SynonymsSet
	if collection.SynonymsSet == "" {
		collection.SynonymsSet = NewSynonymsSetName(collection.RagName)
		if err := saveCollection(collection, []string{model.TCollectionColumns.SynonymsSet}); err != nil {
			return fmt.Errorf("save synonyms_set: %w", err)
		}
	}
	if synonymsSetExist, err := isEsSynonymsSetExist(ctx, collection.SynonymsSet); err != nil {
		return fmt.Errorf("check synonyms set exist: %v", err)
	} else if !synonymsSetExist {
		if err := createEsSynonymsSet(ctx, collection.SynonymsSet, nil); err != nil {
			return fmt.Errorf("create synonyms set: %v", err)
		}
	}

	// 创建索引（如果是新建reindex的dest，不需要保存到数据库）
	if !newDest && collection.IndexName == "" {
		collection.IndexName = NewIndexName(collection.RagName)
		if err := saveCollection(collection, []string{model.TCollectionColumns.IndexName}); err != nil {
			return fmt.Errorf("save index_name: %w", err)
		}
	}
	if collection.IndexName != "" {
		if indexExist, err := IsEsIndexExist(ctx, collection.IndexName); err != nil {
			return fmt.Errorf("check index exist: %v", err)
		} else if !indexExist {
			if err := createEsIndex(ctx, collection.IndexName, collection.SynonymsSet); err != nil {
				return fmt.Errorf("create index: %v", err)
			}
		}
	}

	// 创建别名
	if err := CreateEsAlias(ctx, collection.IndexName, collection.RagName); err != nil {
		return fmt.Errorf("create alias: %v", err)
	}

	collection.IndexCreated = true
	if err := saveCollection(collection, []string{model.TCollectionColumns.IndexCreated}); err != nil {
		return fmt.Errorf("save index_created: %w", err)
	}

	return nil
}

func esClient() *elasticsearch.TypedClient {
	return xes.UseTyped("rag")
}

// IsEsIndexExist 判断索引是否存在
func IsEsIndexExist(ctx context.Context, index string) (bool, error) {
	return esClient().Indices.Exists(index).Do(ctx)
}

func isEsSynonymsSetExist(ctx context.Context, id string) (bool, error) {
	_, err := esClient().Synonyms.GetSynonym(id).Do(ctx)
	if err != nil {
		if eserr, ok := err.(*types.ElasticsearchError); ok && eserr.Status == http.StatusNotFound {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// 创建同义词库
func createEsSynonymsSet(ctx context.Context, id string, rules []string) error {
	synonymRules := make([]types.SynonymRule, 0, len(rules)+1)
	// 默认插入一条规则，避免rules为空的情况
	synonymRules = append(synonymRules, types.SynonymRule{
		Synonyms: "Tencent, 腾讯, 腾讯科技",
	})
	for i, rule := range rules {
		synonymRules[i] = types.SynonymRule{
			Synonyms: rule,
		}
	}
	_, err := esClient().Synonyms.PutSynonym(id).SynonymsSet(synonymRules...).Do(ctx)
	return err
}

// 创建索引
func createEsIndex(ctx context.Context, indexName, synonymsSet string) error {
	_, err := esClient().Indices.Create(indexName).Request(&create.Request{
		Settings: &types.IndexSettings{
			NumberOfShards:   config.GetStringOr("rag.es.numberOfShards", "1"),
			NumberOfReplicas: config.GetStringOr("rag.es.numberOfReplicas", "1"),
			Analysis: &types.IndexSettingsAnalysis{
				Filter: map[string]types.TokenFilter{
					"synonyms_filter": types.SynonymGraphTokenFilter{
						SynonymsSet: &synonymsSet,
						Updateable:  &boolTrue,
					},
				},
				Analyzer: map[string]types.Analyzer{
					"default": map[string]any{
						"type":   "ik_max_word",
						"filter": []string{"synonyms_filter"},
					},
					"default_search": map[string]any{
						"type":   "ik_smart",
						"filter": []string{"synonyms_filter"},
					},
				},
			},
		},
	}).Do(ctx)
	return err
}

// CreateEsAlias 创建索引别名
func CreateEsAlias(ctx context.Context, index, alias string) error {
	_, err := esClient().Indices.UpdateAliases().Request(&updatealiases.Request{
		Actions: []types.IndicesAction{
			{
				Remove: &types.RemoveAction{
					Alias: &alias,
					Index: &asterisk,
				},
			},
			{
				Add: &types.AddAction{
					Alias: &alias,
					Index: &index,
				},
			},
		},
	}).Do(ctx)
	return err
}

// CreateEsReindexTask 创建重新索引任务
func CreateEsReindexTask(ctx context.Context, source, dest string) (string, error) {
	rsp, err := esClient().Reindex().
		Source(&types.ReindexSource{
			Index: []string{source},
		}).
		Dest(&types.ReindexDestination{
			Index: dest,
		}).
		WaitForCompletion(false).
		Do(ctx)
	if err != nil {
		return "", err
	}

	var taskID string
	switch value := rsp.Task.(type) {
	case int:
		taskID = strconv.Itoa(value)
	case string:
		taskID = value
	}
	return taskID, nil
}

// PollReindexTaskResultUntilCompleted 轮询重建索引任务结果
func PollReindexTaskResultUntilCompleted(ctx context.Context, taskID string, maxErrs int, retryAfter time.Duration) error {
	errCnt := 0
	if retryAfter <= 0 {
		retryAfter = 10 * time.Second
	}

	for {
		rsp, err := esClient().Tasks.Get(taskID).Do(ctx)
		if err != nil {
			if errCnt++; errCnt >= maxErrs {
				return fmt.Errorf("check reindex task status: %d errors occurred, last error: %w", errCnt, err)
			}
		} else if rsp.Completed {
			return nil
		}

		time.Sleep(retryAfter)
	}
}

// DeleteEsIndex 删除索引
func DeleteEsIndex(ctx context.Context, index string) error {
	_, err := esClient().Indices.Delete(index).Do(ctx)
	return err
}
