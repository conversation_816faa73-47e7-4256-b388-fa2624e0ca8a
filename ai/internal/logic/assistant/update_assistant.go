package assistant

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"maps"
	"reflect"
	"slices"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/validation"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 允许修改的基础配置字段
var baseWritableColumns = map[basepb.IdentityType]map[string]bool{
	basepb.IdentityType_IDENTITY_TYPE_USER: baseWritableColumnsInConsole,
	basepb.IdentityType_IDENTITY_TYPE_TEAM: baseWritableColumnsInConsole,
	basepb.IdentityType_IDENTITY_TYPE_MGMT: baseWritableColumnsInMgmt,
}

// 允许修改的微信渠道字段
var weixinChannelWritableColumns = map[basepb.IdentityType]map[string]bool{
	basepb.IdentityType_IDENTITY_TYPE_USER: weixinChannelWritableColumnsInConsole,
	basepb.IdentityType_IDENTITY_TYPE_TEAM: weixinChannelWritableColumnsInConsole,
	basepb.IdentityType_IDENTITY_TYPE_MGMT: weixinChannelWritableColumnsInMgmt,
}

// 允许修改的碳LIVE Web渠道字段
var tanliveWebChannelWritableColumns = map[basepb.IdentityType]map[string]bool{
	basepb.IdentityType_IDENTITY_TYPE_USER: tanliveWebChannelWritableColumnsInConsole,
	basepb.IdentityType_IDENTITY_TYPE_TEAM: tanliveWebChannelWritableColumnsInConsole,
	basepb.IdentityType_IDENTITY_TYPE_MGMT: tanliveWebChannelWritableColumnsInMgmt,
}

// 允许修改的碳LIVE应用渠道字段
var tanliveAppChannelWritableColumns = map[basepb.IdentityType]map[string]bool{
	basepb.IdentityType_IDENTITY_TYPE_USER: tanliveAppChannelWritableColumnsInConsole,
	basepb.IdentityType_IDENTITY_TYPE_TEAM: tanliveAppChannelWritableColumnsInConsole,
	basepb.IdentityType_IDENTITY_TYPE_MGMT: tanliveAppChannelWritableColumnsInMgmt,
}

// 允许修改的WhatsApp渠道字段
var whatsappChannelWritableColumns = map[basepb.IdentityType]map[string]bool{
	basepb.IdentityType_IDENTITY_TYPE_USER: whatsappChannelWritableColumnsInConsole,
	basepb.IdentityType_IDENTITY_TYPE_TEAM: whatsappChannelWritableColumnsInConsole,
	basepb.IdentityType_IDENTITY_TYPE_MGMT: whatsappChannelWritableColumnsInMgmt,
}

// 允许修改的小程序渠道字段
var miniprogramChannelWritableColumns = map[basepb.IdentityType]map[string]bool{
	basepb.IdentityType_IDENTITY_TYPE_USER: miniprogramChannelWritableColumnsInConsole,
	basepb.IdentityType_IDENTITY_TYPE_TEAM: miniprogramChannelWritableColumnsInConsole,
	basepb.IdentityType_IDENTITY_TYPE_MGMT: miniprogramChannelWritableColumnsInMgmt,
}

// 门户端用户允许修改的基础配置字段
var baseWritableColumnsInConsole = map[string]bool{
	model.TAssistantColumns.Name:                  true,
	model.TAssistantColumns.NameEn:                true,
	model.TAssistantColumns.Enabled:               true,
	model.TAssistantColumns.PromptPrefix:          true,
	model.TAssistantColumns.Model:                 true,
	model.TAssistantColumns.HistoryRounds:         true,
	model.TAssistantColumns.CloseSearch:           true,
	model.TAssistantColumns.SearchEngine:          true,
	model.TAssistantColumns.MissReply:             true,
	model.TAssistantColumns.SearchTopN:            true,
	model.TAssistantColumns.ChatOrSqlConfig:       true,
	model.TAssistantColumns.AskSuggestionConfig:   true,
	model.TAssistantColumns.TextWeight:            true,
	model.TAssistantColumns.BriefIntro:            true,
	model.TAssistantColumns.DetailIntro:           true,
	model.TAssistantColumns.ShowThink:             true,
	model.TAssistantColumns.TextRecallTopN:        true,
	model.TAssistantColumns.TextRecallQuery:       true,
	model.TAssistantColumns.TextRecallPattern:     true,
	model.TAssistantColumns.TextRecallSlop:        true,
	model.TAssistantColumns.Temperature:           true,
	model.TAssistantColumns.QuestionTypeConfig:    true,
	model.TAssistantColumns.ShowInList:            true,
	model.TAssistantColumns.AllowlistConfig:       true,
	model.TAssistantColumns.CleanChunks:           true,
	model.TAssistantCollectionColumns.Threshold:   true,
	model.TAssistantCollectionColumns.DocTopN:     true,
	model.TAssistantCollectionColumns.ChunkConfig: true,
	"collection_lang":                             true, // 草稿状态下允许编辑
}

// 运营端用户允许修改的基础配置字段
var baseWritableColumnsInMgmt = map[string]bool{
	model.TAssistantColumns.Name:                  true,
	model.TAssistantColumns.NameEn:                true,
	model.TAssistantColumns.SearchDebug:           true,
	model.TAssistantColumns.VisibleChainConfig:    true,
	model.TAssistantColumns.FieldManageConfig:     true,
	model.TAssistantColumns.Enabled:               true,
	model.TAssistantColumns.PromptPrefix:          true,
	model.TAssistantColumns.Model:                 true,
	model.TAssistantColumns.HistoryRounds:         true,
	model.TAssistantColumns.CloseSearch:           true,
	model.TAssistantColumns.SearchEngine:          true,
	model.TAssistantColumns.MissReply:             true,
	model.TAssistantColumns.SearchTopN:            true,
	model.TAssistantColumns.ChatOrSqlConfig:       true,
	model.TAssistantColumns.AskSuggestionConfig:   true,
	model.TAssistantColumns.TextWeight:            true,
	model.TAssistantColumns.BriefIntro:            true,
	model.TAssistantColumns.DetailIntro:           true,
	model.TAssistantColumns.ShowThink:             true,
	model.TAssistantColumns.TextRecallTopN:        true,
	model.TAssistantColumns.TextRecallQuery:       true,
	model.TAssistantColumns.TextRecallPattern:     true,
	model.TAssistantColumns.TextRecallSlop:        true,
	model.TAssistantColumns.Temperature:           true,
	model.TAssistantColumns.QuestionTypeConfig:    true,
	model.TAssistantColumns.ShowInList:            true,
	model.TAssistantColumns.AllowlistConfig:       true,
	model.TAssistantColumns.CleanChunks:           true,
	model.TAssistantCollectionColumns.Threshold:   true,
	model.TAssistantCollectionColumns.DocTopN:     true,
	model.TAssistantCollectionColumns.ChunkConfig: true,
	"admins":          true,
	"collection_lang": true, // 草稿状态下允许编辑
}

// 门户端用户允许修改的微信渠道字段
var weixinChannelWritableColumnsInConsole = map[string]bool{
	model.TAssistantColumns.Nickname:               true,
	model.TAssistantColumns.AvatarUrl:              true,
	model.TAssistantColumns.SystemLang:             true,
	model.TAssistantColumns.WelcomeMessageConfig:   true,
	model.TAssistantColumns.PresetQuestionConfig:   true,
	model.TAssistantColumns.KefuConfig:             true,
	model.TAssistantColumns.RatingScaleReplyConfig: true,
	model.TAssistantColumns.InteractiveCodeConfig:  true,
	model.TAssistantColumns.ChatIdleDuration:       true,
	//model.TAssistantColumns.SystemLanguages:        true,
}

// 运营端用户允许修改的微信渠道字段
var weixinChannelWritableColumnsInMgmt = map[string]bool{
	model.TAssistantColumns.Nickname:               true,
	model.TAssistantColumns.AvatarUrl:              true,
	model.TAssistantColumns.SystemLang:             true,
	model.TAssistantColumns.WelcomeMessageConfig:   true,
	model.TAssistantColumns.PresetQuestionConfig:   true,
	model.TAssistantColumns.KefuConfig:             true,
	model.TAssistantColumns.RatingScaleReplyConfig: true,
	model.TAssistantColumns.InteractiveCodeConfig:  true,
	model.TAssistantColumns.WeixinDevelopConfig:    true,
	model.TAssistantColumns.ChatIdleDuration:       true,
	//model.TAssistantColumns.SystemLanguages:        true,
}

// 门户端用户允许修改的碳LIVE Web渠道字段
var tanliveWebChannelWritableColumnsInConsole = map[string]bool{
	model.TAssistantColumns.Nickname:               true,
	model.TAssistantColumns.NicknameEn:             true,
	model.TAssistantColumns.AvatarUrl:              true,
	model.TAssistantColumns.AssistantLang:          true,
	model.TAssistantColumns.WelcomeMessageConfig:   true,
	model.TAssistantColumns.PresetQuestionConfig:   true,
	model.TAssistantColumns.RatingScaleReplyConfig: true,
	model.TAssistantColumns.InteractiveCodeConfig:  true,
	model.TAssistantColumns.WebsiteConfig:          true,
	model.TAssistantColumns.FeedbackEnabled:        true,
	model.TAssistantColumns.GraphParseConfig:       true,
	model.TAssistantColumns.SwitchAssistantId:      true,
	model.TAssistantColumns.KefuConfig:             true,
	//model.TAssistantColumns.SystemLanguages:        true,
}

// 运营端用户允许修改的碳LIVE Web渠道字段
var tanliveWebChannelWritableColumnsInMgmt = map[string]bool{
	model.TAssistantColumns.Nickname:               true,
	model.TAssistantColumns.NicknameEn:             true,
	model.TAssistantColumns.AvatarUrl:              true,
	model.TAssistantColumns.AssistantLang:          true,
	model.TAssistantColumns.WelcomeMessageConfig:   true,
	model.TAssistantColumns.PresetQuestionConfig:   true,
	model.TAssistantColumns.RatingScaleReplyConfig: true,
	model.TAssistantColumns.InteractiveCodeConfig:  true,
	model.TAssistantColumns.WebsiteConfig:          true,
	model.TAssistantColumns.FeedbackEnabled:        true,
	model.TAssistantColumns.GraphParseConfig:       true,
	model.TAssistantColumns.SwitchAssistantId:      true,
	model.TAssistantColumns.KefuConfig:             true,
	//model.TAssistantColumns.SystemLanguages:        true,
}

// 门户端用户允许修改的碳LIVE应用渠道字段
var tanliveAppChannelWritableColumnsInConsole = map[string]bool{
	model.TAssistantColumns.Nickname:               true,
	model.TAssistantColumns.NicknameEn:             true,
	model.TAssistantColumns.AvatarUrl:              true,
	model.TAssistantColumns.AssistantLang:          true,
	model.TAssistantColumns.WelcomeMessageConfig:   true,
	model.TAssistantColumns.PresetQuestionConfig:   true,
	model.TAssistantColumns.RatingScaleReplyConfig: true,
	model.TAssistantColumns.InteractiveCodeConfig:  true,
	model.TAssistantColumns.FeedbackEnabled:        true,
	model.TAssistantColumns.GraphParseConfig:       true,
	model.TAssistantColumns.KefuConfig:             true,
	model.TAssistantColumns.AppId:                  true,
	//model.TAssistantColumns.SystemLanguages:        true,
}

// 运营端用户允许修改的碳LIVE应用渠道字段
var tanliveAppChannelWritableColumnsInMgmt = map[string]bool{
	model.TAssistantColumns.Nickname:               true,
	model.TAssistantColumns.NicknameEn:             true,
	model.TAssistantColumns.AvatarUrl:              true,
	model.TAssistantColumns.AssistantLang:          true,
	model.TAssistantColumns.WelcomeMessageConfig:   true,
	model.TAssistantColumns.PresetQuestionConfig:   true,
	model.TAssistantColumns.RatingScaleReplyConfig: true,
	model.TAssistantColumns.InteractiveCodeConfig:  true,
	model.TAssistantColumns.FeedbackEnabled:        true,
	model.TAssistantColumns.GraphParseConfig:       true,
	model.TAssistantColumns.KefuConfig:             true,
	model.TAssistantColumns.AppId:                  true,
	//model.TAssistantColumns.SystemLanguages:        true,
}

// 门户端用户允许修改的WhatsApp渠道字段
var whatsappChannelWritableColumnsInConsole = map[string]bool{
	model.TAssistantColumns.AvatarUrl:              true,
	model.TAssistantColumns.SystemLang:             true,
	model.TAssistantColumns.WelcomeMessageConfig:   true,
	model.TAssistantColumns.PresetQuestionConfig:   true,
	model.TAssistantColumns.RatingScaleReplyConfig: true,
	model.TAssistantColumns.WhatsappDevelopConfig:  true,
	model.TAssistantColumns.ChatIdleDuration:       true,
	//model.TAssistantColumns.SystemLanguages:        true,
}

// 运营端用户允许修改的WhatsApp渠道字段
var whatsappChannelWritableColumnsInMgmt = map[string]bool{
	model.TAssistantColumns.AvatarUrl:              true,
	model.TAssistantColumns.SystemLang:             true,
	model.TAssistantColumns.WelcomeMessageConfig:   true,
	model.TAssistantColumns.PresetQuestionConfig:   true,
	model.TAssistantColumns.RatingScaleReplyConfig: true,
	model.TAssistantColumns.WhatsappDevelopConfig:  true,
	model.TAssistantColumns.ChatIdleDuration:       true,
	//model.TAssistantColumns.SystemLanguages:        true,
}

// 门户端用户允许修改的小程序渠道字段
var miniprogramChannelWritableColumnsInConsole = map[string]bool{
	model.TAssistantColumns.Nickname:               true,
	model.TAssistantColumns.AvatarUrl:              true,
	model.TAssistantColumns.AssistantLang:          true,
	model.TAssistantColumns.WelcomeMessageConfig:   true,
	model.TAssistantColumns.PresetQuestionConfig:   true,
	model.TAssistantColumns.RatingScaleReplyConfig: true,
	model.TAssistantColumns.InteractiveCodeConfig:  true,
	model.TAssistantColumns.MiniprogramConfig:      true,
	model.TAssistantColumns.FeedbackEnabled:        true,
	model.TAssistantColumns.KefuConfig:             true,
	//model.TAssistantColumns.GraphParseConfig:       true,
	//model.TAssistantColumns.SystemLanguages:        true,
}

// 运营端用户允许修改的小程序渠道字段
var miniprogramChannelWritableColumnsInMgmt = map[string]bool{
	model.TAssistantColumns.Nickname:               true,
	model.TAssistantColumns.AvatarUrl:              true,
	model.TAssistantColumns.AssistantLang:          true,
	model.TAssistantColumns.WelcomeMessageConfig:   true,
	model.TAssistantColumns.PresetQuestionConfig:   true,
	model.TAssistantColumns.RatingScaleReplyConfig: true,
	model.TAssistantColumns.InteractiveCodeConfig:  true,
	model.TAssistantColumns.MiniprogramConfig:      true,
	model.TAssistantColumns.FeedbackEnabled:        true,
	model.TAssistantColumns.KefuConfig:             true,
	//model.TAssistantColumns.GraphParseConfig:       true,
	//model.TAssistantColumns.SystemLanguages:        true,
}

// UpdateAssistantLogic ...
type UpdateAssistantLogic struct {
}

// BatchUpdate 批量更新
// 如果指定了批次号，items里的助手必须都属于该批次号，且批次号内的助手必须为草稿，允许新增、删除；
// 未指定批次号时items里的助手必须为非草稿，仅允许更新。
func (l *UpdateAssistantLogic) BatchUpdate(ctx context.Context, req *aipb.ReqBatchUpdateAssistant) error {
	updating, creating, deleting, err := l.validateAndSplitItems(ctx, req.BatchNo, req.Items)
	if err != nil {
		return err
	}

	var (
		batchNo  = req.BatchNo
		updateBy = req.UpdateBy
		isDraft  = req.IsDraft
		finished = make(chan struct{})
	)
	defer close(finished)

	// 无批次号时
	if batchNo == "" {
		isDraft = false
	}

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		var err error

		// 更新
		if err = l.batchUpdate(tx, finished, batchNo, updating, updateBy, isDraft); err != nil {
			return err
		}

		// 创建
		if _, err = new(CreateAssistantLogic).batchCreate(tx, finished, batchNo, creating, updateBy, isDraft); err != nil {
			return err
		}

		// 删除
		if len(deleting) > 0 {
			if err = new(DeleteAssistantLogic).deleteTx(tx, deleting, updateBy); err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

func (l *UpdateAssistantLogic) batchUpdate(tx *gorm.DB, finished chan struct{}, batchNo string, items []*aipb.ReqBatchUpdateAssistant_Item, updateBy *basepb.Identity, isDraft bool) error {
	for _, item := range items {
		assistant, err := l.findOld(tx, item.AssistantId)
		if err != nil {
			return err
		}
		oldConfig := proto.Clone(assistant.ToConfig()).(*aipb.AssistantConfig)

		// 该字段没存到数据库，因此需要手动赋值
		userLabelConfigChanged := slices.Contains(item.Mask.Paths, "user_label_config")
		if userLabelConfigChanged {
			oldConfig.UserLabelConfig = item.OldUserLabelConfig
		}

		mask := l.filterMask(assistant, item.Mask, updateBy)

		if err = l.validate(tx, batchNo, assistant, item.Config, mask, updateBy); err != nil {
			return err
		}

		if err = l.update(tx, assistant, item.Config, mask, updateBy); err != nil {
			return err
		}

		// 发布
		if assistant.IsDraft && !isDraft {
			if err = new(PublishAssistantLogic).publishTx(tx, finished, assistant.ID, updateBy); err != nil {
				return err
			}
			assistant.IsDraft = false
		}

		// 操作日志
		newConfig := assistant.ToConfig()
		if userLabelConfigChanged {
			newConfig.UserLabelConfig = item.Config.UserLabelConfig
			mask = append(mask, "user_label_config")
		}
		if err = l.updateLog(tx, assistant, mask, oldConfig, newConfig); err != nil {
			return err
		}
	}
	return nil
}

// 基于批次号更新时，上行的助手必须属于该批次（创建的除外）
func (l *UpdateAssistantLogic) validateAndSplitItems(ctx context.Context, batchNo string,
	items []*aipb.ReqBatchUpdateAssistant_Item) (updating []*aipb.ReqBatchUpdateAssistant_Item, creating []*aipb.AssistantConfig, deleting []uint64, err error) {
	var (
		assistants []*model.TAssistant
		batchMap   map[uint64]bool
	)

	if batchNo != "" {
		if assistants, err = model.NewQuery[model.TAssistant](ctx).Select("id", "is_draft").GetBy("batch_no = ?", batchNo); err != nil {
			err = fmt.Errorf("get assistants by batch_no: %w", err)
			return
		}

		batchMap = make(map[uint64]bool, len(assistants))
		for _, assistant := range assistants {
			if !assistant.IsDraft {
				err = xerrors.ValidationError("this batch of assistants have been published")
				return
			}
			batchMap[assistant.ID] = true
		}
	}

	itemsMap := make(map[uint64]bool, len(items))
	for _, item := range items {
		if batchNo == "" {
			// 无批次号仅允许更新
			if item.AssistantId > 0 {
				updating = append(updating, item)
			}
		} else {
			// 有批次号仅允许更新该批次内的助手
			if item.AssistantId == 0 {
				creating = append(creating, item.Config)
			} else if batchMap[item.AssistantId] {
				updating = append(updating, item)
				itemsMap[item.AssistantId] = true
			} else {
				err = xerrors.ValidationError("assistant not in current batch")
			}
		}
	}

	// 仅按批次号更新时需要执行删除
	if batchNo != "" {
		for _, assistant := range assistants {
			if !itemsMap[assistant.ID] {
				deleting = append(deleting, assistant.ID)
			}
		}
	}

	return
}

// 查询老数据
func (l *UpdateAssistantLogic) findOld(tx *gorm.DB, id uint64) (*model.TAssistant, error) {
	assistant, err := FindAssistantConfigByKey(tx, id)
	if err != nil {
		return nil, err
	}
	return assistant, nil
}

// 过滤mask
func (l *UpdateAssistantLogic) filterMask(assistant *model.TAssistant, mask *fieldmaskpb.FieldMask, updateBy *basepb.Identity) []string {
	identityWritable := maps.Clone(baseWritableColumns[updateBy.IdentityType])

	var channelWritable map[string]bool
	switch assistant.Channel {
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN,
		aipb.AssistantChannel_ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN:
		channelWritable = weixinChannelWritableColumns[updateBy.IdentityType]
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEB:
		channelWritable = tanliveWebChannelWritableColumns[updateBy.IdentityType]
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_APP:
		channelWritable = tanliveAppChannelWritableColumns[updateBy.IdentityType]
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WHATSAPP:
		channelWritable = whatsappChannelWritableColumns[updateBy.IdentityType]
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_MINIPROGRAM:
		channelWritable = miniprogramChannelWritableColumns[updateBy.IdentityType]
	}
	for k, v := range channelWritable {
		identityWritable[k] = v
	}

	// 用户后台允许编辑的字段
	var consoleWritable map[string]bool
	if updateBy.IdentityType == basepb.IdentityType_IDENTITY_TYPE_USER ||
		updateBy.IdentityType == basepb.IdentityType_IDENTITY_TYPE_TEAM {
		consoleWritable = make(map[string]bool, len(assistant.FieldManageConfig.Writable))
		for _, column := range assistant.FieldManageConfig.Writable {
			consoleWritable[column] = true

			// search_engine同时控制search_engine、close_search、miss_reply三个字段
			if column == model.TAssistantColumns.SearchEngine {
				consoleWritable[model.TAssistantColumns.CloseSearch] = true
				consoleWritable[model.TAssistantColumns.MissReply] = true
			}
		}
	}

	var filteredMask []string
	for _, column := range mask.Paths {
		if identityWritable[column] && (consoleWritable == nil || consoleWritable[column]) {
			filteredMask = append(filteredMask, column)
		}
	}

	return filteredMask
}

// 校验
func (l *UpdateAssistantLogic) validate(tx *gorm.DB, batchNo string, assistant *model.TAssistant, config *aipb.AssistantConfig, mask []string, updateBy *basepb.Identity) error {
	if assistant.Channel != config.Channel {
		return xerrors.ValidationError("cannot modify assistant channel")
	}

	if assistant.IsDraft && batchNo == "" {
		return xerrors.ValidationError("batch_no is required when update draft")
	}

	// 归属校验
	if updateBy.IdentityType != basepb.IdentityType_IDENTITY_TYPE_MGMT {
		var isAdmin bool
		for _, admin := range assistant.Admins {
			if admin.AdminType == updateBy.IdentityType && admin.AdminID == updateBy.IdentityId {
				isAdmin = true
				break
			}
		}
		if !isAdmin {
			return xerrors.ForbiddenError("no permission")
		}
	}

	// 未发布的助手校验
	if assistant.IsDraft {
		return l.validateDraft(assistant, config, mask)
	}

	// 已发布的助手参数校验
	if err := validation.Validator.StructFiltered(config, validation.NewAssistantFilterFunc(mask, assistant.Channel)); err != nil {
		return xerrors.ValidationError(err)
	}

	// 校验staff用户名重复
	if model.IsWeixinChannelConfig(config) &&
		slices.Contains(mask, model.TAssistantColumns.KefuConfig) &&
		config.WeixinChannelConfig.KefuConfig.Enabled {
		if err := isStaffUsernameRepeated(config.WeixinChannelConfig.KefuConfig.Staffs); err != nil {
			return err
		}
	}

	// 名称校验
	if slices.Contains(mask, model.TAssistantColumns.Name) {
		if nameRepeated, err := IsAssistantNameRepeated(tx, config.Name, assistant.ID); err != nil {
			return err
		} else if nameRepeated {
			return xerrors.NewCode(errorspb.AiError_AiAssistantNameExisted)
		}
	}
	if slices.Contains(mask, model.TAssistantColumns.NameEn) {
		if nameEnRepeated, err := IsAssistantNameRepeated(tx, config.NameEn, assistant.ID); err != nil {
			return err
		} else if nameEnRepeated {
			return xerrors.NewCode(errorspb.AiError_AiAssistantNameEnExisted)
		}
	}

	// 路由检测
	if config.Channel == aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEB && slices.Contains(mask, model.TAssistantColumns.WebsiteConfig) {
		var err error
		if slices.Contains(mask, "admins") {
			err = ValidateRoutePath(tx, config.TanliveWebChannelConfig.WebsiteConfig.RoutePath, updateBy, config.Admins, nil)
		} else {
			err = ValidateRoutePath(tx, config.TanliveWebChannelConfig.WebsiteConfig.RoutePath, updateBy, nil, assistant.Admins)
		}
		if err != nil {
			return err
		}
	}

	// 应用ID
	if config.Channel == aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_APP && slices.Contains(mask, model.TAssistantColumns.AppId) {
		var err error
		if slices.Contains(mask, "admins") {
			err = ValidateAppId(tx, config.TanliveAppChannelConfig.AppId, updateBy, config.Admins, nil)
		} else {
			err = ValidateAppId(tx, config.TanliveAppChannelConfig.AppId, updateBy, nil, assistant.Admins)
		}
		if err != nil {
			return err
		}
	}

	return nil
}

func (l *UpdateAssistantLogic) validateDraft(assistant *model.TAssistant, config *aipb.AssistantConfig, mask []string) error {
	draftMask := make([]string, 0)
	for _, column := range mask {
		if createDraftMaskMap[column] {
			draftMask = append(draftMask, column)
		}
	}

	// 参数校验
	if err := validation.Validator.StructFiltered(config, validation.NewAssistantFilterFunc(draftMask, assistant.Channel)); err != nil {
		return xerrors.ValidationError(err)
	}

	return nil
}

// 更新
func (l *UpdateAssistantLogic) update(tx *gorm.DB, assistant *model.TAssistant, config *aipb.AssistantConfig, mask []string, updateBy *basepb.Identity) error {
	assistantChanges, assistantCollectionChanges, collectionChanges, modifiedAdmins, newAdmins, modifiedStaffs, newStaffs, allowlist := l.fillConfigFields(assistant, config, mask)

	assistant.UpdateBy = updateBy
	assistant.UpdateDate = time.Now()
	assistantChanges = append(assistantChanges, model.TAssistantColumns.UpdateBy, model.TAssistantColumns.UpdateDate)

	allAdmins, creatingAdmins, deletingAdmins := l.diffAdmins(assistant.ID, modifiedAdmins, newAdmins, assistant.Admins)
	creatingStaffs, updatingStaffs, deletingStaffs := l.diffLiveAgents(assistant.ID, modifiedStaffs, newStaffs, assistant.LiveAgents, updateBy)

	var err error

	// 应用ID
	if slices.Contains(assistantChanges, model.TAssistantColumns.AppId) && assistant.AppId == "" {
		if assistant.AppId, err = NewAppId(tx); err != nil {
			return err
		}
	}

	// 助手
	if err = tx.Select(assistantChanges).Save(assistant).Error; err != nil {
		return fmt.Errorf("update assistant: %w", err)
	}

	// 知识库
	if len(assistantCollectionChanges) > 0 {
		if err = tx.Select(assistantCollectionChanges).Save(assistant.AssistantCollections[0]).Error; err != nil {
			return fmt.Errorf("update assistant collection: %w", err)
		}
	}
	if len(collectionChanges) > 0 {
		if err = tx.Select(collectionChanges).Save(assistant.AssistantCollections[0].Collection).Error; err != nil {
			return fmt.Errorf("update collection: %w", err)
		}
	}

	// 管理员
	if len(deletingAdmins) > 0 {
		if err = tx.Delete(&model.TAssistantAdmin{}, deletingAdmins).Error; err != nil {
			return fmt.Errorf("delete admins: %w", err)
		}
	}
	if len(creatingAdmins) > 0 {
		if err = tx.Create(&creatingAdmins).Error; err != nil {
			return fmt.Errorf("create admins: %w", err)
		}
	}
	if modifiedAdmins {
		assistant.Admins = allAdmins
	}

	// 客服坐席
	if len(creatingStaffs) > 0 {
		if err = tx.Create(&creatingStaffs).Error; err != nil {
			return fmt.Errorf("create kefu staffs: %w", err)
		}
	}
	if len(deletingStaffs) > 0 {
		if err = tx.Delete(&model.TChatLiveAgent{}, deletingStaffs).Error; err != nil {
			return fmt.Errorf("delete kefu staffs: %w", err)
		}
	}
	if len(updatingStaffs) > 0 {
		staffChanges := []string{model.TChatLiveAgentColumns.Username, model.TChatLiveAgentColumns.Nickname,
			model.TChatLiveAgentColumns.Order}
		for _, staff := range updatingStaffs {
			if err = tx.Select(staffChanges).Save(staff).Error; err != nil {
				return fmt.Errorf("update kefu staff: %w", err)
			}
		}
	}

	// 白名单
	if slices.Contains(assistantChanges, model.TAssistantColumns.AllowlistConfig) {
		if err = l.replaceAllowlist(tx, assistant, allowlist); err != nil {
			return err
		}
	}

	return nil
}

func (l *UpdateAssistantLogic) updateLog(tx *gorm.DB, assistant *model.TAssistant, fields []string, oldConfig, newConfig *aipb.AssistantConfig) error {
	// 只保留修改过的值
	rules := make(map[string]bool, len(fields))
	for _, field := range fields {
		rules[field] = true
	}
	ll := &GetAssistantsLogic{}
	oldConfig = ll.applyVisibility(oldConfig, rules)
	newConfig = ll.applyVisibility(newConfig, rules)

	// 确保channel有值
	oldConfig.Channel = assistant.Channel
	newConfig.Channel = assistant.Channel

	action := aipb.AssistantAction_ASSISTANT_ACTION_SAVE_DRAFT
	if !assistant.IsDraft {
		action = aipb.AssistantAction_ASSISTANT_ACTION_PUBLISH
	}
	log := &model.TAssistantLog{
		AssistantID: assistant.ID,
		Action:      action,
		Changes: &aipb.AssistantChanges{
			Fields: fields,
			Old:    oldConfig,
			New:    newConfig,
		},
		CreateBy:   assistant.UpdateBy,
		CreateDate: assistant.UpdateDate,
	}

	if err := tx.Create(log).Error; err != nil {
		return fmt.Errorf("create assistant update log: %w", err)
	}

	return nil
}

func (l *UpdateAssistantLogic) replaceAllowlist(tx *gorm.DB, assistant *model.TAssistant, allowlist []*model.TAssistantAllowlist) error {
	if len(allowlist) == 0 {
		if err := tx.Delete(&model.TAssistantAllowlist{}, "assistant_id = ?", assistant.ID).Error; err != nil {
			return fmt.Errorf("delete all allowlist: %w", err)
		}
		return nil
	}

	// upsert
	if err := tx.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: model.TAssistantAllowlistColumns.AssistantID},
			{Name: model.TAssistantAllowlistColumns.Value},
			{Name: model.TAssistantAllowlistColumns.Value2},
			{Name: model.TAssistantAllowlistColumns.Type},
		},
		DoNothing: true,
	}).Create(allowlist).Error; err != nil {
		return fmt.Errorf("upsert allowlist: %w", err)
	}

	// delete
	notInReplacements := strings.Join(
		slices.Repeat([]string{"(?, ?)"}, len(allowlist)),
		", ",
	)
	deleteWhere := "`assistant_id` = ? AND (`value`, `value2`) NOT IN (" + notInReplacements + ")"

	deleteArgs := make([]any, 0, len(allowlist)+1)
	deleteArgs = append(deleteArgs, assistant.ID)
	for _, item := range allowlist {
		deleteArgs = append(deleteArgs, item.Value, item.Value2)
	}

	if err := tx.Where(deleteWhere, deleteArgs...).Delete(&model.TAssistantAllowlist{}).Error; err != nil {
		return fmt.Errorf("delete allowlist: %w", err)
	}

	return nil
}

func (l *UpdateAssistantLogic) fillConfigFields(assistant *model.TAssistant, config *aipb.AssistantConfig, mask []string) (assistantChanges, assistantCollectionChanges, collectionChanges []string, modifiedAdmins bool, newAdmins []*basepb.Identity, modifiedStaffs bool, newStaffs []*aipb.AssistantKefuStaff, allowlist []*model.TAssistantAllowlist) {
	assistantType := reflect.TypeOf(assistant).Elem()
	assistantValue := reflect.ValueOf(assistant).Elem()

	mmask := make(map[string]bool, len(mask))
	for _, m := range mask {
		mmask[m] = true
	}

	// admins
	if mmask["admins"] {
		modifiedAdmins = true
		newAdmins = config.Admins
		delete(mmask, "admins")
	}

	// allowlist
	var allowlistPhones []string
	if mmask["allowlist_config"] && config.AllowlistConfig != nil {
		allowlistPhones = config.AllowlistConfig.Phones
		config.AllowlistConfig.Phones = nil
	}

	// collection
	var assistantCollection *model.TAssistantCollection
	var collection *model.TCollection
	if len(assistant.AssistantCollections) > 0 {
		assistantCollection = assistant.AssistantCollections[0]
		collection = assistantCollection.Collection
	}
	if assistantCollection != nil && mmask[model.TAssistantCollectionColumns.Threshold] {
		if assistantCollection.Threshold != config.Threshold {
			assistantCollection.Threshold = config.Threshold
			assistantCollectionChanges = append(assistantCollectionChanges, model.TAssistantCollectionColumns.Threshold)
		}
		delete(mmask, model.TAssistantCollectionColumns.Threshold)
	}
	if assistantCollection != nil && mmask[model.TAssistantCollectionColumns.DocTopN] {
		if assistantCollection.DocTopN != config.DocTopN {
			assistantCollection.DocTopN = config.DocTopN
			assistantCollectionChanges = append(assistantCollectionChanges, model.TAssistantCollectionColumns.DocTopN)
		}
		delete(mmask, model.TAssistantCollectionColumns.Threshold)
	}
	if assistantCollection != nil && mmask[model.TAssistantCollectionColumns.ChunkConfig] {
		if !reflect.DeepEqual(assistantCollection.ChunkConfig, config.ChunkConfig) {
			assistantCollection.ChunkConfig = config.ChunkConfig
			assistantCollectionChanges = append(assistantCollectionChanges, model.TAssistantCollectionColumns.ChunkConfig)
		}
		delete(mmask, model.TAssistantCollectionColumns.ChunkConfig)
	}

	assistantChanges = l.fill(assistantType, assistantValue, config, mmask)

	var channelChanges []string
	switch assistant.Channel {
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEIXIN,
		aipb.AssistantChannel_ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN:
		modifiedStaffs, newStaffs, channelChanges = l.fillWeixinChannelConfig(
			assistant, assistantType, assistantValue, config.WeixinChannelConfig, mmask)
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEB:
		channelChanges = l.fillTanliveWebChannelConfig(
			assistant, assistantType, assistantValue, config.TanliveWebChannelConfig, mmask)
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_APP:
		channelChanges = l.fillTanliveAppChannelConfig(
			assistant, assistantType, assistantValue, config.TanliveAppChannelConfig, mmask)
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WHATSAPP:
		channelChanges = l.fillWhatsappChannelConfig(
			assistant, assistantType, assistantValue, config.WhatsappChannelConfig, mmask)
	case aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_MINIPROGRAM:
		channelChanges = l.fillMiniprogramChannelConfig(
			assistant, assistantType, assistantValue, config.MiniprogramChannelConfig, mmask)
	}

	assistantChanges = append(assistantChanges, channelChanges...)

	// 启用/禁用时间
	if slices.Contains(assistantChanges, model.TAssistantColumns.Enabled) {
		assistant.EnabledAt = sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		}
		assistantChanges = append(assistantChanges, model.TAssistantColumns.EnabledAt)
	}

	// 英文名称
	if slices.Contains(assistantChanges, model.TAssistantColumns.NameEn) {
		assistant.NameEn = formatEnName(assistant.NameEn)
	}

	// 英文昵称
	if slices.Contains(assistantChanges, model.TAssistantColumns.NicknameEn) {
		assistant.NicknameEn = formatEnName(assistant.NicknameEn)
	}

	// 问题建议模式默认使用1
	if slices.Contains(assistantChanges, model.TAssistantColumns.AskSuggestionConfig) {
		if assistant.AskSuggestionConfig.Mode == 0 {
			assistant.AskSuggestionConfig.Mode = aipb.AskSuggestionMode_ASK_SUGGESTION_MODE_1
		}
	}

	// 草稿允许更新知识库名称和向量化模型
	if assistant.IsDraft && collection != nil {
		var shouldChangeName bool
		if mmask["collection_lang"] {
			shouldChangeName = true
			collection.Lang = config.CollectionLang
			collectionChanges = append(collectionChanges, model.TCollectionColumns.Lang)
		}
		if slices.Contains(assistantChanges, model.TAssistantColumns.NameEn) || shouldChangeName {
			collection.RagName = NewRagName(assistant.NameEn, collection.Lang)
			collectionChanges = append(collectionChanges, model.TCollectionColumns.RagName)
		}
	}

	// allowlist
	if slices.Contains(assistantChanges, model.TAssistantColumns.AllowlistConfig) {
		allowlist = append(allowlist, l.phonesToAllowlist(assistant.ID, allowlistPhones)...)
	}

	return
}

func (l *UpdateAssistantLogic) fillWeixinChannelConfig(assistant *model.TAssistant, assistantType reflect.Type, assistantValue reflect.Value, config *aipb.AssistantWeixinChannelConfig, mask map[string]bool) (bool, []*aipb.AssistantKefuStaff, []string) {
	// 客服坐席需更新关联表
	var (
		modifiedStaffs bool
		staffs         []*aipb.AssistantKefuStaff
	)
	if mask[model.TAssistantColumns.KefuConfig] {
		modifiedStaffs = true
		staffs = config.KefuConfig.Staffs
		config.KefuConfig.Staffs = nil
	}

	changes := l.fill(assistantType, assistantValue, config, mask)

	// 兼容system_languages
	if slices.Contains(changes, model.TAssistantColumns.SystemLang) {
		assistant.SystemLanguages = []string{config.SystemLang}
		changes = append(changes, model.TAssistantColumns.SystemLanguages)
	}

	return modifiedStaffs, staffs, changes
}

func (l *UpdateAssistantLogic) fillTanliveWebChannelConfig(assistant *model.TAssistant, assistantType reflect.Type, assistantValue reflect.Value, config *aipb.AssistantTanliveWebChannelConfig, mask map[string]bool) []string {
	changes := l.fill(assistantType, assistantValue, config, mask)

	// 兼容system_languages
	if slices.Contains(changes, model.TAssistantColumns.AssistantLang) {
		assistant.SystemLanguages = []string{config.AssistantLang}
		changes = append(changes, model.TAssistantColumns.SystemLanguages)
	}

	return changes
}

func (l *UpdateAssistantLogic) fillTanliveAppChannelConfig(assistant *model.TAssistant, assistantType reflect.Type, assistantValue reflect.Value, config *aipb.AssistantTanliveAppChannelConfig, mask map[string]bool) []string {
	changes := l.fill(assistantType, assistantValue, config, mask)

	// 兼容system_languages
	if slices.Contains(changes, model.TAssistantColumns.AssistantLang) {
		assistant.SystemLanguages = []string{config.AssistantLang}
		changes = append(changes, model.TAssistantColumns.SystemLanguages)
	}

	return changes
}

func (l *UpdateAssistantLogic) fillWhatsappChannelConfig(assistant *model.TAssistant, assistantType reflect.Type, assistantValue reflect.Value, config *aipb.AssistantWhatsappChannelConfig, mask map[string]bool) []string {
	changes := l.fill(assistantType, assistantValue, config, mask)

	if slices.Contains(changes, model.TAssistantColumns.WhatsappDevelopConfig) {
		assistant.Nickname = assistant.WhatsappDevelopConfig.BusinessNumber
		changes = append(changes, model.TAssistantColumns.Nickname)
	}

	// 兼容system_languages
	if slices.Contains(changes, model.TAssistantColumns.SystemLang) {
		assistant.SystemLanguages = []string{config.SystemLang}
		changes = append(changes, model.TAssistantColumns.SystemLanguages)
	}

	return changes
}

func (l *UpdateAssistantLogic) fillMiniprogramChannelConfig(assistant *model.TAssistant, assistantType reflect.Type, assistantValue reflect.Value, config *aipb.AssistantMiniprogramChannelConfig, mask map[string]bool) []string {
	changes := l.fill(assistantType, assistantValue, config, mask)

	// 兼容system_languages
	if slices.Contains(changes, model.TAssistantColumns.AssistantLang) {
		assistant.SystemLanguages = []string{config.AssistantLang}
		changes = append(changes, model.TAssistantColumns.SystemLanguages)
	}

	return changes
}

func (l *UpdateAssistantLogic) fill(assistantType reflect.Type, assistantValue reflect.Value, config proto.Message, mask map[string]bool) []string {
	var changes []string

	newType := reflect.TypeOf(config).Elem()
	newValue := reflect.ValueOf(config).Elem()

	for column, modified := range mask {
		if !modified {
			continue
		}

		name := xstrings.Pascal(column)

		if _, ok := assistantType.FieldByName(name); !ok {
			continue
		}
		if _, ok := newType.FieldByName(name); !ok {
			continue
		}

		oldFieldValue := assistantValue.FieldByName(name)
		newFieldValue := newValue.FieldByName(name)
		if !reflect.DeepEqual(oldFieldValue.Interface(), newFieldValue.Interface()) {
			oldFieldValue.Set(newFieldValue)
			changes = append(changes, column)
		}
	}

	return changes
}

func (l *UpdateAssistantLogic) diffAdmins(assistantID uint64, modifiedAdmins bool, newAdmins []*basepb.Identity, oldAdmins []*model.TAssistantAdmin) (all, creating []*model.TAssistantAdmin, deleting []uint64) {
	if !modifiedAdmins {
		return
	}

	type identity struct {
		identityId   uint64
		identityType basepb.IdentityType
	}
	newAdminsMap := make(map[identity]bool, len(newAdmins))
	for _, newAdmin := range newAdmins {
		newAdminsMap[identity{newAdmin.IdentityId, newAdmin.IdentityType}] = true
		all = append(all, &model.TAssistantAdmin{
			AssistantID: assistantID,
			AdminID:     newAdmin.IdentityId,
			AdminType:   newAdmin.IdentityType,
		})
	}
	oldAdminsMap := make(map[identity]bool, len(oldAdmins))
	for _, oldAdmin := range oldAdmins {
		oldAdminsMap[identity{oldAdmin.AdminID, oldAdmin.AdminType}] = true
	}

	for _, newAdmin := range newAdmins {
		if !oldAdminsMap[identity{newAdmin.IdentityId, newAdmin.IdentityType}] {
			creating = append(creating, &model.TAssistantAdmin{
				AssistantID: assistantID,
				AdminID:     newAdmin.IdentityId,
				AdminType:   newAdmin.IdentityType,
			})
		}
	}

	for _, oldAdmin := range oldAdmins {
		if !newAdminsMap[identity{oldAdmin.AdminID, oldAdmin.AdminType}] {
			deleting = append(deleting, oldAdmin.ID)
		}
	}

	return
}

func (l *UpdateAssistantLogic) diffLiveAgents(assistantID uint64, modifiedStaffs bool, newStaffs []*aipb.AssistantKefuStaff, oldLiveAgents []*model.TChatLiveAgent, updateBy *basepb.Identity) (creating, updating []*model.TChatLiveAgent, deleting []uint64) {
	if !modifiedStaffs {
		return
	}

	oldLiveAgentsMap := make(map[uint64]*model.TChatLiveAgent, len(oldLiveAgents))
	for _, oldLiveAgent := range oldLiveAgents {
		oldLiveAgentsMap[oldLiveAgent.ID] = oldLiveAgent
	}

	canCreate := updateBy.IdentityType == basepb.IdentityType_IDENTITY_TYPE_MGMT
	newStaffsMap := make(map[uint64]bool, len(newStaffs))
	for index, newStaff := range newStaffs {
		if newStaff.Username == "" {
			continue
		}
		if canCreate && newStaff.Id == 0 {
			creating = append(creating, &model.TChatLiveAgent{
				AssistantID: assistantID,
				Username:    newStaff.Username,
				Nickname:    newStaff.Nickname,
				CreateDate:  time.Now(),
				Status:      aipb.ChatLiveAgentStatus_CHAT_LIVE_AGENT_STATUS_CURRENTLY_SERVING,
				Order:       index,
			})
		} else if newStaff.Id > 0 && oldLiveAgentsMap[newStaff.Id] != nil {
			// 门户端无法修改用户名
			liveAgent := &model.TChatLiveAgent{
				ID:       newStaff.Id,
				Nickname: newStaff.Nickname,
				Username: oldLiveAgentsMap[newStaff.Id].Username,
				Order:    index,
			}
			if canCreate {
				liveAgent.Username = newStaff.Username
			}
			updating = append(updating, liveAgent)
			newStaffsMap[newStaff.Id] = true
		}
	}

	for _, oldLiveAgent := range oldLiveAgents {
		if !newStaffsMap[oldLiveAgent.ID] {
			deleting = append(deleting, oldLiveAgent.ID)
		}
	}

	return
}

func (l *UpdateAssistantLogic) phonesToAllowlist(assistantID uint64, phones []string) []*model.TAssistantAllowlist {
	m := make(map[string]bool, len(phones))
	allowlist := make([]*model.TAssistantAllowlist, 0, len(phones))
	for _, phone := range phones {
		// 去重
		if m[phone] {
			continue
		}
		m[phone] = true

		allowlist = append(allowlist, &model.TAssistantAllowlist{
			AssistantID: assistantID,
			Type:        aipb.AssistantAllowlistType_ASSISTANT_ALLOWLIST_TYPE_PHONE,
			Value:       phone,
			CreateDate:  time.Now(),
		})
	}
	return allowlist
}

// FindAssistantConfigByKey 通过主键查询助手配置
func FindAssistantConfigByKey(tx *gorm.DB, id uint64) (*model.TAssistant, error) {
	assistant, err := FindAssistantByKey(tx, id, "Admins", "AssistantCollections.Collection", "LiveAgents")
	if err != nil {
		return nil, err
	}
	if assistant == nil {
		return nil, xerrors.NewCode(errorspb.AiError_AiAssistantNotFound)
	}
	return assistant, nil
}

// FindAssistantByKey 通过主键查询助手
func FindAssistantByKey(tx *gorm.DB, id uint64, relations ...string) (*model.TAssistant, error) {
	query := tx.Model(&model.TAssistant{})

	for _, relation := range relations {
		query.Preload(relation)
	}

	assistant := &model.TAssistant{}
	if err := query.Find(assistant, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("find assistant: %w", err)
	}

	return assistant, nil
}
