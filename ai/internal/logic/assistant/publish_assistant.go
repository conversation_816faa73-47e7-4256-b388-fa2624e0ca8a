package assistant

import (
	"bytes"
	"context"
	"fmt"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/helper"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/validation"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/miniprogram"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/xcos"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"github.com/go-redsync/redsync/v4"
	"github.com/google/uuid"
	"github.com/tencentyun/cos-go-sdk-v5"
	"gorm.io/gorm"
)

// PublishAssistantLogic 发布助手
type PublishAssistantLogic struct {
}

func (l *PublishAssistantLogic) publishTx(tx *gorm.DB, finished chan struct{}, assistantID uint64, publishBy *basepb.Identity) error {
	assistant, err := FindAssistantConfigByKey(tx, assistantID)
	if err != nil {
		return err
	}

	if err = l.validate(tx, assistant, publishBy); err != nil {
		return err
	}

	return l.publish(tx, finished, assistant, publishBy)
}

func (l *PublishAssistantLogic) validate(tx *gorm.DB, assistant *model.TAssistant, publishBy *basepb.Identity) error {
	if !assistant.IsDraft {
		return nil
	}

	// 参数校验
	config := assistant.ToConfig()
	if err := validation.Validator.Struct(config); err != nil {
		return xerrors.ValidationError(err)
	}

	// 名称校验
	if nameRepeated, err := IsAssistantNameRepeated(tx, assistant.Name, assistant.ID); err != nil {
		return err
	} else if nameRepeated {
		return xerrors.NewCode(errorspb.AiError_AiAssistantNameExisted)
	}
	if nameEnRepeated, err := IsAssistantNameRepeated(tx, assistant.NameEn, assistant.ID); err != nil {
		return err
	} else if nameEnRepeated {
		return xerrors.NewCode(errorspb.AiError_AiAssistantNameEnExisted)
	}

	// 路由检测
	if config.Channel == aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_WEB {
		if err := ValidateRoutePath(tx, assistant.WebsiteConfig.RoutePath, publishBy, nil, assistant.Admins); err != nil {
			return err
		}
	}

	return nil
}

func (l *PublishAssistantLogic) publish(tx *gorm.DB, finished chan struct{}, assistant *model.TAssistant, updateBy *basepb.Identity) error {
	if !assistant.IsDraft {
		return nil
	}

	assistant.IsDraft = false
	assistant.UpdateBy = updateBy
	assistant.UpdateDate = time.Now()

	changes := []string{
		model.TAssistantColumns.IsDraft,
		model.TAssistantColumns.UpdateBy,
		model.TAssistantColumns.UpdateDate,
	}

	err := tx.Select(changes).Save(assistant).Error
	if err != nil {
		return fmt.Errorf("publish assistant: %w", err)
	}

	if assistant.Channel == aipb.AssistantChannel_ASSISTANT_CHANNEL_TANLIVE_MINIPROGRAM {
		assistantID := assistant.ID
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			<-finished

			if err := l.GenMiniprogramQrCode(ctx, assistantID); err != nil {
				log.WithContext(ctx).Errorw("gen assistant miniprogram qrcode", "err", err)
			}

			if err := l.GenMiniprogramSchema(ctx, assistantID); err != nil {
				log.WithContext(ctx).Errorw("gen assistant miniprogram schema", "err", err)
			}

			return nil
		}, boot.TraceGo(tx.Statement.Context))
	}

	// 创建知识库
	collection := assistant.AssistantCollections[0].Collection
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		<-finished

		if err := InitCollectionIndex(ctx, collection, false); err != nil {
			log.WithContext(ctx).Errorw("init collection index failed", "collection", collection.ID, "err", err)
			return nil
		}

		return nil
	})

	return nil
}

// GenMiniprogramQrCode 生成小程序码
func (l *PublishAssistantLogic) GenMiniprogramQrCode(ctx context.Context, assistantID uint64) error {
	hashID, err := hashids.Encode(assistantID)
	if err != nil {
		return fmt.Errorf("hash assistant id: %w", err)
	}

	// 使用分布式锁，避免重复生成
	mu := helper.NewDistributedLock("genMiniprogramQrCode:"+hashID, redsync.WithExpiry(30*time.Second))
	if err = mu.TryLockContext(ctx); err != nil {
		return fmt.Errorf("new lock: %w", err)
	}
	defer mu.UnlockContext(ctx)

	assistant, err := model.NewQuery[model.TAssistant](ctx).Select("id", "miniprogram_config").FindByKey(assistantID)
	if err != nil {
		return fmt.Errorf("query assistant: %w", err)
	}
	if assistant.MiniprogramConfig != nil && assistant.MiniprogramConfig.Url != "" {
		return nil
	}

	page := strings.TrimPrefix(config.GetString("assistant.miniprogram.page"), "/")
	scene := strings.ReplaceAll(config.GetString("assistant.miniprogram.scene"), "{{.AssistantId}}", hashID)
	env := config.GetString("assistant.miniprogram.env")

	checkPath := true
	if config.Has("assistant.miniprogram.checkPath") {
		checkPath = config.GetBool("assistant.miniprogram.checkPath")
	}

	image, err := miniprogram.MPClient.GetWXACodeUnlimit(miniprogram.QRCoder{
		Page:       page,
		Scene:      scene,
		EnvVersion: env,
		CheckPath:  &checkPath,
	})
	if err != nil {
		return err
	}

	filenameUuid := uuid.New().String()
	filename := config.GetString("assistant.miniprogram.cosFilename")
	filename = strings.ReplaceAll(filename, "{{.AssistantId}}", hashID)
	filename = strings.ReplaceAll(filename, "{{.Uuid}}", filenameUuid)
	data := bytes.NewReader(image)
	_, err = xcos.Client("public").Object.Put(ctx, filename, data, &cos.ObjectPutOptions{
		ObjectPutHeaderOptions: &cos.ObjectPutHeaderOptions{
			ContentType: "image/jpeg",
		},
	})
	if err != nil {
		return fmt.Errorf("put qrcode to cos: %w", err)
	}

	_, err = model.NewQuery[model.TAssistant](ctx).UpdateByKey(assistantID, map[string]any{
		"miniprogram_config": gorm.Expr("JSON_SET(miniprogram_config, '$.url', ?)", filename),
	})
	if err != nil {
		return fmt.Errorf("update miniprogram_config.url: %w", err)
	}

	return nil
}

// GenMiniprogramSchema 生成小程序schema
func (l *PublishAssistantLogic) GenMiniprogramSchema(ctx context.Context, assistantID uint64) error {
	hashID, err := hashids.Encode(assistantID)
	if err != nil {
		return fmt.Errorf("hash assistant id: %w", err)
	}

	// 使用分布式锁，避免重复生成
	mu := helper.NewDistributedLock("genMiniprogramSchema:"+hashID, redsync.WithExpiry(30*time.Second))
	if err = mu.TryLockContext(ctx); err != nil {
		return fmt.Errorf("new lock: %w", err)
	}
	defer mu.UnlockContext(ctx)

	assistant, err := model.NewQuery[model.TAssistant](ctx).Select("id", "miniprogram_config").FindByKey(assistantID)
	if err != nil {
		return fmt.Errorf("query assistant: %w", err)
	}
	if assistant.MiniprogramConfig != nil && assistant.MiniprogramConfig.Schema != "" {
		return nil
	}

	page := strings.TrimPrefix(config.GetString("assistant.miniprogram.page"), "/")
	scene := strings.ReplaceAll(config.GetString("assistant.miniprogram.scene"), "{{.AssistantId}}", hashID)
	env := config.GetString("assistant.miniprogram.env")

	schema, err := miniprogram.MPClient.GetSURLScheme(scene, page, env)
	if err != nil {
		return err
	}

	_, err = model.NewQuery[model.TAssistant](ctx).UpdateByKey(assistantID, map[string]any{
		"miniprogram_config": gorm.Expr("JSON_SET(miniprogram_config, '$.schema', ?)", schema),
	})
	if err != nil {
		return fmt.Errorf("update miniprogram_config.schema: %w", err)
	}

	return nil
}
