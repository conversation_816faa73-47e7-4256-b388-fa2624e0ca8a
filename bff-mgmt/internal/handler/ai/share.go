package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	"google.golang.org/protobuf/types/known/emptypb"
)

// ListAssistantCanShareDoc 查询可分享的助手列表
func (a *Ai) ListAssistantCanShareDoc(ctx context.Context, req *bffaipb.ReqListAssistantCanShareDoc,
	rsp *bffaipb.RspListAssistantCanShareDoc,
) error {
	reqSender := &aipb.ReqListAssistantCanShareDoc{
		CreateBy:  config.GetUint64("ai.share.team_id"),
		AdminType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
		DocId:     req.DocId,
		Name:      req.Name,
		Language:  req.Language,
	}

	sharedAssistant, err := client.AiClient.ListAssistantCanShareDoc(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	for _, assistant := range sharedAssistant.Assistants {
		rsp.Assistants = append(rsp.Assistants, &bffaipb.RspListAssistantCanShareDoc_SharedAssistant{
			Id: assistant.Id, Name: assistant.Name,
			IsSelected: assistant.IsSelected, NameEn: assistant.NameEn,
		})
	}
	return nil
}

// CreateAssistantShare 创建文档分享（支持助手、个人、团队）
func (a *Ai) CreateAssistantShare(ctx context.Context, req *bffaipb.ReqCreateAssistantShare,
	rsp *bffaipb.RspCreateDocShare,
) error {
	var adminType basepb.IdentityType
	var createBy uint64

	createBy = config.GetUint64("ai.share.team_id")
	adminType = basepb.IdentityType_IDENTITY_TYPE_TEAM

	rpcRsp, err := client.AiClient.CreateDocShare(ctx, &aipb.ReqCreateDocShare{
		DocId:       req.DocId,
		AssistantId: req.AssistantId,
		UserId:      req.UserId,
		TeamId:      req.TeamId,
		CreateBy:    createBy,
		Operator:    &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: createBy},
		AdminType:   adminType,
		IsMgmt:      true,
		QueryId:     req.QueryId,
	})
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	if rpcRsp != nil {
		rsp.Async = rpcRsp.Async
	}
	return nil
}

// CreateDocShareConfigSender 创建助手发送方设置
func (a *Ai) CreateDocShareConfigSender(ctx context.Context, req *bffaipb.ReqCreateDocShareConfigSender,
	empty *emptypb.Empty,
) error {
	reqSender := &aipb.ReqCreateDocShareConfigSender{
		ShareAssistantId: req.ShareAssistantId,
		ShareUserId:      req.ShareUserId,
		ShareTeamId:      req.ShareTeamId,
		UserId:           config.GetUint64("ai.share.team_id"),
		AdminType:        basepb.IdentityType_IDENTITY_TYPE_TEAM,
	}

	_, err := client.AiClient.CreateDocShareConfigSender(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	return nil
}

// ListeDocShareConfigSender 查询助手发送方设置
func (a *Ai) ListeDocShareConfigSender(ctx context.Context, req *bffaipb.ReqListeDocShareConfigSender,
	rsp *bffaipb.RspListeDocShareConfigSender,
) error {
	createBy := config.GetUint64("ai.share.team_id")
	reqSender := &aipb.ReqListeDocShareConfigSender{
		CreateBy:  createBy,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
	}

	assistants, err := ailogic.ListAssistantCanShareDoc(ctx, createBy, req.Name, req.Language)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	assistantSender, err := client.AiClient.ListeDocShareConfigSender(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	assistantSelectedMaps := make(map[uint64]bool)
	assistantRemoveMaps := make(map[uint64]bool)

	for _, assistant := range assistantSender.ShareAssistantId {
		assistantSelectedMaps[assistant] = true
	}
	for _, assistant := range assistantSender.MyAdminAssistantId {
		assistantRemoveMaps[assistant] = true
	}

	for _, assistant := range assistants.Assistants {
		if assistantRemoveMaps[assistant.Id] {
			continue
		}
		rsp.Assistants = append(rsp.Assistants, &bffaipb.RspListeDocShareConfigSender_SharedAssistant{
			Id: assistant.Id, Name: assistant.Name,
			IsSelected: assistantSelectedMaps[assistant.Id], NameEn: assistant.NameEn,
		})
	}

	// 获取分享的团队和用户信息
	var teamIds, userIds []uint64
	for _, v := range assistantSender.ShareTeamId {
		teamIds = append(teamIds, v)
	}
	for _, v := range assistantSender.ShareUserId {
		userIds = append(userIds, v)
	}

	if len(teamIds) != 0 {
		teams, err := logic.FetchTeamName(ctx, teamIds)
		if err != nil {
			return xerrors.InternalServerError(err)
		}
		for id, name := range teams {
			rsp.Teams = append(rsp.Teams, &bffaipb.RspListeDocShareConfigSender_SharedUserTeam{
				Id:         id,
				Name:       name,
				IsSelected: true,
			})
		}
	}
	if len(userIds) != 0 {
		users, err := logic.FetchUserName(ctx, userIds)
		if err != nil {
			return xerrors.InternalServerError(err)
		}
		for id, name := range users {
			rsp.Users = append(rsp.Users, &bffaipb.RspListeDocShareConfigSender_SharedUserTeam{
				Id:         id,
				Name:       name,
				IsSelected: true,
			})
		}
	}
	return nil
}

// ListUserCanShareDoc 查询可分享的个人列表
func (a *Ai) ListUserCanShareDoc(ctx context.Context, req *bffaipb.ReqListUserCanShareDoc,
	rsp *bffaipb.RspListUserCanShareDoc,
) error {
	userToShare, err := logic.GetUserIdByName(ctx, req.Name)
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	// 没有找到用户
	if userToShare == 0 {
		return nil
	}

	reqSender := &aipb.ReqListUserCanShareDoc{
		CreateBy:  config.GetUint64("ai.share.team_id"),
		AdminType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
		UserId:    userToShare,
		DocId:     req.DocId,
	}

	userList, err := client.AiClient.ListUserCanShareDoc(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	for _, user := range userList.Users {
		rsp.Users = append(rsp.Users, &bffaipb.RspListUserCanShareDoc_Users{
			Id:         user.Id,
			Name:       req.Name, // 因为是名称全匹配，直接用请求的 name 即可
			IsSelected: user.IsSelected,
		})
	}
	return nil
}

// ListTeamCanShareDoc 查询可分享的团队列表
func (a *Ai) ListTeamCanShareDoc(ctx context.Context, req *bffaipb.ReqListTeamCanShareDoc,
	rsp *bffaipb.RspListTeamCanShareDoc,
) error {
	reqSender := &aipb.ReqListTeamCanShareDoc{
		CreateBy:  config.GetUint64("ai.share.team_id"),
		AdminType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
		Name:      req.Name,
		Offset:    req.Offset,
		Limit:     req.Limit,
		DocId:     req.DocId,
	}

	teamList, err := client.AiClient.ListTeamCanShareDoc(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	for _, team := range teamList.Teams {
		rsp.Teams = append(rsp.Teams, &bffaipb.RspListTeamCanShareDoc_Teams{
			Id:         team.Id,
			Name:       team.Name,
			NameEn:     team.NameEn,
			IsSelected: team.IsSelected,
		})
	}
	return nil
}

// ListMyAssistantIds 查询已经设置并开启知识库接收的助手
func (a *Ai) ListMyAssistantIds(ctx context.Context, req *bffaipb.ReqListMyAssistantIds,
	rsp *bffaipb.RspListMyAssistantIds,
) error {
	// 从配置中读取固定的团队ID
	teamId := config.GetUint64("ai.share.team_id")

	reqSender := &aipb.ReqListMyAssistantIds{
		CreateBy:  teamId,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
	}

	myAssistantIds, err := client.AiClient.ListMyAssistantIds(ctx, reqSender)
	if err != nil {
		return err
	}

	rsp.ShareAssistantIds = append(rsp.ShareAssistantIds, myAssistantIds.ShareAssistantIds...)

	return nil
}

// ListTanliveAssistant 获取ai助手列表, 分享管理
func (a *Ai) ListTanliveAssistant(ctx context.Context, req *bffaipb.ReqListTanliveAssistant, rsp *bffaipb.RspListTanliveAssistant) error {
	// 从配置中读取固定的团队ID
	teamId := config.GetUint64("ai.share.team_id")

	pbReq := &aipb.ReqListAssistant{
		Page: &basepb.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Type:     req.Type,
		Name:     req.Name,
		Language: req.Language,
	}

	// 固定设置团队管理员
	pbReq.Admins = []*aipb.AssistantAdmin{{
		Id:   teamId,
		Type: basepb.IdentityType_IDENTITY_TYPE_TEAM,
	}}

	pbRsp, err := client.AiClient.ListAssistant(ctx, pbReq)
	if err != nil {
		return err
	}
	rsp.TotalCount = pbRsp.Total
	rsp.Assistants = pbRsp.Assistants
	return nil
}

// CreateDocShareConfigReceiverUserTeam 创建个人/团队接收方设置(黑名单)
func (a *Ai) CreateDocShareConfigReceiverUserTeam(ctx context.Context, req *bffaipb.ReqCreateDocShareConfigReceiverUserTeam,
	rsp *bffaipb.RspCreateDocShareConfigReceiverUserTeam,
) error {
	// 从配置中读取固定的团队ID
	teamId := config.GetUint64("ai.share.team_id")

	reqSender := &aipb.ReqCreateDocShareConfigReceiverUserTeam{
		CreateBy:      teamId,
		AdminType:     basepb.IdentityType_IDENTITY_TYPE_TEAM,
		ReceiverState: aipb.DocShareAcceptState_DOC_SHARE_ACCEPT_STATE_ENABLED,
		OtherState:    aipb.DocShareState_DOC_SHARE_STATE_DISABLED,
	}

	var userShareData []*aipb.DocReceiveConfigBySender
	if len(req.TeamId) != 0 || len(req.UserId) != 0 {
		userShareData = append(userShareData, &aipb.DocReceiveConfigBySender{
			TeamId: req.TeamId,
			UserId: req.UserId,
			State:  aipb.DocShareState_DOC_SHARE_STATE_NOT_ACCEPTED,
		})
	}

	reqSender.UserShares = userShareData

	_, err := client.AiClient.CreateDocShareConfigReceiverUserTeam(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	return nil
}

// ListDocShareConfigReceiverUserTeam 查询个人/团队接收方设置(黑名单)
func (a *Ai) ListDocShareConfigReceiverUserTeam(ctx context.Context, req *bffaipb.ReqListDocShareConfigReceiverUserTeam,
	rsp *bffaipb.RspListDocShareConfigReceiverUserTeam,
) error {
	// 从配置中读取固定的团队ID
	teamId := config.GetUint64("ai.share.team_id")

	reqSender := &aipb.ReqListDocShareConfigReceiverUserTeam{
		CreateBy:  teamId,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
	}

	userTeamReceiver, err := client.AiClient.ListDocShareConfigReceiverUserTeam(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	if userTeamReceiver == nil {
		return nil
	}

	// 获取团队和用户的名称信息
	var teamIds, userIds []uint64
	for _, share := range userTeamReceiver.UserShares {
		teamIds = append(teamIds, share.TeamId...)
		userIds = append(userIds, share.UserId...)
	}

	if len(teamIds) != 0 {
		teams, err := logic.FetchTeamName(ctx, teamIds)
		if err != nil {
			return xerrors.InternalServerError(err)
		}
		for id, name := range teams {
			rsp.Teams = append(rsp.Teams, &bffaipb.RspListDocShareConfigReceiverUserTeam_Members{
				Id:   id,
				Name: name,
			})
		}
	}

	if len(userIds) != 0 {
		users, err := logic.FetchUserName(ctx, userIds)
		if err != nil {
			return xerrors.InternalServerError(err)
		}
		for id, name := range users {
			rsp.Users = append(rsp.Users, &bffaipb.RspListDocShareConfigReceiverUserTeam_Members{
				Id:   id,
				Name: name,
			})
		}
	}

	return nil
}

// CreateDocShareConfigReceiverAssistant 创建助手接收方设置
func (a *Ai) CreateDocShareConfigReceiverAssistant(ctx context.Context, req *bffaipb.ReqCreateDocShareConfigReceiverAssistant,
	rsp *bffaipb.RspCreateDocShareConfigReceiverAssistant,
) error {
	// 从配置中读取固定的团队ID
	teamId := config.GetUint64("ai.share.team_id")

	reqSender := &aipb.ReqCreateDocShareConfigReceiverAssistant{
		CreateBy:      teamId,
		AdminType:     basepb.IdentityType_IDENTITY_TYPE_TEAM,
		AssistantId:   req.AssistantId,
		ReceiverState: req.ReceiverState,
		OtherState:    req.OtherState,
	}

	var userShareData []*aipb.DocReceiveConfigBySender
	for _, u := range req.UserShares {
		userShareData = append(userShareData, &aipb.DocReceiveConfigBySender{
			UserId: u.UserId,
			TeamId: u.TeamId,
			State:  u.State,
		})
	}

	reqSender.UserShares = userShareData

	_, err := client.AiClient.CreateDocShareConfigReceiverAssistant(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	return nil
}

// ListDocShareConfigReceiverAssistant 查询助手接收方设置
func (a *Ai) ListDocShareConfigReceiverAssistant(ctx context.Context, req *bffaipb.ReqListDocShareConfigReceiverAssistant,
	rsp *bffaipb.RspListDocShareConfigReceiverAssistant,
) error {
	// 从配置中读取固定的团队ID
	teamId := config.GetUint64("ai.share.team_id")

	reqSender := &aipb.ReqListDocShareConfigReceiverAssistant{
		AssistantId: req.AssistantId,
		CreateBy:    teamId,
		AdminType:   basepb.IdentityType_IDENTITY_TYPE_TEAM,
	}

	assistantReceiver, err := client.AiClient.ListDocShareConfigReceiverAssistant(ctx, reqSender)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	if assistantReceiver == nil {
		return nil
	}

	rsp.AssistantId = assistantReceiver.AssistantId
	rsp.ReceiverState = assistantReceiver.ReceiverState
	rsp.OtherState = assistantReceiver.OtherState

	userInfos, teamInfos, err := ailogic.ListDocShareConfigReceiverAssistantUserTeam(ctx, assistantReceiver)
	if err != nil {
		return err
	}

	for _, share := range assistantReceiver.UserShares {
		var uss []*bffaipb.RspListDocShareConfigReceiverAssistant_Members
		if len(share.UserId) > 0 {
			for _, u := range share.UserId {
				us := &bffaipb.RspListDocShareConfigReceiverAssistant_Members{}
				us.Id = u
				us.Name = userInfos[u]
				uss = append(uss, us)
			}
		}

		var teams []*bffaipb.RspListDocShareConfigReceiverAssistant_Members
		if len(share.TeamId) > 0 {
			for _, u := range share.TeamId {
				team := &bffaipb.RspListDocShareConfigReceiverAssistant_Members{}
				team.Id = u
				team.Name = teamInfos[u]
				teams = append(teams, team)
			}
		}

		userShare := &bffaipb.RspListDocShareConfigReceiverAssistant_UserShare{}
		userShare.State = share.State
		userShare.GroupId = share.GroupId

		userShare.Teams = teams
		userShare.Users = uss

		rsp.UserShares = append(rsp.UserShares, userShare)
	}

	return nil
}
