package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
)

// ListNebulaAssistants 获取知识星云助手列表
// mgmt 拥有所有助手权限，无需权限限制
func (a *Ai) ListNebulaAssistants(ctx context.Context, req *bffaipb.ReqListNebulaAssistants, rsp *bffaipb.RspListNebulaAssistants) error {
	scene := aipb.ReqGetAssistants_VISIBLE_SCENE_IN_NEBULA

	pbReq := &aipb.ReqGetAssistants{
		Filter: &aipb.ReqGetAssistants_Filter{
			IsDraft: basepb.BoolEnum_BOOL_ENUM_FALSE,
		},
		OrderBy: []*basepb.OrderBy{
			{Column: "update_date", Desc: true},
		},
		Page: &basepb.Paginator{
			Limit:  req.Limit,
			Offset: req.Offset,
		},
		Relation: &aipb.ReqGetAssistants_Relation{
			Collection: true,
		},
		WithTotalCount: true,
		VisibleScene:   scene,
	}

	// mgmt 不需要用户和团队过滤，可以查看所有助手
	result, err := client.AiClient.GetAssistants(ctx, pbReq)
	if err != nil {
		return err
	}

	for _, v := range result.Assistants {
		rsp.Assistants = append(rsp.Assistants, &aipb.AssistantV2{
			Id: v.Assistant.Id,
			Config: &aipb.AssistantConfig{
				Name:           v.Assistant.Config.Name,
				NameEn:         v.Assistant.Config.NameEn,
				CollectionLang: v.Assistant.Config.CollectionLang,
			},
		})
	}
	rsp.TotalCount = result.TotalCount

	return nil
}

// ListNebulaContributors 获取知识星云助手下的贡献者筛选项
// mgmt 拥有所有贡献者权限，无需权限限制
func (a *Ai) ListNebulaContributors(ctx context.Context, req *bffaipb.ReqListNebulaContributors, rsp *bffaipb.RspListNebulaContributors) error {
	pbReq := &aipb.ReqListContributor{
		WithAssistantId: true,
	}

	// 如果指定了助手ID，则按助手ID过滤
	if len(req.GetAssistantId()) > 0 {
		pbReq.Or = &aipb.ReqListContributor_OrGroup{
			ScopedAssistantId: req.GetAssistantId(),
		}
	}
	// mgmt 不需要贡献者权限限制，可以查看所有贡献者

	pbRsp, err := client.AiClient.ListContributor(ctx, pbReq)
	if err != nil {
		return err
	}

	rsp.Contributors = pbRsp.Contributors
	return nil
}
