package iam

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffiampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/iam"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
)

// DisableUser 冻结用户
func (i Iam) DisableUser(ctx context.Context, req *bffiampb.ReqDisableUser, rsp *bffiampb.RspDisableUser) error {
	// me := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	result, err := client.IamNational.DisableUser(ctx, &iampb.ReqDisableUser{
		UserId: req.UserId,
	})
	if err != nil {
		return err
	}

	// TODO 运营端操作日志

	results := make([]*bffiampb.RspDisableUser_Result, 0, len(result.Results))
	for _, item := range result.Results {
		results = append(results, &bffiampb.RspDisableUser_Result{
			Code: item.Code,
		})
	}

	rsp.Results = results

	return nil
}

// EnableUser 解冻用户
func (i Iam) EnableUser(ctx context.Context, req *bffiampb.ReqEnableUser, rsp *bffiampb.RspEnableUser) error {
	// me := xsession.UserFromContext[mgmtpb.OpUser](ctx)

	result, err := client.IamNational.EnableUser(ctx, &iampb.ReqEnableUser{
		UserId: req.UserId,
	})
	if err != nil {
		return err
	}

	// TODO 运营端操作日志

	results := make([]*bffiampb.RspEnableUser_Result, 0, len(result.Results))
	for _, item := range result.Results {
		results = append(results, &bffiampb.RspEnableUser_Result{
			Code: item.Code,
		})
	}

	rsp.Results = results

	return nil
}

// GetUserSelect 搜索用户选择
func (i Iam) GetUserSelect(ctx context.Context, req *bffiampb.ReqGetUserSelect, rsp *bffiampb.RspGetUserSelect) error {
	if req.Region == basepb.Region_REGION_UNSPECIFIED {
		req.Region = basepb.Region_REGION_NATIONAL
	}

	rpcReq := &iampb.ReqGetUserSelect{
		Region: req.Region,
		Page:   req.Page,
	}

	withoutFilter := false
	if req.Filter != nil {
		rpcReq.Filter = &iampb.ReqGetUserSelect_Filter{}
		if len(req.Filter.RegionCode) > 0 {
			rpcReq.Filter.RegionCode = req.Filter.RegionCode
		}
		if req.Filter.NickName != "" {
			withoutFilter = true
			rpcReq.Filter.NickName = req.Filter.NickName
		}
		if req.Filter.Username != "" {
			withoutFilter = true
			rpcReq.Filter.Username = req.Filter.Username
		}
	}
	if !withoutFilter && (req.Page == nil || req.Page.Limit == 0) {
		rpcReq.Page = &basepb.Paginator{Limit: 10}
	}

	rpcRsp, err := client.IamClientByRegion(req.Region).GetUserSelect(ctx, rpcReq)
	if err != nil {
		return err
	}
	if rpcRsp == nil || len(rpcRsp.Users) == 0 {
		return nil
	}
	rsp.Users = make([]*bffiampb.RspGetUserSelect_User, len(rpcRsp.Users))
	for j, user := range rpcRsp.Users {
		rsp.Users[j] = &bffiampb.RspGetUserSelect_User{
			Id:       user.Id,
			Username: user.Username,
			NickName: user.NickName,
		}
	}

	return nil
}
