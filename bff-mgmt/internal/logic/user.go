package logic

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mgmtpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	teampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
)

// InternationalMinUserID 国际用户ID最小值
const InternationalMinUserID = 200000000

// IsInternationalUser 是否为国际用户
func IsInternationalUser(userID uint64) bool {
	return userID >= InternationalMinUserID
}

// SplitUserIDs 将用户ID分割为国内和国际的
func SplitUserIDs(userIDs []uint64) (national, international []uint64) {
	for _, userID := range userIDs {
		if userID == 0 {
			continue
		}
		if IsInternationalUser(userID) {
			international = append(international, userID)
		} else {
			national = append(national, userID)
		}
	}
	return
}

// FetchUserName 通过ids获取门户端用户名
func FetchUserName(ctx context.Context, userIds []uint64) (map[uint64]string, error) {
	if len(userIds) == 0 {
		return nil, nil
	}

	userNames := make(map[uint64]string, len(userIds))

	users, err := client.IamNational.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{
		Id: userIds,
	})
	if err != nil {
		return nil, err
	}

	if len(users.UserSet) == 0 {
		return userNames, nil
	}

	for _, user := range users.UserSet {
		userNames[user.Id] = user.Username
	}

	return userNames, nil
}

// FetchOpUserName 通过ids获取运营端用户名
func FetchOpUserName(ctx context.Context, userIds []uint64) (map[uint64]string, error) {
	if len(userIds) == 0 {
		return nil, nil
	}

	userNames := make(map[uint64]string, len(userIds))

	users, err := client.MgmtClient.GetUsers(ctx, &mgmtpb.ReqGetUsers{
		Id: userIds,
	})
	if err != nil {
		return nil, err
	}

	if len(users.UserSet) == 0 {
		return userNames, nil
	}

	for _, user := range users.UserSet {
		userNames[user.Id] = user.Username
	}

	return userNames, nil
}

// FetchUserTeam 获取用户团队信息
func FetchUserTeam(ctx context.Context, userId uint64) (*teampb.TeamInfo, error) {
	teams, err := client.TeamClient.GetUserTeams(ctx, &teampb.ReqGetUserTeams{
		UserId: []uint64{userId},
	})
	if err != nil {
		return nil, err
	}
	return teams.GetTeams()[userId].GetTeamInfo(), nil
}

// FetchUserTeamName 获取用户对应团队简称
func FetchUserTeamName(ctx context.Context, userIds []uint64) (map[uint64]string, error) {
	teamNames := make(map[uint64]string, len(userIds))
	teams, err := client.TeamClient.GetUserTeams(ctx, &teampb.ReqGetUserTeams{
		UserId: userIds,
	})
	if err != nil {
		return nil, err
	}
	for user, v := range teams.GetTeams() {
		teamNames[user] = v.TeamInfo.ShortName
	}
	return teamNames, nil
}

// FetchTeamName 通过ids获取团队简称
func FetchTeamName(ctx context.Context, teamIds []uint64) (map[uint64]string, error) {
	if len(teamIds) == 0 {
		return nil, nil
	}

	teamNames := make(map[uint64]string, len(teamIds))
	teams, err := client.TeamClient.GetTeams(ctx, &teampb.ReqGetTeams{
		Filter: &teampb.ReqGetTeams_Filter{TeamId: teamIds},
	})
	if err != nil {
		return nil, err
	}
	for _, v := range teams.GetTeams() {
		teamNames[v.TeamInfo.Id] = v.TeamInfo.ShortName
	}
	return teamNames, nil
}

// FetchTeamInfo 通过ids获取团队信息
func FetchTeamInfo(ctx context.Context, teamIds []uint64) (map[uint64]*teampb.TeamInfo, error) {
	infos := make(map[uint64]*teampb.TeamInfo, len(teamIds))
	teams, err := client.TeamClient.GetTeams(ctx, &teampb.ReqGetTeams{
		Filter: &teampb.ReqGetTeams_Filter{TeamId: teamIds, WithDisbanded: true},
	})
	if err != nil {
		return nil, err
	}
	for _, v := range teams.GetTeams() {
		infos[v.TeamInfo.Id] = v.TeamInfo
	}
	return infos, nil
}

// GetUserIdByName 通过用户名获取用户id,完全匹配。没找到会返回 id = 0
func GetUserIdByName(ctx context.Context, userName string) (uint64, error) {
	// 先从国内查询
	rsp, err := client.IamNational.GetUserIds(ctx, &iampb.ReqGetUserIds{
		Username:   userName,
		ExactMatch: true,
	})
	if err != nil {
		return 0, err
	}
	if len(rsp.Ids) > 0 {
		return rsp.Ids[0], nil
	}

	// 国内未找到，再从国际查询
	rspIntl, err := client.IamInternational.GetUserIds(ctx, &iampb.ReqGetUserIds{
		Username:   userName,
		ExactMatch: true,
	})
	if err != nil {
		return 0, err
	}
	if len(rspIntl.Ids) > 0 {
		return rspIntl.Ids[0], nil
	}

	return 0, nil
}

// LoadIdentityNames 加载identity名称
func LoadIdentityNames(ctx context.Context, identities []*basepb.Identity) error {
	if len(identities) == 0 {
		return nil
	}

	var userIDs, teamIDs, opUserIDs []uint64
	for _, identity := range identities {
		switch identity.IdentityType {
		case basepb.IdentityType_IDENTITY_TYPE_USER:
			userIDs = append(userIDs, identity.IdentityId)
		case basepb.IdentityType_IDENTITY_TYPE_TEAM:
			teamIDs = append(teamIDs, identity.IdentityId)
		case basepb.IdentityType_IDENTITY_TYPE_MGMT:
			opUserIDs = append(opUserIDs, identity.IdentityId)
		}
	}

	users, err := FetchUserName(ctx, userIDs)
	if err != nil {
		return err
	}
	teams, err := FetchTeamName(ctx, teamIDs)
	if err != nil {
		return err
	}
	opUsers, err := FetchOpUserName(ctx, opUserIDs)
	if err != nil {
		return nil
	}

	for _, identity := range identities {
		switch identity.IdentityType {
		case basepb.IdentityType_IDENTITY_TYPE_USER:
			identity.Name = users[identity.IdentityId]
		case basepb.IdentityType_IDENTITY_TYPE_TEAM:
			identity.Name = teams[identity.IdentityId]
		case basepb.IdentityType_IDENTITY_TYPE_MGMT:
			identity.Name = opUsers[identity.IdentityId]
		}
	}

	return nil
}

// LoadUserCards 加载用户卡片
func LoadUserCards(ctx context.Context, userIDs []uint64) ([]*iampb.UserCard, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}

	users, err := GetUserByKeys(ctx, userIDs)
	if err != nil {
		return nil, err
	}

	cards := make([]*iampb.UserCard, 0, len(users))
	for _, user := range users {
		cards = append(cards, &iampb.UserCard{
			Id:       user.Id,
			Username: user.Username,
			Image:    user.Image,
			Level:    user.Level,
		})
	}

	return cards, nil
}

// GetUserByKeys 通过主键查询用户映射
func GetUserByKeys(ctx context.Context, userIDs []uint64) (map[uint64]*iampb.UserInfo, error) {
	national, international := SplitUserIDs(userIDs)

	errs := make([]error, 2)
	results := make([][]*iampb.UserInfo, 2)
	g := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))

	if len(national) > 0 {
		g.SafeGo(func(ctx context.Context) error {
			rsp, err := client.IamNational.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{
				Id: national,
			})
			if err != nil {
				errs[0] = err
				return err
			}

			results[0] = rsp.UserSet

			return nil
		})
	}

	if len(international) > 0 {
		g.SafeGo(func(ctx context.Context) error {
			rsp, err := client.IamInternational.GetUsersByKey(ctx, &iampb.ReqGetUsersByKey{
				Id: international,
			})
			if err != nil {
				errs[1] = err
				return err
			}

			results[1] = rsp.UserSet

			return nil
		})
	}

	g.Wait()

	for _, err := range errs {
		if err != nil {
			return nil, err
		}
	}

	users := make(map[uint64]*iampb.UserInfo, len(results[0])+len(results[1]))
	for _, result := range results {
		for _, user := range result {
			users[user.Id] = user
		}
	}

	return users, nil
}
