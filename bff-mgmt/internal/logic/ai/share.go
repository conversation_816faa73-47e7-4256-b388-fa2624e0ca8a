package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"golang.org/x/sync/errgroup"
)

// ListAssistantCanShareDoc 获取可分享的助手列表
func ListAssistantCanShareDoc(ctx context.Context, createBy uint64, name, lang string) (*aipb.RspListAssistantCanShareDoc, error) {
	reqSender := &aipb.ReqListAssistantCanShareDoc{
		CreateBy:  createBy,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
		Name:      name,
		Language:  lang,
	}

	sharedAssistant, err := client.AiClient.ListAssistantCanShareDoc(ctx, reqSender)
	if err != nil {
		return nil, err
	}

	return sharedAssistant, err
}

// ListUserCanShareDoc 获取可分享的个人列表
func ListUserCanShareDoc(ctx context.Context, createBy uint64, userName string, docId uint64) (*aipb.RspListUserCanShareDoc, error) {
	// 通过用户名获取用户ID
	userToShare, err := logic.GetUserIdByName(ctx, userName)
	if err != nil {
		return nil, err
	}
	// 没有找到用户
	if userToShare == 0 {
		return &aipb.RspListUserCanShareDoc{}, nil
	}

	reqSender := &aipb.ReqListUserCanShareDoc{
		CreateBy:  createBy,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
		UserId:    userToShare,
		DocId:     docId,
	}

	userList, err := client.AiClient.ListUserCanShareDoc(ctx, reqSender)
	if err != nil {
		return nil, err
	}

	return userList, nil
}

// ListTeamCanShareDoc 获取可分享的团队列表
func ListTeamCanShareDoc(ctx context.Context, createBy uint64, name string, offset, limit, docId uint64) (*aipb.RspListTeamCanShareDoc, error) {
	reqSender := &aipb.ReqListTeamCanShareDoc{
		CreateBy:  createBy,
		AdminType: basepb.IdentityType_IDENTITY_TYPE_TEAM,
		Name:      name,
		Offset:    offset,
		Limit:     limit,
		DocId:     docId,
	}

	teamList, err := client.AiClient.ListTeamCanShareDoc(ctx, reqSender)
	if err != nil {
		return nil, err
	}

	return teamList, nil
}

// filterZero 过滤掉0值
func filterZero(ids []uint64) []uint64 {
	var result []uint64
	for _, id := range ids {
		if id != 0 {
			result = append(result, id)
		}
	}
	return result
}

// ListDocShareConfigReceiverAssistantUserTeam 获取助手接收方设置的用户、团队信息
/*
  @return: userNames, teamNames, error
*/
func ListDocShareConfigReceiverAssistantUserTeam(ctx context.Context, assistantReceiver *aipb.RspListDocShareConfigReceiverAssistant) (map[uint64]string,
	map[uint64]string, error,
) {
	var err error
	var teamIds []uint64
	var userIds []uint64

	var teamNames map[uint64]string
	var userNames map[uint64]string

	for _, receiver := range assistantReceiver.UserShares {
		teamIds = append(teamIds, receiver.TeamId...)
		userIds = append(userIds, receiver.UserId...)
	}

	teamIds = filterZero(teamIds)
	userIds = filterZero(userIds)

	wg, ctx := errgroup.WithContext(ctx)
	if len(userIds) > 0 {
		wg.Go(func() error {
			userNames, err = logic.FetchUserName(ctx, userIds)
			if err != nil {
				return err
			}
			return nil
		})
	}

	if len(teamIds) > 0 {
		wg.Go(func() error {
			teamNames, err = logic.FetchTeamName(ctx, teamIds)
			if err != nil {
				return err
			}
			return nil
		})
	}

	err = wg.Wait()
	if err != nil {
		return nil, nil, err
	}

	return userNames, teamNames, err
}
