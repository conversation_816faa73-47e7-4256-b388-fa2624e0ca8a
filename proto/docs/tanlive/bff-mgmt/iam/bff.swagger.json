{"swagger": "2.0", "info": {"title": "tanlive/bff-mgmt/iam/bff.proto", "version": "version not set"}, "tags": [{"name": "IamBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/iam/disable_user": {"post": {"summary": "冻结用户", "operationId": "IamBff_DisableUser", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspDisableUser"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqDisableUser"}}], "tags": ["IamBff"]}}, "/iam/enable_user": {"post": {"summary": "解冻用户", "operationId": "IamBff_EnableUser", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspEnableUser"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqEnableUser"}}], "tags": ["IamBff"]}}, "/iam/get_user_select": {"post": {"summary": "搜索用户选择", "operationId": "IamBff_GetUserSelect", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspGetUserSelect"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqGetUserSelect"}}], "tags": ["IamBff"]}}}, "definitions": {"ReqGetUserSelectFilter": {"type": "object", "properties": {"region_code": {"type": "array", "items": {"type": "string"}, "title": "国家或地区编码"}, "username": {"type": "string", "title": "搜索用户名"}, "nick_name": {"type": "string", "title": "搜索昵称"}}}, "RspGetUserSelectUser": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "username": {"type": "string"}, "nick_name": {"type": "string"}}}, "basePaginator": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64", "title": "便宜量"}, "limit": {"type": "integer", "format": "int64", "title": "页面大小"}}, "title": "分页器"}, "baseRegion": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 国内\n - 2: 海外", "title": "地域"}, "iamReqDisableUser": {"type": "object", "properties": {"user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "用户ID"}}}, "iamReqEnableUser": {"type": "object", "properties": {"user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "用户ID"}}}, "iamReqGetUserSelect": {"type": "object", "properties": {"region": {"$ref": "#/definitions/baseRegion", "title": "地域"}, "filter": {"$ref": "#/definitions/ReqGetUserSelectFilter", "title": "过滤器"}, "page": {"$ref": "#/definitions/basePaginator", "title": "分页器 limit = 0 时查询全部"}}}, "iamRspDisableUser": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamRspDisableUserResult"}}}}, "iamRspDisableUserResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}}}, "iamRspEnableUser": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamRspEnableUserResult"}}}}, "iamRspEnableUserResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}}}, "iamRspGetUserSelect": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspGetUserSelectUser"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}