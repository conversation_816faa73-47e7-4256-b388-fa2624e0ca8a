syntax = "proto3";

package tanlive.bff_mgmt.iam;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-mgmt/iam";

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "tanlive/options.proto";
import "tanlive/base/ugc.proto";
import "tanlive/base/base.proto";

// IAM BFF
service IamBff {
  option (tanlive.bff) = true;

  // 冻结用户
  rpc DisableUser(ReqDisableUser) returns (RspDisableUser){
    option (google.api.http) = {
      post: "/iam/disable_user"
      body: "*"
    };
  }

  // 解冻用户
  rpc EnableUser(ReqEnableUser) returns (RspEnableUser){
    option (google.api.http) = {
      post: "/iam/enable_user"
      body: "*"
    };
  }

  // 搜索用户选择
  rpc GetUserSelect(ReqGetUserSelect) returns (RspGetUserSelect){
    option (google.api.http) = {
      post: "/iam/get_user_select"
      body: "*"
    };
  }
}

message ReqDisableUser {
  // 用户ID
  repeated uint64 user_id = 1 [(validator) = "required,dive,required"];
}

message RspDisableUser {
  message Result {
    int32 code = 1;
  }
  repeated Result results = 1;
}

message ReqEnableUser {
  // 用户ID
  repeated uint64 user_id = 1 [(validator) = "required,dive,required"];
}

message RspEnableUser {
  message Result {
    int32 code = 1;
  }
  repeated Result results = 1;
}


message ReqGetUserSelect {
  message Filter {
    // 国家或地区编码
    repeated string region_code = 1;
    // 搜索用户名
    string username = 2;
    // 搜索昵称
    string nick_name = 3;
  }
  // 地域
  base.Region region = 1;
  // 过滤器
  Filter filter = 2;
  // 分页器 limit = 0 时查询全部
  base.Paginator page = 3;
}

message RspGetUserSelect {
  message User {
    uint64 id = 1;
    string username = 2;
    string nick_name = 3;
  }
  repeated User users = 1;
}