// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package iam

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	proto "google.golang.org/protobuf/proto"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required,dive,required",
	}, &ReqDisableUser{})
}

func (x *ReqDisableUser) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required,dive,required",
	}, &ReqEnableUser{})
}

func (x *ReqEnableUser) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *RspDisableUser) MaskInLog() any {
	if x == nil {
		return (*RspDisableUser)(nil)
	}

	y := proto.Clone(x).(*RspDisableUser)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspDisableUser_Result)
		}
	}

	return y
}

func (x *RspDisableUser) MaskInRpc() any {
	if x == nil {
		return (*RspDisableUser)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspDisableUser_Result)
		}
	}

	return y
}

func (x *RspDisableUser) MaskInBff() any {
	if x == nil {
		return (*RspDisableUser)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspDisableUser_Result)
		}
	}

	return y
}

func (x *RspEnableUser) MaskInLog() any {
	if x == nil {
		return (*RspEnableUser)(nil)
	}

	y := proto.Clone(x).(*RspEnableUser)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspEnableUser_Result)
		}
	}

	return y
}

func (x *RspEnableUser) MaskInRpc() any {
	if x == nil {
		return (*RspEnableUser)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspEnableUser_Result)
		}
	}

	return y
}

func (x *RspEnableUser) MaskInBff() any {
	if x == nil {
		return (*RspEnableUser)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspEnableUser_Result)
		}
	}

	return y
}

func (x *ReqGetUserSelect) MaskInLog() any {
	if x == nil {
		return (*ReqGetUserSelect)(nil)
	}

	y := proto.Clone(x).(*ReqGetUserSelect)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqGetUserSelect_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqGetUserSelect) MaskInRpc() any {
	if x == nil {
		return (*ReqGetUserSelect)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqGetUserSelect_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqGetUserSelect) MaskInBff() any {
	if x == nil {
		return (*ReqGetUserSelect)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqGetUserSelect_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *RspGetUserSelect) MaskInLog() any {
	if x == nil {
		return (*RspGetUserSelect)(nil)
	}

	y := proto.Clone(x).(*RspGetUserSelect)
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*RspGetUserSelect_User)
		}
	}

	return y
}

func (x *RspGetUserSelect) MaskInRpc() any {
	if x == nil {
		return (*RspGetUserSelect)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*RspGetUserSelect_User)
		}
	}

	return y
}

func (x *RspGetUserSelect) MaskInBff() any {
	if x == nil {
		return (*RspGetUserSelect)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*RspGetUserSelect_User)
		}
	}

	return y
}

func (x *RspDisableUser) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspEnableUser) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetUserSelect) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetUserSelect) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

type IamBffHandler interface {
	// 冻结用户
	DisableUser(context.Context, *ReqDisableUser, *RspDisableUser) error
	// 解冻用户
	EnableUser(context.Context, *ReqEnableUser, *RspEnableUser) error
	// 搜索用户选择
	GetUserSelect(context.Context, *ReqGetUserSelect, *RspGetUserSelect) error
}

func RegisterIamBff(s bff.Server, h IamBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Iam"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/iam/disable_user", h.DisableUser).Name("DisableUser")
	bff.AddRoute(group, http.MethodPost, "/iam/enable_user", h.EnableUser).Name("EnableUser")
	bff.AddRoute(group, http.MethodPost, "/iam/get_user_select", h.GetUserSelect).Name("GetUserSelect")
	return group
}
