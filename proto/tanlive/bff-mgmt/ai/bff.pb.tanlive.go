// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package ai

import (
	context "context"
	bff "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	xhttp "e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	ai "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iam "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mgmt "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	team "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/team"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	http "net/http"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Text":        "required",
		"QuestionId":  "required",
		"AssistantId": "required",
		"Threshold":   "min=0,max=1",
		"Temperature": "min=0,max=2",
	}, &ReqSearchChatStream{})
}

func (x *ReqSearchChatStream) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Urls": "required",
	}, &ReqProxyChatHtmlUrl{})
}

func (x *ReqProxyChatHtmlUrl) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Ids": "required,min=1",
	}, &ReqReparseTextFiles{})
}

func (x *ReqReparseTextFiles) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id": "required",
	}, &ReqUpdateObjectCustomLabels{})
}

func (x *ReqUpdateObjectCustomLabels) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Language": "omitempty,oneof=zh en",
	}, &ReqListAssistant{})
}

func (x *ReqListAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Language": "omitempty,oneof=zh en",
	}, &ReqListTanliveAssistant{})
}

func (x *ReqListTanliveAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ObjectType": "oneof=1 2 3",
	}, &ReqModifyCustomLabels{})
}

func (x *ReqModifyCustomLabels) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ObjectType": "oneof=1 2 3",
	}, &ReqListCustomLabel{})
}

func (x *ReqListCustomLabel) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id":     "required",
		"Region": "required",
	}, &ReqGetChatMessageDetail{})
}

func (x *ReqGetChatMessageDetail) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Search":      "required",
		"AssistantId": "required",
	}, &ReqSearchCollection{})
}

func (x *ReqSearchCollection) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Contributor": "omitempty,dive",
		"Id":          "required",
	}, &ReqUpdateQA{})
}

func (x *ReqUpdateQA) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required,max=200,dive",
	}, &ReqUpdateQAs{})
}

func (x *ReqUpdateQAs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Contributor": "omitempty,dive",
		"State":       "omitempty,oneof=1 2",
	}, &ReqCreateQA{})
}

func (x *ReqCreateQA) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required,max=200,dive",
	}, &ReqCreateQAs{})
}

func (x *ReqCreateQAs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id":       "required",
		"FileName": "omitempty,max=4096",
	}, &ReqUpdateTextFile{})
}

func (x *ReqUpdateTextFile) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required,max=200,dive",
	}, &ReqUpdateTextFiles{})
}

func (x *ReqUpdateTextFiles) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required,max=1000,dive",
	}, &ReqCreateTextFiles{})
}

func (x *ReqCreateTextFiles) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"FileName":    "required",
		"Contributor": "omitempty,dive",
		"Type":        "omitempty,oneof=2 3",
	}, &ReqCreateTextFiles_Item{})
}

func (x *ReqCreateTextFiles_Item) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required,dive",
	}, &ReqCreateSystemDocCopy{})
}

func (x *ReqCreateSystemDocCopy) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required",
	}, &ReqCreateSystemDocCopy_Item{})
}

func (x *ReqCreateSystemDocCopy_Item) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required,dive,required",
	}, &ReqEnableSystemDoc{})
}

func (x *ReqEnableSystemDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required,dive,required",
	}, &ReqDisableSystemDoc{})
}

func (x *ReqDisableSystemDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required,dive,required",
	}, &ReqDeleteSystemDoc{})
}

func (x *ReqDeleteSystemDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AnswerId":     "required_without=FeedbackId",
		"AnswerRating": "required",
		"MgmtFeedback": "required",
		"FeedbackId":   "required_without=AnswerId",
		"MgmtDocId":    "omitempty,dive,required",
	}, &ReqUpsertMgmtFeedback{})
}

func (x *ReqUpsertMgmtFeedback) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Region": "required",
	}, &ReqGetFeedbacks{})
}

func (x *ReqGetFeedbacks) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"FeedbackId": "required",
	}, &ReqFindFeedback{})
}

func (x *ReqFindFeedback) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Region": "required",
	}, &ReqGetFeedbackLogs{})
}

func (x *ReqGetFeedbackLogs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Conf": "required",
	}, &ReqUpdateChatNoticeConf{})
}

func (x *ReqUpdateChatNoticeConf) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Region": "required",
	}, &ReqDescribeChatRegionCode{})
}

func (x *ReqDescribeChatRegionCode) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Region": "required",
	}, &ReqDescribeFeedbackRegionCode{})
}

func (x *ReqDescribeFeedbackRegionCode) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Filter": "required",
	}, &ReqListChat{})
}

func (x *ReqListChat) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Region": "required",
	}, &ReqListChat_Filter{})
}

func (x *ReqListChat_Filter) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id":     "min=1",
		"Region": "required",
	}, &ReqGetChatDetail{})
}

func (x *ReqGetChatDetail) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Text":        "required",
		"QuestionId":  "required",
		"AssistantId": "required",
		"Threshold":   "min=0,max=1",
		"Temperature": "min=0,max=2",
	}, &ReqSearchChat{})
}

func (x *ReqSearchChat) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Message": "required",
		"UserId":  "required",
	}, &RspSearchChat{})
}

func (x *RspSearchChat) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id": "required",
	}, &ReqCloneDoc{})
}

func (x *ReqCloneDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"State": "required,oneof=1 2",
	}, &ReqOnOffDocs{})
}

func (x *ReqOnOffDocs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ChatId": "required",
		"Region": "required",
	}, &ReqListChatLiveAgent{})
}

func (x *ReqListChatLiveAgent) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"LiveAgentId": "required",
		"ChatId":      "required",
		"Region":      "required",
	}, &ReqSwitchChatLiveAgent{})
}

func (x *ReqSwitchChatLiveAgent) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Language": "omitempty,oneof=zh en",
	}, &ReqListAssistantCanShareDoc{})
}

func (x *ReqListAssistantCanShareDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Language": "omitempty,oneof=zh en",
	}, &ReqListeDocShareConfigSender{})
}

func (x *ReqListeDocShareConfigSender) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Name":  "required",
		"Limit": "min=1,max=200",
	}, &ReqListTeamCanShareDoc{})
}

func (x *ReqListTeamCanShareDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Name": "required",
	}, &ReqListUserCanShareDoc{})
}

func (x *ReqListUserCanShareDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Config": "required",
	}, &ReqCreateAssistant{})
}

func (x *ReqCreateAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Configs": "required,dive,required",
	}, &ReqBatchCreateAssistant{})
}

func (x *ReqBatchCreateAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId": "required",
		"Config":      "required",
		"Mask":        "required",
	}, &ReqUpdateAssistant{})
}

func (x *ReqUpdateAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items": "required,dive,required",
	}, &ReqBatchUpdateAssistant{})
}

func (x *ReqBatchUpdateAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Config": "required",
		"Mask":   "required",
	}, &ReqBatchUpdateAssistant_Item{})
}

func (x *ReqBatchUpdateAssistant_Item) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId": "required",
		"Action":      "omitempty,dive,required",
	}, &ReqGetAssistantLogsPage{})
}

func (x *ReqGetAssistantLogsPage) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId":    "required",
		"AutoPara": "required",
	}, &ReqAutoChunkDoc{})
}

func (x *ReqAutoChunkDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId":      "required",
		"ManualPara": "required",
	}, &ReqManualChunkDoc{})
}

func (x *ReqManualChunkDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required",
	}, &ReqGetDocChunks{})
}

func (x *ReqGetDocChunks) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required,dive,required",
	}, &ReqGetChunkDocTasks{})
}

func (x *ReqGetChunkDocTasks) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required",
	}, &ReqGetDocEmbeddingModels{})
}

func (x *ReqGetDocEmbeddingModels) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Mask": "required",
	}, &ReqBatchUpdateDocAttr{})
}

func (x *ReqBatchUpdateDocAttr) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Contributor":      "omitempty,dive",
		"AssistantId":      "omitempty",
		"ShareAssistantId": "omitempty",
	}, &ReqImportQA{})
}

func (x *ReqImportQA) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *RspDescribeChatSuggestLog) MaskInLog() any {
	if x == nil {
		return (*RspDescribeChatSuggestLog)(nil)
	}

	y := proto.Clone(x).(*RspDescribeChatSuggestLog)
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Logs[k] = vv.MaskInLog().(*ai.ChatSuggestLog)
		}
	}

	return y
}

func (x *RspDescribeChatSuggestLog) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeChatSuggestLog)(nil)
	}

	y := x
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Logs[k] = vv.MaskInRpc().(*ai.ChatSuggestLog)
		}
	}

	return y
}

func (x *RspDescribeChatSuggestLog) MaskInBff() any {
	if x == nil {
		return (*RspDescribeChatSuggestLog)(nil)
	}

	y := x
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Logs[k] = vv.MaskInBff().(*ai.ChatSuggestLog)
		}
	}

	return y
}

func (x *RspProxyChatHtmlUrl) MaskInLog() any {
	if x == nil {
		return (*RspProxyChatHtmlUrl)(nil)
	}

	y := proto.Clone(x).(*RspProxyChatHtmlUrl)
	for k, v := range y.Contents {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contents[k] = vv.MaskInLog().(*RspProxyChatHtmlUrl_Content)
		}
	}

	return y
}

func (x *RspProxyChatHtmlUrl) MaskInRpc() any {
	if x == nil {
		return (*RspProxyChatHtmlUrl)(nil)
	}

	y := x
	for k, v := range y.Contents {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contents[k] = vv.MaskInRpc().(*RspProxyChatHtmlUrl_Content)
		}
	}

	return y
}

func (x *RspProxyChatHtmlUrl) MaskInBff() any {
	if x == nil {
		return (*RspProxyChatHtmlUrl)(nil)
	}

	y := x
	for k, v := range y.Contents {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contents[k] = vv.MaskInBff().(*RspProxyChatHtmlUrl_Content)
		}
	}

	return y
}

func (x *RspGetTextFile) MaskInLog() any {
	if x == nil {
		return (*RspGetTextFile)(nil)
	}

	y := proto.Clone(x).(*RspGetTextFile)
	if v, ok := any(y.Item).(interface{ MaskInLog() any }); ok {
		y.Item = v.MaskInLog().(*CollectionTextFile)
	}

	return y
}

func (x *RspGetTextFile) MaskInRpc() any {
	if x == nil {
		return (*RspGetTextFile)(nil)
	}

	y := x
	if v, ok := any(y.Item).(interface{ MaskInRpc() any }); ok {
		y.Item = v.MaskInRpc().(*CollectionTextFile)
	}

	return y
}

func (x *RspGetTextFile) MaskInBff() any {
	if x == nil {
		return (*RspGetTextFile)(nil)
	}

	y := x
	if v, ok := any(y.Item).(interface{ MaskInBff() any }); ok {
		y.Item = v.MaskInBff().(*CollectionTextFile)
	}

	return y
}

func (x *ReqCreateDocQuery) MaskInLog() any {
	if x == nil {
		return (*ReqCreateDocQuery)(nil)
	}

	y := proto.Clone(x).(*ReqCreateDocQuery)
	if v, ok := any(y.Doc).(interface{ MaskInLog() any }); ok {
		y.Doc = v.MaskInLog().(*ReqListTextFiles)
	}
	if v, ok := any(y.Qa).(interface{ MaskInLog() any }); ok {
		y.Qa = v.MaskInLog().(*ReqListQA)
	}

	return y
}

func (x *ReqCreateDocQuery) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateDocQuery)(nil)
	}

	y := x
	if v, ok := any(y.Doc).(interface{ MaskInRpc() any }); ok {
		y.Doc = v.MaskInRpc().(*ReqListTextFiles)
	}
	if v, ok := any(y.Qa).(interface{ MaskInRpc() any }); ok {
		y.Qa = v.MaskInRpc().(*ReqListQA)
	}

	return y
}

func (x *ReqCreateDocQuery) MaskInBff() any {
	if x == nil {
		return (*ReqCreateDocQuery)(nil)
	}

	y := x
	if v, ok := any(y.Doc).(interface{ MaskInBff() any }); ok {
		y.Doc = v.MaskInBff().(*ReqListTextFiles)
	}
	if v, ok := any(y.Qa).(interface{ MaskInBff() any }); ok {
		y.Qa = v.MaskInBff().(*ReqListQA)
	}

	return y
}

func (x *ReqUpdateObjectCustomLabels) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateObjectCustomLabels)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateObjectCustomLabels)
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateObjectCustomLabels) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateObjectCustomLabels)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateObjectCustomLabels) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateObjectCustomLabels)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *RspListAssistant) MaskInLog() any {
	if x == nil {
		return (*RspListAssistant)(nil)
	}

	y := proto.Clone(x).(*RspListAssistant)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.Assistant)
		}
	}

	return y
}

func (x *RspListAssistant) MaskInRpc() any {
	if x == nil {
		return (*RspListAssistant)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.Assistant)
		}
	}

	return y
}

func (x *RspListAssistant) MaskInBff() any {
	if x == nil {
		return (*RspListAssistant)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.Assistant)
		}
	}

	return y
}

func (x *RspListTanliveAssistant) MaskInLog() any {
	if x == nil {
		return (*RspListTanliveAssistant)(nil)
	}

	y := proto.Clone(x).(*RspListTanliveAssistant)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.Assistant)
		}
	}

	return y
}

func (x *RspListTanliveAssistant) MaskInRpc() any {
	if x == nil {
		return (*RspListTanliveAssistant)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.Assistant)
		}
	}

	return y
}

func (x *RspListTanliveAssistant) MaskInBff() any {
	if x == nil {
		return (*RspListTanliveAssistant)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.Assistant)
		}
	}

	return y
}

func (x *ReqModifyCustomLabels) MaskInLog() any {
	if x == nil {
		return (*ReqModifyCustomLabels)(nil)
	}

	y := proto.Clone(x).(*ReqModifyCustomLabels)
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqModifyCustomLabels) MaskInRpc() any {
	if x == nil {
		return (*ReqModifyCustomLabels)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *ReqModifyCustomLabels) MaskInBff() any {
	if x == nil {
		return (*ReqModifyCustomLabels)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *RspListCustomLabel) MaskInLog() any {
	if x == nil {
		return (*RspListCustomLabel)(nil)
	}

	y := proto.Clone(x).(*RspListCustomLabel)
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *RspListCustomLabel) MaskInRpc() any {
	if x == nil {
		return (*RspListCustomLabel)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *RspListCustomLabel) MaskInBff() any {
	if x == nil {
		return (*RspListCustomLabel)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}

	return y
}

func (x *RspListDocByRef) MaskInLog() any {
	if x == nil {
		return (*RspListDocByRef)(nil)
	}

	y := proto.Clone(x).(*RspListDocByRef)
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*RspListDocByRef_Doc)
		}
	}

	return y
}

func (x *RspListDocByRef) MaskInRpc() any {
	if x == nil {
		return (*RspListDocByRef)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*RspListDocByRef_Doc)
		}
	}

	return y
}

func (x *RspListDocByRef) MaskInBff() any {
	if x == nil {
		return (*RspListDocByRef)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*RspListDocByRef_Doc)
		}
	}

	return y
}

func (x *RspListDocByRef_Doc) MaskInLog() any {
	if x == nil {
		return (*RspListDocByRef_Doc)(nil)
	}

	y := proto.Clone(x).(*RspListDocByRef_Doc)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*Operator)
	}

	return y
}

func (x *RspListDocByRef_Doc) MaskInRpc() any {
	if x == nil {
		return (*RspListDocByRef_Doc)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*Operator)
	}

	return y
}

func (x *RspListDocByRef_Doc) MaskInBff() any {
	if x == nil {
		return (*RspListDocByRef_Doc)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*Operator)
	}

	return y
}

func (x *RspGetChatMessageDetail) MaskInLog() any {
	if x == nil {
		return (*RspGetChatMessageDetail)(nil)
	}

	y := proto.Clone(x).(*RspGetChatMessageDetail)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ai.EventChatMessage)
	}
	for k, v := range y.CollectionItems {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.CollectionItems[k] = vv.MaskInLog().(*SearchCollectionItem)
		}
	}
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Logs[k] = vv.MaskInLog().(*ai.ChatMessageLog)
		}
	}
	if v, ok := any(y.CollectionTime).(interface{ MaskInLog() any }); ok {
		y.CollectionTime = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *RspGetChatMessageDetail) MaskInRpc() any {
	if x == nil {
		return (*RspGetChatMessageDetail)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ai.EventChatMessage)
	}
	for k, v := range y.CollectionItems {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.CollectionItems[k] = vv.MaskInRpc().(*SearchCollectionItem)
		}
	}
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Logs[k] = vv.MaskInRpc().(*ai.ChatMessageLog)
		}
	}
	if v, ok := any(y.CollectionTime).(interface{ MaskInRpc() any }); ok {
		y.CollectionTime = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *RspGetChatMessageDetail) MaskInBff() any {
	if x == nil {
		return (*RspGetChatMessageDetail)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ai.EventChatMessage)
	}
	for k, v := range y.CollectionItems {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.CollectionItems[k] = vv.MaskInBff().(*SearchCollectionItem)
		}
	}
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Logs[k] = vv.MaskInBff().(*ai.ChatMessageLog)
		}
	}
	if v, ok := any(y.CollectionTime).(interface{ MaskInBff() any }); ok {
		y.CollectionTime = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *ReqValidateQAs) MaskInLog() any {
	if x == nil {
		return (*ReqValidateQAs)(nil)
	}

	y := proto.Clone(x).(*ReqValidateQAs)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqValidateQAs_Item)
		}
	}

	return y
}

func (x *ReqValidateQAs) MaskInRpc() any {
	if x == nil {
		return (*ReqValidateQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqValidateQAs_Item)
		}
	}

	return y
}

func (x *ReqValidateQAs) MaskInBff() any {
	if x == nil {
		return (*ReqValidateQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqValidateQAs_Item)
		}
	}

	return y
}

func (x *ReqValidateQAs_Item) MaskInLog() any {
	if x == nil {
		return (*ReqValidateQAs_Item)(nil)
	}

	y := proto.Clone(x).(*ReqValidateQAs_Item)
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}

	return y
}

func (x *ReqValidateQAs_Item) MaskInRpc() any {
	if x == nil {
		return (*ReqValidateQAs_Item)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}

	return y
}

func (x *ReqValidateQAs_Item) MaskInBff() any {
	if x == nil {
		return (*ReqValidateQAs_Item)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspListCollectionFileName) MaskInLog() any {
	if x == nil {
		return (*RspListCollectionFileName)(nil)
	}

	y := proto.Clone(x).(*RspListCollectionFileName)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*RspListCollectionFileName_Item)
		}
	}

	return y
}

func (x *RspListCollectionFileName) MaskInRpc() any {
	if x == nil {
		return (*RspListCollectionFileName)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*RspListCollectionFileName_Item)
		}
	}

	return y
}

func (x *RspListCollectionFileName) MaskInBff() any {
	if x == nil {
		return (*RspListCollectionFileName)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*RspListCollectionFileName_Item)
		}
	}

	return y
}

func (x *RspListCollection) MaskInLog() any {
	if x == nil {
		return (*RspListCollection)(nil)
	}

	y := proto.Clone(x).(*RspListCollection)
	for k, v := range y.Collections {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Collections[k] = vv.MaskInLog().(*ai.Collection)
		}
	}

	return y
}

func (x *RspListCollection) MaskInRpc() any {
	if x == nil {
		return (*RspListCollection)(nil)
	}

	y := x
	for k, v := range y.Collections {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Collections[k] = vv.MaskInRpc().(*ai.Collection)
		}
	}

	return y
}

func (x *RspListCollection) MaskInBff() any {
	if x == nil {
		return (*RspListCollection)(nil)
	}

	y := x
	for k, v := range y.Collections {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Collections[k] = vv.MaskInBff().(*ai.Collection)
		}
	}

	return y
}

func (x *RspListContributor) MaskInLog() any {
	if x == nil {
		return (*RspListContributor)(nil)
	}

	y := proto.Clone(x).(*RspListContributor)
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributors[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspListContributor) MaskInRpc() any {
	if x == nil {
		return (*RspListContributor)(nil)
	}

	y := x
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributors[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspListContributor) MaskInBff() any {
	if x == nil {
		return (*RspListContributor)(nil)
	}

	y := x
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributors[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspListOperator) MaskInLog() any {
	if x == nil {
		return (*RspListOperator)(nil)
	}

	y := proto.Clone(x).(*RspListOperator)
	for k, v := range y.Operators {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Operators[k] = vv.MaskInLog().(*Operator)
		}
	}

	return y
}

func (x *RspListOperator) MaskInRpc() any {
	if x == nil {
		return (*RspListOperator)(nil)
	}

	y := x
	for k, v := range y.Operators {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Operators[k] = vv.MaskInRpc().(*Operator)
		}
	}

	return y
}

func (x *RspListOperator) MaskInBff() any {
	if x == nil {
		return (*RspListOperator)(nil)
	}

	y := x
	for k, v := range y.Operators {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Operators[k] = vv.MaskInBff().(*Operator)
		}
	}

	return y
}

func (x *RspValidateQAs) MaskInLog() any {
	if x == nil {
		return (*RspValidateQAs)(nil)
	}

	y := proto.Clone(x).(*RspValidateQAs)
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Errors[k] = vv.MaskInLog().(*RspValidateQAs_Err)
		}
	}

	return y
}

func (x *RspValidateQAs) MaskInRpc() any {
	if x == nil {
		return (*RspValidateQAs)(nil)
	}

	y := x
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Errors[k] = vv.MaskInRpc().(*RspValidateQAs_Err)
		}
	}

	return y
}

func (x *RspValidateQAs) MaskInBff() any {
	if x == nil {
		return (*RspValidateQAs)(nil)
	}

	y := x
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Errors[k] = vv.MaskInBff().(*RspValidateQAs_Err)
		}
	}

	return y
}

func (x *SearchCollectionItem) MaskInLog() any {
	if x == nil {
		return (*SearchCollectionItem)(nil)
	}

	y := proto.Clone(x).(*SearchCollectionItem)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*Operator)
	}

	return y
}

func (x *SearchCollectionItem) MaskInRpc() any {
	if x == nil {
		return (*SearchCollectionItem)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*Operator)
	}

	return y
}

func (x *SearchCollectionItem) MaskInBff() any {
	if x == nil {
		return (*SearchCollectionItem)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*Operator)
	}

	return y
}

func (x *RspSearchCollection) MaskInLog() any {
	if x == nil {
		return (*RspSearchCollection)(nil)
	}

	y := proto.Clone(x).(*RspSearchCollection)
	if v, ok := any(y.Start).(interface{ MaskInLog() any }); ok {
		y.Start = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.End).(interface{ MaskInLog() any }); ok {
		y.End = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Item {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Item[k] = vv.MaskInLog().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *RspSearchCollection) MaskInRpc() any {
	if x == nil {
		return (*RspSearchCollection)(nil)
	}

	y := x
	if v, ok := any(y.Start).(interface{ MaskInRpc() any }); ok {
		y.Start = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.End).(interface{ MaskInRpc() any }); ok {
		y.End = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Item {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Item[k] = vv.MaskInRpc().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *RspSearchCollection) MaskInBff() any {
	if x == nil {
		return (*RspSearchCollection)(nil)
	}

	y := x
	if v, ok := any(y.Start).(interface{ MaskInBff() any }); ok {
		y.Start = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.End).(interface{ MaskInBff() any }); ok {
		y.End = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Item {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Item[k] = vv.MaskInBff().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *ReqUpdateQA) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateQA)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateQA)
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.States[k] = vv.MaskInLog().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqUpdateQA) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.States[k] = vv.MaskInRpc().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqUpdateQA) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.States[k] = vv.MaskInBff().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqUpdateQAs) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateQAs)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateQAs)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqUpdateQA)
		}
	}

	return y
}

func (x *ReqUpdateQAs) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqUpdateQA)
		}
	}

	return y
}

func (x *ReqUpdateQAs) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqUpdateQA)
		}
	}

	return y
}

func (x *ReqCreateQA) MaskInLog() any {
	if x == nil {
		return (*ReqCreateQA)(nil)
	}

	y := proto.Clone(x).(*ReqCreateQA)
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}

	return y
}

func (x *ReqCreateQA) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}

	return y
}

func (x *ReqCreateQA) MaskInBff() any {
	if x == nil {
		return (*ReqCreateQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}

	return y
}

func (x *ReqCreateQAs) MaskInLog() any {
	if x == nil {
		return (*ReqCreateQAs)(nil)
	}

	y := proto.Clone(x).(*ReqCreateQAs)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqCreateQA)
		}
	}

	return y
}

func (x *ReqCreateQAs) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqCreateQA)
		}
	}

	return y
}

func (x *ReqCreateQAs) MaskInBff() any {
	if x == nil {
		return (*ReqCreateQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqCreateQA)
		}
	}

	return y
}

func (x *ReqListQA) MaskInLog() any {
	if x == nil {
		return (*ReqListQA)(nil)
	}

	y := proto.Clone(x).(*ReqListQA)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UpdateBy[k] = vv.MaskInLog().(*ai.Operator)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInLog() any }); ok {
		y.OrderByLabel = v.MaskInLog().(*ai.OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.CreateBy[k] = vv.MaskInLog().(*ai.Operator)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInLog() any }); ok {
		y.TipFilter = v.MaskInLog().(*ReqListQA_TipFilter)
	}

	return y
}

func (x *ReqListQA) MaskInRpc() any {
	if x == nil {
		return (*ReqListQA)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UpdateBy[k] = vv.MaskInRpc().(*ai.Operator)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInRpc() any }); ok {
		y.OrderByLabel = v.MaskInRpc().(*ai.OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.CreateBy[k] = vv.MaskInRpc().(*ai.Operator)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInRpc() any }); ok {
		y.TipFilter = v.MaskInRpc().(*ReqListQA_TipFilter)
	}

	return y
}

func (x *ReqListQA) MaskInBff() any {
	if x == nil {
		return (*ReqListQA)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UpdateBy[k] = vv.MaskInBff().(*ai.Operator)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInBff() any }); ok {
		y.OrderByLabel = v.MaskInBff().(*ai.OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.CreateBy[k] = vv.MaskInBff().(*ai.Operator)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInBff() any }); ok {
		y.TipFilter = v.MaskInBff().(*ReqListQA_TipFilter)
	}

	return y
}

func (x *RspListQA) MaskInLog() any {
	if x == nil {
		return (*RspListQA)(nil)
	}

	y := proto.Clone(x).(*RspListQA)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*CollectionQA)
		}
	}

	return y
}

func (x *RspListQA) MaskInRpc() any {
	if x == nil {
		return (*RspListQA)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*CollectionQA)
		}
	}

	return y
}

func (x *RspListQA) MaskInBff() any {
	if x == nil {
		return (*RspListQA)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*CollectionQA)
		}
	}

	return y
}

func (x *ReqUpdateTextFile) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateTextFile)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateTextFile)
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.States[k] = vv.MaskInLog().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqUpdateTextFile) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateTextFile)(nil)
	}

	y := x
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.States[k] = vv.MaskInRpc().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqUpdateTextFile) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateTextFile)(nil)
	}

	y := x
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.States[k] = vv.MaskInBff().(*ai.DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqUpdateTextFiles) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateTextFiles)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateTextFiles)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqUpdateTextFile)
		}
	}

	return y
}

func (x *ReqUpdateTextFiles) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqUpdateTextFile)
		}
	}

	return y
}

func (x *ReqUpdateTextFiles) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqUpdateTextFile)
		}
	}

	return y
}

func (x *ReqCreateTextFiles) MaskInLog() any {
	if x == nil {
		return (*ReqCreateTextFiles)(nil)
	}

	y := proto.Clone(x).(*ReqCreateTextFiles)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqCreateTextFiles_Item)
		}
	}

	return y
}

func (x *ReqCreateTextFiles) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqCreateTextFiles_Item)
		}
	}

	return y
}

func (x *ReqCreateTextFiles) MaskInBff() any {
	if x == nil {
		return (*ReqCreateTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqCreateTextFiles_Item)
		}
	}

	return y
}

func (x *ReqCreateTextFiles_Item) MaskInLog() any {
	if x == nil {
		return (*ReqCreateTextFiles_Item)(nil)
	}

	y := proto.Clone(x).(*ReqCreateTextFiles_Item)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqCreateTextFiles_Item) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateTextFiles_Item)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqCreateTextFiles_Item) MaskInBff() any {
	if x == nil {
		return (*ReqCreateTextFiles_Item)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqListTextFiles) MaskInLog() any {
	if x == nil {
		return (*ReqListTextFiles)(nil)
	}

	y := proto.Clone(x).(*ReqListTextFiles)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UpdateBy[k] = vv.MaskInLog().(*ai.Operator)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Search).(interface{ MaskInLog() any }); ok {
		y.Search = v.MaskInLog().(*ReqListTextFiles_Search)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInLog() any }); ok {
		y.OrderByLabel = v.MaskInLog().(*ai.OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.CreateBy[k] = vv.MaskInLog().(*ai.Operator)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInLog() any }); ok {
		y.TipFilter = v.MaskInLog().(*ReqListTextFiles_TipFilter)
	}

	return y
}

func (x *ReqListTextFiles) MaskInRpc() any {
	if x == nil {
		return (*ReqListTextFiles)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UpdateBy[k] = vv.MaskInRpc().(*ai.Operator)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Search).(interface{ MaskInRpc() any }); ok {
		y.Search = v.MaskInRpc().(*ReqListTextFiles_Search)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInRpc() any }); ok {
		y.OrderByLabel = v.MaskInRpc().(*ai.OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.CreateBy[k] = vv.MaskInRpc().(*ai.Operator)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInRpc() any }); ok {
		y.TipFilter = v.MaskInRpc().(*ReqListTextFiles_TipFilter)
	}

	return y
}

func (x *ReqListTextFiles) MaskInBff() any {
	if x == nil {
		return (*ReqListTextFiles)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UpdateBy[k] = vv.MaskInBff().(*ai.Operator)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Search).(interface{ MaskInBff() any }); ok {
		y.Search = v.MaskInBff().(*ReqListTextFiles_Search)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInBff() any }); ok {
		y.OrderByLabel = v.MaskInBff().(*ai.OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.CreateBy[k] = vv.MaskInBff().(*ai.Operator)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInBff() any }); ok {
		y.TipFilter = v.MaskInBff().(*ReqListTextFiles_TipFilter)
	}

	return y
}

func (x *RspListTextFiles) MaskInLog() any {
	if x == nil {
		return (*RspListTextFiles)(nil)
	}

	y := proto.Clone(x).(*RspListTextFiles)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*CollectionTextFile)
		}
	}

	return y
}

func (x *RspListTextFiles) MaskInRpc() any {
	if x == nil {
		return (*RspListTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*CollectionTextFile)
		}
	}

	return y
}

func (x *RspListTextFiles) MaskInBff() any {
	if x == nil {
		return (*RspListTextFiles)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*CollectionTextFile)
		}
	}

	return y
}

func (x *ReqCreateSystemDocCopy) MaskInLog() any {
	if x == nil {
		return (*ReqCreateSystemDocCopy)(nil)
	}

	y := proto.Clone(x).(*ReqCreateSystemDocCopy)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqCreateSystemDocCopy_Item)
		}
	}

	return y
}

func (x *ReqCreateSystemDocCopy) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateSystemDocCopy)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqCreateSystemDocCopy_Item)
		}
	}

	return y
}

func (x *ReqCreateSystemDocCopy) MaskInBff() any {
	if x == nil {
		return (*ReqCreateSystemDocCopy)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqCreateSystemDocCopy_Item)
		}
	}

	return y
}

func (x *RspCreateSystemDocCopy) MaskInLog() any {
	if x == nil {
		return (*RspCreateSystemDocCopy)(nil)
	}

	y := proto.Clone(x).(*RspCreateSystemDocCopy)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspCreateSystemDocCopy_Result)
		}
	}

	return y
}

func (x *RspCreateSystemDocCopy) MaskInRpc() any {
	if x == nil {
		return (*RspCreateSystemDocCopy)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspCreateSystemDocCopy_Result)
		}
	}

	return y
}

func (x *RspCreateSystemDocCopy) MaskInBff() any {
	if x == nil {
		return (*RspCreateSystemDocCopy)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspCreateSystemDocCopy_Result)
		}
	}

	return y
}

func (x *RspEnableSystemDoc) MaskInLog() any {
	if x == nil {
		return (*RspEnableSystemDoc)(nil)
	}

	y := proto.Clone(x).(*RspEnableSystemDoc)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspEnableSystemDoc_Result)
		}
	}

	return y
}

func (x *RspEnableSystemDoc) MaskInRpc() any {
	if x == nil {
		return (*RspEnableSystemDoc)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspEnableSystemDoc_Result)
		}
	}

	return y
}

func (x *RspEnableSystemDoc) MaskInBff() any {
	if x == nil {
		return (*RspEnableSystemDoc)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspEnableSystemDoc_Result)
		}
	}

	return y
}

func (x *RspDisableSystemDoc) MaskInLog() any {
	if x == nil {
		return (*RspDisableSystemDoc)(nil)
	}

	y := proto.Clone(x).(*RspDisableSystemDoc)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspDisableSystemDoc_Result)
		}
	}

	return y
}

func (x *RspDisableSystemDoc) MaskInRpc() any {
	if x == nil {
		return (*RspDisableSystemDoc)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspDisableSystemDoc_Result)
		}
	}

	return y
}

func (x *RspDisableSystemDoc) MaskInBff() any {
	if x == nil {
		return (*RspDisableSystemDoc)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspDisableSystemDoc_Result)
		}
	}

	return y
}

func (x *RspDeleteSystemDoc) MaskInLog() any {
	if x == nil {
		return (*RspDeleteSystemDoc)(nil)
	}

	y := proto.Clone(x).(*RspDeleteSystemDoc)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspDeleteSystemDoc_Result)
		}
	}

	return y
}

func (x *RspDeleteSystemDoc) MaskInRpc() any {
	if x == nil {
		return (*RspDeleteSystemDoc)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspDeleteSystemDoc_Result)
		}
	}

	return y
}

func (x *RspDeleteSystemDoc) MaskInBff() any {
	if x == nil {
		return (*RspDeleteSystemDoc)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspDeleteSystemDoc_Result)
		}
	}

	return y
}

func (x *ReqUpsertMgmtFeedback) MaskInLog() any {
	if x == nil {
		return (*ReqUpsertMgmtFeedback)(nil)
	}

	y := proto.Clone(x).(*ReqUpsertMgmtFeedback)
	if v, ok := any(y.MgmtFeedback).(interface{ MaskInLog() any }); ok {
		y.MgmtFeedback = v.MaskInLog().(*ai.FeedbackComment)
	}
	if v, ok := any(y.MgmtComment).(interface{ MaskInLog() any }); ok {
		y.MgmtComment = v.MaskInLog().(*ai.FeedbackComment)
	}

	return y
}

func (x *ReqUpsertMgmtFeedback) MaskInRpc() any {
	if x == nil {
		return (*ReqUpsertMgmtFeedback)(nil)
	}

	y := x
	if v, ok := any(y.MgmtFeedback).(interface{ MaskInRpc() any }); ok {
		y.MgmtFeedback = v.MaskInRpc().(*ai.FeedbackComment)
	}
	if v, ok := any(y.MgmtComment).(interface{ MaskInRpc() any }); ok {
		y.MgmtComment = v.MaskInRpc().(*ai.FeedbackComment)
	}

	return y
}

func (x *ReqUpsertMgmtFeedback) MaskInBff() any {
	if x == nil {
		return (*ReqUpsertMgmtFeedback)(nil)
	}

	y := x
	if v, ok := any(y.MgmtFeedback).(interface{ MaskInBff() any }); ok {
		y.MgmtFeedback = v.MaskInBff().(*ai.FeedbackComment)
	}
	if v, ok := any(y.MgmtComment).(interface{ MaskInBff() any }); ok {
		y.MgmtComment = v.MaskInBff().(*ai.FeedbackComment)
	}

	return y
}

func (x *ReqGetFeedbacks) MaskInLog() any {
	if x == nil {
		return (*ReqGetFeedbacks)(nil)
	}

	y := proto.Clone(x).(*ReqGetFeedbacks)
	if v, ok := any(y.CreateDateRange).(interface{ MaskInLog() any }); ok {
		y.CreateDateRange = v.MaskInLog().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetFeedbacks) MaskInRpc() any {
	if x == nil {
		return (*ReqGetFeedbacks)(nil)
	}

	y := x
	if v, ok := any(y.CreateDateRange).(interface{ MaskInRpc() any }); ok {
		y.CreateDateRange = v.MaskInRpc().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetFeedbacks) MaskInBff() any {
	if x == nil {
		return (*ReqGetFeedbacks)(nil)
	}

	y := x
	if v, ok := any(y.CreateDateRange).(interface{ MaskInBff() any }); ok {
		y.CreateDateRange = v.MaskInBff().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}

	return y
}

func (x *RspGetFeedbacks) MaskInLog() any {
	if x == nil {
		return (*RspGetFeedbacks)(nil)
	}

	y := proto.Clone(x).(*RspGetFeedbacks)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*RspGetFeedbacks_Item)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserCards[k] = vv.MaskInLog().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamCards[k] = vv.MaskInLog().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInLog().(*mgmt.UserCard)
		}
	}

	return y
}

func (x *RspGetFeedbacks) MaskInRpc() any {
	if x == nil {
		return (*RspGetFeedbacks)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*RspGetFeedbacks_Item)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserCards[k] = vv.MaskInRpc().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamCards[k] = vv.MaskInRpc().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInRpc().(*mgmt.UserCard)
		}
	}

	return y
}

func (x *RspGetFeedbacks) MaskInBff() any {
	if x == nil {
		return (*RspGetFeedbacks)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*RspGetFeedbacks_Item)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserCards[k] = vv.MaskInBff().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamCards[k] = vv.MaskInBff().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInBff().(*mgmt.UserCard)
		}
	}

	return y
}

func (x *RspGetFeedbacks_Item) MaskInLog() any {
	if x == nil {
		return (*RspGetFeedbacks_Item)(nil)
	}

	y := proto.Clone(x).(*RspGetFeedbacks_Item)
	if v, ok := any(y.Feedback).(interface{ MaskInLog() any }); ok {
		y.Feedback = v.MaskInLog().(*ai.Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.References[k] = vv.MaskInLog().(*ai.FeedbackReference)
		}
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInLog() any }); ok {
		y.OriginalQuestion = v.MaskInLog().(*ai.ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInLog() any }); ok {
		y.OriginalAnswer = v.MaskInLog().(*ai.ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInLog().(*ai.ChatMessageDoc)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInLog().(*ai.ChatMessageDoc)
		}
	}

	return y
}

func (x *RspGetFeedbacks_Item) MaskInRpc() any {
	if x == nil {
		return (*RspGetFeedbacks_Item)(nil)
	}

	y := x
	if v, ok := any(y.Feedback).(interface{ MaskInRpc() any }); ok {
		y.Feedback = v.MaskInRpc().(*ai.Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.References[k] = vv.MaskInRpc().(*ai.FeedbackReference)
		}
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInRpc() any }); ok {
		y.OriginalQuestion = v.MaskInRpc().(*ai.ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInRpc() any }); ok {
		y.OriginalAnswer = v.MaskInRpc().(*ai.ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInRpc().(*ai.ChatMessageDoc)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInRpc().(*ai.ChatMessageDoc)
		}
	}

	return y
}

func (x *RspGetFeedbacks_Item) MaskInBff() any {
	if x == nil {
		return (*RspGetFeedbacks_Item)(nil)
	}

	y := x
	if v, ok := any(y.Feedback).(interface{ MaskInBff() any }); ok {
		y.Feedback = v.MaskInBff().(*ai.Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.References[k] = vv.MaskInBff().(*ai.FeedbackReference)
		}
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInBff() any }); ok {
		y.OriginalQuestion = v.MaskInBff().(*ai.ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInBff() any }); ok {
		y.OriginalAnswer = v.MaskInBff().(*ai.ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInBff().(*ai.ChatMessageDoc)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInBff().(*ai.ChatMessageDoc)
		}
	}

	return y
}

func (x *RspFindFeedback) MaskInLog() any {
	if x == nil {
		return (*RspFindFeedback)(nil)
	}

	y := proto.Clone(x).(*RspFindFeedback)
	if v, ok := any(y.Feedback).(interface{ MaskInLog() any }); ok {
		y.Feedback = v.MaskInLog().(*ai.Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.References[k] = vv.MaskInLog().(*ai.FeedbackReference)
		}
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInLog() any }); ok {
		y.OriginalQuestion = v.MaskInLog().(*ai.ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInLog() any }); ok {
		y.OriginalAnswer = v.MaskInLog().(*ai.ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInLog().(*ai.ChatMessageDoc)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserCards[k] = vv.MaskInLog().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamCards[k] = vv.MaskInLog().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInLog().(*mgmt.UserCard)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInLog().(*ai.ChatMessageDoc)
		}
	}

	return y
}

func (x *RspFindFeedback) MaskInRpc() any {
	if x == nil {
		return (*RspFindFeedback)(nil)
	}

	y := x
	if v, ok := any(y.Feedback).(interface{ MaskInRpc() any }); ok {
		y.Feedback = v.MaskInRpc().(*ai.Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.References[k] = vv.MaskInRpc().(*ai.FeedbackReference)
		}
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInRpc() any }); ok {
		y.OriginalQuestion = v.MaskInRpc().(*ai.ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInRpc() any }); ok {
		y.OriginalAnswer = v.MaskInRpc().(*ai.ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInRpc().(*ai.ChatMessageDoc)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserCards[k] = vv.MaskInRpc().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamCards[k] = vv.MaskInRpc().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInRpc().(*mgmt.UserCard)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInRpc().(*ai.ChatMessageDoc)
		}
	}

	return y
}

func (x *RspFindFeedback) MaskInBff() any {
	if x == nil {
		return (*RspFindFeedback)(nil)
	}

	y := x
	if v, ok := any(y.Feedback).(interface{ MaskInBff() any }); ok {
		y.Feedback = v.MaskInBff().(*ai.Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.References[k] = vv.MaskInBff().(*ai.FeedbackReference)
		}
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInBff() any }); ok {
		y.OriginalQuestion = v.MaskInBff().(*ai.ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInBff() any }); ok {
		y.OriginalAnswer = v.MaskInBff().(*ai.ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInBff().(*ai.ChatMessageDoc)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserCards[k] = vv.MaskInBff().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamCards[k] = vv.MaskInBff().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInBff().(*mgmt.UserCard)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInBff().(*ai.ChatMessageDoc)
		}
	}

	return y
}

func (x *RspAcceptFeedback) MaskInLog() any {
	if x == nil {
		return (*RspAcceptFeedback)(nil)
	}

	y := proto.Clone(x).(*RspAcceptFeedback)
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Results[k] = vv.MaskInLog().(*RspAcceptFeedback_Result)
		}
	}

	return y
}

func (x *RspAcceptFeedback) MaskInRpc() any {
	if x == nil {
		return (*RspAcceptFeedback)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Results[k] = vv.MaskInRpc().(*RspAcceptFeedback_Result)
		}
	}

	return y
}

func (x *RspAcceptFeedback) MaskInBff() any {
	if x == nil {
		return (*RspAcceptFeedback)(nil)
	}

	y := x
	for k, v := range y.Results {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Results[k] = vv.MaskInBff().(*RspAcceptFeedback_Result)
		}
	}

	return y
}

func (x *ReqGetFeedbackLogs) MaskInLog() any {
	if x == nil {
		return (*ReqGetFeedbackLogs)(nil)
	}

	y := proto.Clone(x).(*ReqGetFeedbackLogs)
	if v, ok := any(y.CreateIdentity).(interface{ MaskInLog() any }); ok {
		y.CreateIdentity = v.MaskInLog().(*base.Identity)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInLog() any }); ok {
		y.CreateDateRange = v.MaskInLog().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetFeedbackLogs) MaskInRpc() any {
	if x == nil {
		return (*ReqGetFeedbackLogs)(nil)
	}

	y := x
	if v, ok := any(y.CreateIdentity).(interface{ MaskInRpc() any }); ok {
		y.CreateIdentity = v.MaskInRpc().(*base.Identity)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInRpc() any }); ok {
		y.CreateDateRange = v.MaskInRpc().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetFeedbackLogs) MaskInBff() any {
	if x == nil {
		return (*ReqGetFeedbackLogs)(nil)
	}

	y := x
	if v, ok := any(y.CreateIdentity).(interface{ MaskInBff() any }); ok {
		y.CreateIdentity = v.MaskInBff().(*base.Identity)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInBff() any }); ok {
		y.CreateDateRange = v.MaskInBff().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}

	return y
}

func (x *RspGetFeedbackLogs) MaskInLog() any {
	if x == nil {
		return (*RspGetFeedbackLogs)(nil)
	}

	y := proto.Clone(x).(*RspGetFeedbackLogs)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ai.FullFeedbackLog)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserCards[k] = vv.MaskInLog().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamCards[k] = vv.MaskInLog().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInLog().(*mgmt.UserCard)
		}
	}

	return y
}

func (x *RspGetFeedbackLogs) MaskInRpc() any {
	if x == nil {
		return (*RspGetFeedbackLogs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ai.FullFeedbackLog)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserCards[k] = vv.MaskInRpc().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamCards[k] = vv.MaskInRpc().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInRpc().(*mgmt.UserCard)
		}
	}

	return y
}

func (x *RspGetFeedbackLogs) MaskInBff() any {
	if x == nil {
		return (*RspGetFeedbackLogs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ai.FullFeedbackLog)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserCards[k] = vv.MaskInBff().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamCards[k] = vv.MaskInBff().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInBff().(*mgmt.UserCard)
		}
	}

	return y
}

func (x *ReqUpdateChatNoticeConf) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateChatNoticeConf)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateChatNoticeConf)
	if v, ok := any(y.Conf).(interface{ MaskInLog() any }); ok {
		y.Conf = v.MaskInLog().(*ai.AiAssistantNoticeConf)
	}

	return y
}

func (x *ReqUpdateChatNoticeConf) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateChatNoticeConf)(nil)
	}

	y := x
	if v, ok := any(y.Conf).(interface{ MaskInRpc() any }); ok {
		y.Conf = v.MaskInRpc().(*ai.AiAssistantNoticeConf)
	}

	return y
}

func (x *ReqUpdateChatNoticeConf) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateChatNoticeConf)(nil)
	}

	y := x
	if v, ok := any(y.Conf).(interface{ MaskInBff() any }); ok {
		y.Conf = v.MaskInBff().(*ai.AiAssistantNoticeConf)
	}

	return y
}

func (x *ReqListChat) MaskInLog() any {
	if x == nil {
		return (*ReqListChat)(nil)
	}

	y := proto.Clone(x).(*ReqListChat)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqListChat_Filter)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInLog() any }); ok {
		y.CreateDateRange = v.MaskInLog().(*base.TimeRange)
	}
	if v, ok := any(y.UpdateDateRange).(interface{ MaskInLog() any }); ok {
		y.UpdateDateRange = v.MaskInLog().(*base.TimeRange)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInLog() any }); ok {
		y.OrderByLabel = v.MaskInLog().(*ai.OrderByLabel)
	}

	return y
}

func (x *ReqListChat) MaskInRpc() any {
	if x == nil {
		return (*ReqListChat)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqListChat_Filter)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInRpc() any }); ok {
		y.CreateDateRange = v.MaskInRpc().(*base.TimeRange)
	}
	if v, ok := any(y.UpdateDateRange).(interface{ MaskInRpc() any }); ok {
		y.UpdateDateRange = v.MaskInRpc().(*base.TimeRange)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInRpc() any }); ok {
		y.OrderByLabel = v.MaskInRpc().(*ai.OrderByLabel)
	}

	return y
}

func (x *ReqListChat) MaskInBff() any {
	if x == nil {
		return (*ReqListChat)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqListChat_Filter)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInBff() any }); ok {
		y.CreateDateRange = v.MaskInBff().(*base.TimeRange)
	}
	if v, ok := any(y.UpdateDateRange).(interface{ MaskInBff() any }); ok {
		y.UpdateDateRange = v.MaskInBff().(*base.TimeRange)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInBff() any }); ok {
		y.OrderByLabel = v.MaskInBff().(*ai.OrderByLabel)
	}

	return y
}

func (x *RspListChat) MaskInLog() any {
	if x == nil {
		return (*RspListChat)(nil)
	}

	y := proto.Clone(x).(*RspListChat)
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Chats[k] = vv.MaskInLog().(*Chat)
		}
	}

	return y
}

func (x *RspListChat) MaskInRpc() any {
	if x == nil {
		return (*RspListChat)(nil)
	}

	y := x
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Chats[k] = vv.MaskInRpc().(*Chat)
		}
	}

	return y
}

func (x *RspListChat) MaskInBff() any {
	if x == nil {
		return (*RspListChat)(nil)
	}

	y := x
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Chats[k] = vv.MaskInBff().(*Chat)
		}
	}

	return y
}

func (x *ReqGetChatDetail) MaskInLog() any {
	if x == nil {
		return (*ReqGetChatDetail)(nil)
	}

	y := proto.Clone(x).(*ReqGetChatDetail)
	if v, ok := any(y.SendRange).(interface{ MaskInLog() any }); ok {
		y.SendRange = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetChatDetail) MaskInRpc() any {
	if x == nil {
		return (*ReqGetChatDetail)(nil)
	}

	y := x
	if v, ok := any(y.SendRange).(interface{ MaskInRpc() any }); ok {
		y.SendRange = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetChatDetail) MaskInBff() any {
	if x == nil {
		return (*ReqGetChatDetail)(nil)
	}

	y := x
	if v, ok := any(y.SendRange).(interface{ MaskInBff() any }); ok {
		y.SendRange = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *RspGetChatDetail) MaskInLog() any {
	if x == nil {
		return (*RspGetChatDetail)(nil)
	}

	y := proto.Clone(x).(*RspGetChatDetail)
	if v, ok := any(y.ChatDetail).(interface{ MaskInLog() any }); ok {
		y.ChatDetail = v.MaskInLog().(*ChatDetail)
	}

	return y
}

func (x *RspGetChatDetail) MaskInRpc() any {
	if x == nil {
		return (*RspGetChatDetail)(nil)
	}

	y := x
	if v, ok := any(y.ChatDetail).(interface{ MaskInRpc() any }); ok {
		y.ChatDetail = v.MaskInRpc().(*ChatDetail)
	}

	return y
}

func (x *RspGetChatDetail) MaskInBff() any {
	if x == nil {
		return (*RspGetChatDetail)(nil)
	}

	y := x
	if v, ok := any(y.ChatDetail).(interface{ MaskInBff() any }); ok {
		y.ChatDetail = v.MaskInBff().(*ChatDetail)
	}

	return y
}

func (x *RspSearchChat) MaskInLog() any {
	if x == nil {
		return (*RspSearchChat)(nil)
	}

	y := proto.Clone(x).(*RspSearchChat)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ai.EventChatMessage)
	}

	return y
}

func (x *RspSearchChat) MaskInRpc() any {
	if x == nil {
		return (*RspSearchChat)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ai.EventChatMessage)
	}

	return y
}

func (x *RspSearchChat) MaskInBff() any {
	if x == nil {
		return (*RspSearchChat)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ai.EventChatMessage)
	}

	return y
}

func (x *RspListChatLiveAgent) MaskInLog() any {
	if x == nil {
		return (*RspListChatLiveAgent)(nil)
	}

	y := proto.Clone(x).(*RspListChatLiveAgent)
	if v, ok := any(y.ChatLiveAgent).(interface{ MaskInLog() any }); ok {
		y.ChatLiveAgent = v.MaskInLog().(*ai.ChatLiveAgentInfo)
	}

	return y
}

func (x *RspListChatLiveAgent) MaskInRpc() any {
	if x == nil {
		return (*RspListChatLiveAgent)(nil)
	}

	y := x
	if v, ok := any(y.ChatLiveAgent).(interface{ MaskInRpc() any }); ok {
		y.ChatLiveAgent = v.MaskInRpc().(*ai.ChatLiveAgentInfo)
	}

	return y
}

func (x *RspListChatLiveAgent) MaskInBff() any {
	if x == nil {
		return (*RspListChatLiveAgent)(nil)
	}

	y := x
	if v, ok := any(y.ChatLiveAgent).(interface{ MaskInBff() any }); ok {
		y.ChatLiveAgent = v.MaskInBff().(*ai.ChatLiveAgentInfo)
	}

	return y
}

func (x *RspOnOffDocs) MaskInLog() any {
	if x == nil {
		return (*RspOnOffDocs)(nil)
	}

	y := proto.Clone(x).(*RspOnOffDocs)
	for k, v := range y.PreRepeatCollections {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.PreRepeatCollections[k] = vv.MaskInLog().(*RspOnOffDocs_RepeatCollection)
		}
	}
	for k, v := range y.RepeatCollections {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.RepeatCollections[k] = vv.MaskInLog().(*RspOnOffDocs_RepeatCollection)
		}
	}
	for k, v := range y.QaNumExceed {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.QaNumExceed[k] = vv.MaskInLog().(*RspOnOffDocs_QaContainsMatchCount)
		}
	}

	return y
}

func (x *RspOnOffDocs) MaskInRpc() any {
	if x == nil {
		return (*RspOnOffDocs)(nil)
	}

	y := x
	for k, v := range y.PreRepeatCollections {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.PreRepeatCollections[k] = vv.MaskInRpc().(*RspOnOffDocs_RepeatCollection)
		}
	}
	for k, v := range y.RepeatCollections {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.RepeatCollections[k] = vv.MaskInRpc().(*RspOnOffDocs_RepeatCollection)
		}
	}
	for k, v := range y.QaNumExceed {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.QaNumExceed[k] = vv.MaskInRpc().(*RspOnOffDocs_QaContainsMatchCount)
		}
	}

	return y
}

func (x *RspOnOffDocs) MaskInBff() any {
	if x == nil {
		return (*RspOnOffDocs)(nil)
	}

	y := x
	for k, v := range y.PreRepeatCollections {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.PreRepeatCollections[k] = vv.MaskInBff().(*RspOnOffDocs_RepeatCollection)
		}
	}
	for k, v := range y.RepeatCollections {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.RepeatCollections[k] = vv.MaskInBff().(*RspOnOffDocs_RepeatCollection)
		}
	}
	for k, v := range y.QaNumExceed {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.QaNumExceed[k] = vv.MaskInBff().(*RspOnOffDocs_QaContainsMatchCount)
		}
	}

	return y
}

func (x *RspListAssistantCanShareDoc) MaskInLog() any {
	if x == nil {
		return (*RspListAssistantCanShareDoc)(nil)
	}

	y := proto.Clone(x).(*RspListAssistantCanShareDoc)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*RspListAssistantCanShareDoc_SharedAssistant)
		}
	}

	return y
}

func (x *RspListAssistantCanShareDoc) MaskInRpc() any {
	if x == nil {
		return (*RspListAssistantCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*RspListAssistantCanShareDoc_SharedAssistant)
		}
	}

	return y
}

func (x *RspListAssistantCanShareDoc) MaskInBff() any {
	if x == nil {
		return (*RspListAssistantCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*RspListAssistantCanShareDoc_SharedAssistant)
		}
	}

	return y
}

func (x *RspListeDocShareConfigSender) MaskInLog() any {
	if x == nil {
		return (*RspListeDocShareConfigSender)(nil)
	}

	y := proto.Clone(x).(*RspListeDocShareConfigSender)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*RspListeDocShareConfigSender_SharedAssistant)
		}
	}
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*RspListeDocShareConfigSender_SharedUserTeam)
		}
	}
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*RspListeDocShareConfigSender_SharedUserTeam)
		}
	}

	return y
}

func (x *RspListeDocShareConfigSender) MaskInRpc() any {
	if x == nil {
		return (*RspListeDocShareConfigSender)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*RspListeDocShareConfigSender_SharedAssistant)
		}
	}
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*RspListeDocShareConfigSender_SharedUserTeam)
		}
	}
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*RspListeDocShareConfigSender_SharedUserTeam)
		}
	}

	return y
}

func (x *RspListeDocShareConfigSender) MaskInBff() any {
	if x == nil {
		return (*RspListeDocShareConfigSender)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*RspListeDocShareConfigSender_SharedAssistant)
		}
	}
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*RspListeDocShareConfigSender_SharedUserTeam)
		}
	}
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*RspListeDocShareConfigSender_SharedUserTeam)
		}
	}

	return y
}

func (x *RspListTeamCanShareDoc) MaskInLog() any {
	if x == nil {
		return (*RspListTeamCanShareDoc)(nil)
	}

	y := proto.Clone(x).(*RspListTeamCanShareDoc)
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*RspListTeamCanShareDoc_Teams)
		}
	}

	return y
}

func (x *RspListTeamCanShareDoc) MaskInRpc() any {
	if x == nil {
		return (*RspListTeamCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*RspListTeamCanShareDoc_Teams)
		}
	}

	return y
}

func (x *RspListTeamCanShareDoc) MaskInBff() any {
	if x == nil {
		return (*RspListTeamCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*RspListTeamCanShareDoc_Teams)
		}
	}

	return y
}

func (x *RspListUserCanShareDoc) MaskInLog() any {
	if x == nil {
		return (*RspListUserCanShareDoc)(nil)
	}

	y := proto.Clone(x).(*RspListUserCanShareDoc)
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*RspListUserCanShareDoc_Users)
		}
	}

	return y
}

func (x *RspListUserCanShareDoc) MaskInRpc() any {
	if x == nil {
		return (*RspListUserCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*RspListUserCanShareDoc_Users)
		}
	}

	return y
}

func (x *RspListUserCanShareDoc) MaskInBff() any {
	if x == nil {
		return (*RspListUserCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*RspListUserCanShareDoc_Users)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverUserTeam) MaskInLog() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverUserTeam)(nil)
	}

	y := proto.Clone(x).(*RspListDocShareConfigReceiverUserTeam)
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*RspListDocShareConfigReceiverUserTeam_Members)
		}
	}
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*RspListDocShareConfigReceiverUserTeam_Members)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverUserTeam) MaskInRpc() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverUserTeam)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*RspListDocShareConfigReceiverUserTeam_Members)
		}
	}
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*RspListDocShareConfigReceiverUserTeam_Members)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverUserTeam) MaskInBff() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverUserTeam)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*RspListDocShareConfigReceiverUserTeam_Members)
		}
	}
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*RspListDocShareConfigReceiverUserTeam_Members)
		}
	}

	return y
}

func (x *ReqCreateDocShareConfigReceiverAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqCreateDocShareConfigReceiverAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqCreateDocShareConfigReceiverAssistant)
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserShares[k] = vv.MaskInLog().(*ReqCreateDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *ReqCreateDocShareConfigReceiverAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateDocShareConfigReceiverAssistant)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserShares[k] = vv.MaskInRpc().(*ReqCreateDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *ReqCreateDocShareConfigReceiverAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqCreateDocShareConfigReceiverAssistant)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserShares[k] = vv.MaskInBff().(*ReqCreateDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant) MaskInLog() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant)(nil)
	}

	y := proto.Clone(x).(*RspListDocShareConfigReceiverAssistant)
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserShares[k] = vv.MaskInLog().(*RspListDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant) MaskInRpc() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserShares[k] = vv.MaskInRpc().(*RspListDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant) MaskInBff() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserShares[k] = vv.MaskInBff().(*RspListDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant_UserShare) MaskInLog() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant_UserShare)(nil)
	}

	y := proto.Clone(x).(*RspListDocShareConfigReceiverAssistant_UserShare)
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*RspListDocShareConfigReceiverAssistant_Members)
		}
	}
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*RspListDocShareConfigReceiverAssistant_Members)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant_UserShare) MaskInRpc() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant_UserShare)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*RspListDocShareConfigReceiverAssistant_Members)
		}
	}
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*RspListDocShareConfigReceiverAssistant_Members)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant_UserShare) MaskInBff() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant_UserShare)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*RspListDocShareConfigReceiverAssistant_Members)
		}
	}
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*RspListDocShareConfigReceiverAssistant_Members)
		}
	}

	return y
}

func (x *ReqGetAssistantsPage) MaskInLog() any {
	if x == nil {
		return (*ReqGetAssistantsPage)(nil)
	}

	y := proto.Clone(x).(*ReqGetAssistantsPage)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetAssistantsPage) MaskInRpc() any {
	if x == nil {
		return (*ReqGetAssistantsPage)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}

	return y
}

func (x *ReqGetAssistantsPage) MaskInBff() any {
	if x == nil {
		return (*ReqGetAssistantsPage)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*base.TimeRange)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}

	return y
}

func (x *RspGetAssistantsPage) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantsPage)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantsPage)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.FullAssistant)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserCards[k] = vv.MaskInLog().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamCards[k] = vv.MaskInLog().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInLog().(*mgmt.UserCard)
		}
	}

	return y
}

func (x *RspGetAssistantsPage) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantsPage)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.FullAssistant)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserCards[k] = vv.MaskInRpc().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamCards[k] = vv.MaskInRpc().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInRpc().(*mgmt.UserCard)
		}
	}

	return y
}

func (x *RspGetAssistantsPage) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantsPage)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.FullAssistant)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserCards[k] = vv.MaskInBff().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamCards[k] = vv.MaskInBff().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInBff().(*mgmt.UserCard)
		}
	}

	return y
}

func (x *ReqCreateAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqCreateAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqCreateAssistant)
	if v, ok := any(y.Config).(interface{ MaskInLog() any }); ok {
		y.Config = v.MaskInLog().(*ai.AssistantConfig)
	}

	return y
}

func (x *ReqCreateAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateAssistant)(nil)
	}

	y := x
	if v, ok := any(y.Config).(interface{ MaskInRpc() any }); ok {
		y.Config = v.MaskInRpc().(*ai.AssistantConfig)
	}

	return y
}

func (x *ReqCreateAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqCreateAssistant)(nil)
	}

	y := x
	if v, ok := any(y.Config).(interface{ MaskInBff() any }); ok {
		y.Config = v.MaskInBff().(*ai.AssistantConfig)
	}

	return y
}

func (x *ReqBatchCreateAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqBatchCreateAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqBatchCreateAssistant)
	for k, v := range y.Configs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Configs[k] = vv.MaskInLog().(*ai.AssistantConfig)
		}
	}

	return y
}

func (x *ReqBatchCreateAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqBatchCreateAssistant)(nil)
	}

	y := x
	for k, v := range y.Configs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Configs[k] = vv.MaskInRpc().(*ai.AssistantConfig)
		}
	}

	return y
}

func (x *ReqBatchCreateAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqBatchCreateAssistant)(nil)
	}

	y := x
	for k, v := range y.Configs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Configs[k] = vv.MaskInBff().(*ai.AssistantConfig)
		}
	}

	return y
}

func (x *ReqUpdateAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateAssistant)
	if v, ok := any(y.Config).(interface{ MaskInLog() any }); ok {
		y.Config = v.MaskInLog().(*ai.AssistantConfig)
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqUpdateAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateAssistant)(nil)
	}

	y := x
	if v, ok := any(y.Config).(interface{ MaskInRpc() any }); ok {
		y.Config = v.MaskInRpc().(*ai.AssistantConfig)
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqUpdateAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateAssistant)(nil)
	}

	y := x
	if v, ok := any(y.Config).(interface{ MaskInBff() any }); ok {
		y.Config = v.MaskInBff().(*ai.AssistantConfig)
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqBatchUpdateAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqBatchUpdateAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqBatchUpdateAssistant)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqBatchUpdateAssistant_Item)
		}
	}

	return y
}

func (x *ReqBatchUpdateAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqBatchUpdateAssistant)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqBatchUpdateAssistant_Item)
		}
	}

	return y
}

func (x *ReqBatchUpdateAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqBatchUpdateAssistant)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqBatchUpdateAssistant_Item)
		}
	}

	return y
}

func (x *ReqBatchUpdateAssistant_Item) MaskInLog() any {
	if x == nil {
		return (*ReqBatchUpdateAssistant_Item)(nil)
	}

	y := proto.Clone(x).(*ReqBatchUpdateAssistant_Item)
	if v, ok := any(y.Config).(interface{ MaskInLog() any }); ok {
		y.Config = v.MaskInLog().(*ai.AssistantConfig)
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqBatchUpdateAssistant_Item) MaskInRpc() any {
	if x == nil {
		return (*ReqBatchUpdateAssistant_Item)(nil)
	}

	y := x
	if v, ok := any(y.Config).(interface{ MaskInRpc() any }); ok {
		y.Config = v.MaskInRpc().(*ai.AssistantConfig)
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqBatchUpdateAssistant_Item) MaskInBff() any {
	if x == nil {
		return (*ReqBatchUpdateAssistant_Item)(nil)
	}

	y := x
	if v, ok := any(y.Config).(interface{ MaskInBff() any }); ok {
		y.Config = v.MaskInBff().(*ai.AssistantConfig)
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqGetAssistantLogsPage) MaskInLog() any {
	if x == nil {
		return (*ReqGetAssistantLogsPage)(nil)
	}

	y := proto.Clone(x).(*ReqGetAssistantLogsPage)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetAssistantLogsPage) MaskInRpc() any {
	if x == nil {
		return (*ReqGetAssistantLogsPage)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetAssistantLogsPage) MaskInBff() any {
	if x == nil {
		return (*ReqGetAssistantLogsPage)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *RspGetAssistantLogsPage) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantLogsPage)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantLogsPage)
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Logs[k] = vv.MaskInLog().(*ai.AssistantLog)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserCards[k] = vv.MaskInLog().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TeamCards[k] = vv.MaskInLog().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInLog().(*mgmt.UserCard)
		}
	}

	return y
}

func (x *RspGetAssistantLogsPage) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantLogsPage)(nil)
	}

	y := x
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Logs[k] = vv.MaskInRpc().(*ai.AssistantLog)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserCards[k] = vv.MaskInRpc().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TeamCards[k] = vv.MaskInRpc().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInRpc().(*mgmt.UserCard)
		}
	}

	return y
}

func (x *RspGetAssistantLogsPage) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantLogsPage)(nil)
	}

	y := x
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Logs[k] = vv.MaskInBff().(*ai.AssistantLog)
		}
	}
	for k, v := range y.UserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserCards[k] = vv.MaskInBff().(*iam.UserCard)
		}
	}
	for k, v := range y.TeamCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TeamCards[k] = vv.MaskInBff().(*team.TeamCard)
		}
	}
	for k, v := range y.MgmtUserCards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.MgmtUserCards[k] = vv.MaskInBff().(*mgmt.UserCard)
		}
	}

	return y
}

func (x *RspGetAssistantOptions) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantOptions)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantOptions)
	for k, v := range y.InteractiveCode {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.InteractiveCode[k] = vv.MaskInLog().(*ai.InteractiveCodeOption)
		}
	}
	for k, v := range y.VisibleChain {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.VisibleChain[k] = vv.MaskInLog().(*ai.VisibleChainOption)
		}
	}
	for k, v := range y.EmbeddingModel {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.EmbeddingModel[k] = vv.MaskInLog().(*ai.EmbeddingModelOption)
		}
	}
	for k, v := range y.ChatModelV2 {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ChatModelV2[k] = vv.MaskInLog().(*ai.ChatModelOption)
		}
	}
	for k, v := range y.SearchEngineV2 {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SearchEngineV2[k] = vv.MaskInLog().(*ai.SearchEngineOption)
		}
	}

	return y
}

func (x *RspGetAssistantOptions) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantOptions)(nil)
	}

	y := x
	for k, v := range y.InteractiveCode {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.InteractiveCode[k] = vv.MaskInRpc().(*ai.InteractiveCodeOption)
		}
	}
	for k, v := range y.VisibleChain {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.VisibleChain[k] = vv.MaskInRpc().(*ai.VisibleChainOption)
		}
	}
	for k, v := range y.EmbeddingModel {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.EmbeddingModel[k] = vv.MaskInRpc().(*ai.EmbeddingModelOption)
		}
	}
	for k, v := range y.ChatModelV2 {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ChatModelV2[k] = vv.MaskInRpc().(*ai.ChatModelOption)
		}
	}
	for k, v := range y.SearchEngineV2 {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SearchEngineV2[k] = vv.MaskInRpc().(*ai.SearchEngineOption)
		}
	}

	return y
}

func (x *RspGetAssistantOptions) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantOptions)(nil)
	}

	y := x
	for k, v := range y.InteractiveCode {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.InteractiveCode[k] = vv.MaskInBff().(*ai.InteractiveCodeOption)
		}
	}
	for k, v := range y.VisibleChain {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.VisibleChain[k] = vv.MaskInBff().(*ai.VisibleChainOption)
		}
	}
	for k, v := range y.EmbeddingModel {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.EmbeddingModel[k] = vv.MaskInBff().(*ai.EmbeddingModelOption)
		}
	}
	for k, v := range y.ChatModelV2 {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ChatModelV2[k] = vv.MaskInBff().(*ai.ChatModelOption)
		}
	}
	for k, v := range y.SearchEngineV2 {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SearchEngineV2[k] = vv.MaskInBff().(*ai.SearchEngineOption)
		}
	}

	return y
}

func (x *ReqAutoChunkDoc) MaskInLog() any {
	if x == nil {
		return (*ReqAutoChunkDoc)(nil)
	}

	y := proto.Clone(x).(*ReqAutoChunkDoc)
	if v, ok := any(y.AutoPara).(interface{ MaskInLog() any }); ok {
		y.AutoPara = v.MaskInLog().(*ai.AutoChunkPara)
	}

	return y
}

func (x *ReqAutoChunkDoc) MaskInRpc() any {
	if x == nil {
		return (*ReqAutoChunkDoc)(nil)
	}

	y := x
	if v, ok := any(y.AutoPara).(interface{ MaskInRpc() any }); ok {
		y.AutoPara = v.MaskInRpc().(*ai.AutoChunkPara)
	}

	return y
}

func (x *ReqAutoChunkDoc) MaskInBff() any {
	if x == nil {
		return (*ReqAutoChunkDoc)(nil)
	}

	y := x
	if v, ok := any(y.AutoPara).(interface{ MaskInBff() any }); ok {
		y.AutoPara = v.MaskInBff().(*ai.AutoChunkPara)
	}

	return y
}

func (x *RspAutoChunkDoc) MaskInLog() any {
	if x == nil {
		return (*RspAutoChunkDoc)(nil)
	}

	y := proto.Clone(x).(*RspAutoChunkDoc)
	for k, v := range y.Chunks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Chunks[k] = vv.MaskInLog().(*ai.ChunkItem)
		}
	}

	return y
}

func (x *RspAutoChunkDoc) MaskInRpc() any {
	if x == nil {
		return (*RspAutoChunkDoc)(nil)
	}

	y := x
	for k, v := range y.Chunks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Chunks[k] = vv.MaskInRpc().(*ai.ChunkItem)
		}
	}

	return y
}

func (x *RspAutoChunkDoc) MaskInBff() any {
	if x == nil {
		return (*RspAutoChunkDoc)(nil)
	}

	y := x
	for k, v := range y.Chunks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Chunks[k] = vv.MaskInBff().(*ai.ChunkItem)
		}
	}

	return y
}

func (x *ReqManualChunkDoc) MaskInLog() any {
	if x == nil {
		return (*ReqManualChunkDoc)(nil)
	}

	y := proto.Clone(x).(*ReqManualChunkDoc)
	if v, ok := any(y.ManualPara).(interface{ MaskInLog() any }); ok {
		y.ManualPara = v.MaskInLog().(*ai.ManualChunkPara)
	}

	return y
}

func (x *ReqManualChunkDoc) MaskInRpc() any {
	if x == nil {
		return (*ReqManualChunkDoc)(nil)
	}

	y := x
	if v, ok := any(y.ManualPara).(interface{ MaskInRpc() any }); ok {
		y.ManualPara = v.MaskInRpc().(*ai.ManualChunkPara)
	}

	return y
}

func (x *ReqManualChunkDoc) MaskInBff() any {
	if x == nil {
		return (*ReqManualChunkDoc)(nil)
	}

	y := x
	if v, ok := any(y.ManualPara).(interface{ MaskInBff() any }); ok {
		y.ManualPara = v.MaskInBff().(*ai.ManualChunkPara)
	}

	return y
}

func (x *RspGetDocChunks) MaskInLog() any {
	if x == nil {
		return (*RspGetDocChunks)(nil)
	}

	y := proto.Clone(x).(*RspGetDocChunks)
	for k, v := range y.AssistantChunks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.AssistantChunks[k] = vv.MaskInLog().(*ai.AssistantChunks)
		}
	}

	return y
}

func (x *RspGetDocChunks) MaskInRpc() any {
	if x == nil {
		return (*RspGetDocChunks)(nil)
	}

	y := x
	for k, v := range y.AssistantChunks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.AssistantChunks[k] = vv.MaskInRpc().(*ai.AssistantChunks)
		}
	}

	return y
}

func (x *RspGetDocChunks) MaskInBff() any {
	if x == nil {
		return (*RspGetDocChunks)(nil)
	}

	y := x
	for k, v := range y.AssistantChunks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.AssistantChunks[k] = vv.MaskInBff().(*ai.AssistantChunks)
		}
	}

	return y
}

func (x *RspGetChunkDocTasks) MaskInLog() any {
	if x == nil {
		return (*RspGetChunkDocTasks)(nil)
	}

	y := proto.Clone(x).(*RspGetChunkDocTasks)
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tasks[k] = vv.MaskInLog().(*ai.DocChunkTask)
		}
	}

	return y
}

func (x *RspGetChunkDocTasks) MaskInRpc() any {
	if x == nil {
		return (*RspGetChunkDocTasks)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tasks[k] = vv.MaskInRpc().(*ai.DocChunkTask)
		}
	}

	return y
}

func (x *RspGetChunkDocTasks) MaskInBff() any {
	if x == nil {
		return (*RspGetChunkDocTasks)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tasks[k] = vv.MaskInBff().(*ai.DocChunkTask)
		}
	}

	return y
}

func (x *RspGetDocEmbeddingModels) MaskInLog() any {
	if x == nil {
		return (*RspGetDocEmbeddingModels)(nil)
	}

	y := proto.Clone(x).(*RspGetDocEmbeddingModels)
	for k, v := range y.EmbeddingModels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.EmbeddingModels[k] = vv.MaskInLog().(*ai.EmbeddingModelCount)
		}
	}

	return y
}

func (x *RspGetDocEmbeddingModels) MaskInRpc() any {
	if x == nil {
		return (*RspGetDocEmbeddingModels)(nil)
	}

	y := x
	for k, v := range y.EmbeddingModels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.EmbeddingModels[k] = vv.MaskInRpc().(*ai.EmbeddingModelCount)
		}
	}

	return y
}

func (x *RspGetDocEmbeddingModels) MaskInBff() any {
	if x == nil {
		return (*RspGetDocEmbeddingModels)(nil)
	}

	y := x
	for k, v := range y.EmbeddingModels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.EmbeddingModels[k] = vv.MaskInBff().(*ai.EmbeddingModelCount)
		}
	}

	return y
}

func (x *ReqBatchUpdateDocAttr) MaskInLog() any {
	if x == nil {
		return (*ReqBatchUpdateDocAttr)(nil)
	}

	y := proto.Clone(x).(*ReqBatchUpdateDocAttr)
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqBatchUpdateDocAttr) MaskInRpc() any {
	if x == nil {
		return (*ReqBatchUpdateDocAttr)(nil)
	}

	y := x
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}

	return y
}

func (x *ReqBatchUpdateDocAttr) MaskInBff() any {
	if x == nil {
		return (*ReqBatchUpdateDocAttr)(nil)
	}

	y := x
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}

	return y
}

func (x *RspGetTextFileTip) MaskInLog() any {
	if x == nil {
		return (*RspGetTextFileTip)(nil)
	}

	y := proto.Clone(x).(*RspGetTextFileTip)
	for k, v := range y.TableOverSize {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TableOverSize[k] = vv.MaskInLog().(*ai.TextFileTipTableOverSize)
		}
	}

	return y
}

func (x *RspGetTextFileTip) MaskInRpc() any {
	if x == nil {
		return (*RspGetTextFileTip)(nil)
	}

	y := x
	for k, v := range y.TableOverSize {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TableOverSize[k] = vv.MaskInRpc().(*ai.TextFileTipTableOverSize)
		}
	}

	return y
}

func (x *RspGetTextFileTip) MaskInBff() any {
	if x == nil {
		return (*RspGetTextFileTip)(nil)
	}

	y := x
	for k, v := range y.TableOverSize {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TableOverSize[k] = vv.MaskInBff().(*ai.TextFileTipTableOverSize)
		}
	}

	return y
}

func (x *ReqImportQA) MaskInLog() any {
	if x == nil {
		return (*ReqImportQA)(nil)
	}

	y := proto.Clone(x).(*ReqImportQA)
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*ai.CustomLabel)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqImportQA) MaskInRpc() any {
	if x == nil {
		return (*ReqImportQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*ai.CustomLabel)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqImportQA) MaskInBff() any {
	if x == nil {
		return (*ReqImportQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*ai.DocReference)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*ai.CustomLabel)
		}
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}

	return y
}

func (x *ReqImportQAs) MaskInLog() any {
	if x == nil {
		return (*ReqImportQAs)(nil)
	}

	y := proto.Clone(x).(*ReqImportQAs)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqImportQA)
		}
	}

	return y
}

func (x *ReqImportQAs) MaskInRpc() any {
	if x == nil {
		return (*ReqImportQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqImportQA)
		}
	}

	return y
}

func (x *ReqImportQAs) MaskInBff() any {
	if x == nil {
		return (*ReqImportQAs)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqImportQA)
		}
	}

	return y
}

func (x *RspDescribeExportTasks) MaskInLog() any {
	if x == nil {
		return (*RspDescribeExportTasks)(nil)
	}

	y := proto.Clone(x).(*RspDescribeExportTasks)
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tasks[k] = vv.MaskInLog().(*ai.ExportTask)
		}
	}

	return y
}

func (x *RspDescribeExportTasks) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeExportTasks)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tasks[k] = vv.MaskInRpc().(*ai.ExportTask)
		}
	}

	return y
}

func (x *RspDescribeExportTasks) MaskInBff() any {
	if x == nil {
		return (*RspDescribeExportTasks)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tasks[k] = vv.MaskInBff().(*ai.ExportTask)
		}
	}

	return y
}

func (x *RspListNebulaAssistants) MaskInLog() any {
	if x == nil {
		return (*RspListNebulaAssistants)(nil)
	}

	y := proto.Clone(x).(*RspListNebulaAssistants)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*ai.AssistantV2)
		}
	}

	return y
}

func (x *RspListNebulaAssistants) MaskInRpc() any {
	if x == nil {
		return (*RspListNebulaAssistants)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*ai.AssistantV2)
		}
	}

	return y
}

func (x *RspListNebulaAssistants) MaskInBff() any {
	if x == nil {
		return (*RspListNebulaAssistants)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*ai.AssistantV2)
		}
	}

	return y
}

func (x *RspListNebulaContributors) MaskInLog() any {
	if x == nil {
		return (*RspListNebulaContributors)(nil)
	}

	y := proto.Clone(x).(*RspListNebulaContributors)
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributors[k] = vv.MaskInLog().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspListNebulaContributors) MaskInRpc() any {
	if x == nil {
		return (*RspListNebulaContributors)(nil)
	}

	y := x
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributors[k] = vv.MaskInRpc().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspListNebulaContributors) MaskInBff() any {
	if x == nil {
		return (*RspListNebulaContributors)(nil)
	}

	y := x
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributors[k] = vv.MaskInBff().(*ai.Contributor)
		}
	}

	return y
}

func (x *RspDescribeChatSuggestLog) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Logs {
		if sanitizer, ok := any(x.Logs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspProxyChatHtmlUrl) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contents {
		if sanitizer, ok := any(x.Contents[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetTextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Item).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqCreateDocQuery) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Doc).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Qa).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqUpdateObjectCustomLabels) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListTanliveAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqModifyCustomLabels) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListCustomLabel) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListDocByRef) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListDocByRef_Doc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetChatMessageDetail) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.CollectionItems {
		if sanitizer, ok := any(x.CollectionItems[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Logs {
		if sanitizer, ok := any(x.Logs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.CollectionTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqValidateQAs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqValidateQAs_Item) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListCollectionFileName) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListCollection) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Collections {
		if sanitizer, ok := any(x.Collections[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListContributor) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributors {
		if sanitizer, ok := any(x.Contributors[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListOperator) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Operators {
		if sanitizer, ok := any(x.Operators[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspValidateQAs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Errors {
		if sanitizer, ok := any(x.Errors[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *SearchCollectionItem) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSearchCollection) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Start).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.End).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Item {
		if sanitizer, ok := any(x.Item[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.States {
		if sanitizer, ok := any(x.States[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqUpdateQAs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateQAs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UpdateBy {
		if sanitizer, ok := any(x.UpdateBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.OrderByLabel).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.CreateBy {
		if sanitizer, ok := any(x.CreateBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.TipFilter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateTextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.States {
		if sanitizer, ok := any(x.States[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateTextFiles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateTextFiles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateTextFiles_Item) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListTextFiles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UpdateBy {
		if sanitizer, ok := any(x.UpdateBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Search).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.OrderByLabel).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.CreateBy {
		if sanitizer, ok := any(x.CreateBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.TipFilter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListTextFiles) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateSystemDocCopy) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspCreateSystemDocCopy) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspEnableSystemDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDisableSystemDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDeleteSystemDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpsertMgmtFeedback) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.MgmtFeedback).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.MgmtComment).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetFeedbacks) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDateRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetFeedbacks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UserCards {
		if sanitizer, ok := any(x.UserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamCards {
		if sanitizer, ok := any(x.TeamCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.MgmtUserCards {
		if sanitizer, ok := any(x.MgmtUserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetFeedbacks_Item) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Feedback).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.References {
		if sanitizer, ok := any(x.References[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.OriginalQuestion).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OriginalAnswer).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.ExpectedDocs {
		if sanitizer, ok := any(x.ExpectedDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ExpectedMgmtDocs {
		if sanitizer, ok := any(x.ExpectedMgmtDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspFindFeedback) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Feedback).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.References {
		if sanitizer, ok := any(x.References[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.OriginalQuestion).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OriginalAnswer).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.ExpectedDocs {
		if sanitizer, ok := any(x.ExpectedDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UserCards {
		if sanitizer, ok := any(x.UserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamCards {
		if sanitizer, ok := any(x.TeamCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.MgmtUserCards {
		if sanitizer, ok := any(x.MgmtUserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ExpectedMgmtDocs {
		if sanitizer, ok := any(x.ExpectedMgmtDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspAcceptFeedback) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Results {
		if sanitizer, ok := any(x.Results[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetFeedbackLogs) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateIdentity).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDateRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetFeedbackLogs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UserCards {
		if sanitizer, ok := any(x.UserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamCards {
		if sanitizer, ok := any(x.TeamCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.MgmtUserCards {
		if sanitizer, ok := any(x.MgmtUserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateChatNoticeConf) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Conf).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqListChat) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDateRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDateRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.OrderByLabel).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListChat) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Chats {
		if sanitizer, ok := any(x.Chats[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetChatDetail) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.SendRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetChatDetail) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ChatDetail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSearchChat) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListChatLiveAgent) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ChatLiveAgent).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspOnOffDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.PreRepeatCollections {
		if sanitizer, ok := any(x.PreRepeatCollections[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.RepeatCollections {
		if sanitizer, ok := any(x.RepeatCollections[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.QaNumExceed {
		if sanitizer, ok := any(x.QaNumExceed[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListAssistantCanShareDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListeDocShareConfigSender) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListTeamCanShareDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListUserCanShareDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListDocShareConfigReceiverUserTeam) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateDocShareConfigReceiverAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.UserShares {
		if sanitizer, ok := any(x.UserShares[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListDocShareConfigReceiverAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.UserShares {
		if sanitizer, ok := any(x.UserShares[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListDocShareConfigReceiverAssistant_UserShare) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetAssistantsPage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAssistantsPage) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UserCards {
		if sanitizer, ok := any(x.UserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamCards {
		if sanitizer, ok := any(x.TeamCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.MgmtUserCards {
		if sanitizer, ok := any(x.MgmtUserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Config).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqBatchCreateAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Configs {
		if sanitizer, ok := any(x.Configs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Config).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqBatchUpdateAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqBatchUpdateAssistant_Item) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Config).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetAssistantLogsPage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetAssistantLogsPage) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Logs {
		if sanitizer, ok := any(x.Logs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UserCards {
		if sanitizer, ok := any(x.UserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.TeamCards {
		if sanitizer, ok := any(x.TeamCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.MgmtUserCards {
		if sanitizer, ok := any(x.MgmtUserCards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAssistantOptions) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.InteractiveCode {
		if sanitizer, ok := any(x.InteractiveCode[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.VisibleChain {
		if sanitizer, ok := any(x.VisibleChain[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.EmbeddingModel {
		if sanitizer, ok := any(x.EmbeddingModel[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ChatModelV2 {
		if sanitizer, ok := any(x.ChatModelV2[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SearchEngineV2 {
		if sanitizer, ok := any(x.SearchEngineV2[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqAutoChunkDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AutoPara).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspAutoChunkDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Chunks {
		if sanitizer, ok := any(x.Chunks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqManualChunkDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ManualPara).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetDocChunks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.AssistantChunks {
		if sanitizer, ok := any(x.AssistantChunks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetChunkDocTasks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tasks {
		if sanitizer, ok := any(x.Tasks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetDocEmbeddingModels) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.EmbeddingModels {
		if sanitizer, ok := any(x.EmbeddingModels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqBatchUpdateDocAttr) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetTextFileTip) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TableOverSize {
		if sanitizer, ok := any(x.TableOverSize[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqImportQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqImportQAs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeExportTasks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tasks {
		if sanitizer, ok := any(x.Tasks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListNebulaAssistants) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListNebulaContributors) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributors {
		if sanitizer, ok := any(x.Contributors[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

type AiBffHandler interface {
	// 创建文本或文件
	CreateTextFiles(context.Context, *ReqCreateTextFiles, *RspCreateTextFiles) error
	// 查询文本或文件列表
	ListTextFiles(context.Context, *ReqListTextFiles, *RspListTextFiles) error
	// 更新文本或文件
	UpdateTextFiles(context.Context, *ReqUpdateTextFiles, *emptypb.Empty) error
	// 创建系统文档副本（人工修改）
	CreateSystemDocCopy(context.Context, *ReqCreateSystemDocCopy, *RspCreateSystemDocCopy) error
	// 启用系统文档
	EnableSystemDoc(context.Context, *ReqEnableSystemDoc, *RspEnableSystemDoc) error
	// 停用系统文档
	DisableSystemDoc(context.Context, *ReqDisableSystemDoc, *RspDisableSystemDoc) error
	// 删除系统文档
	DeleteSystemDoc(context.Context, *ReqDeleteSystemDoc, *RspDeleteSystemDoc) error
	// 查询QA列表
	ListQA(context.Context, *ReqListQA, *RspListQA) error
	// 创建QA
	CreateQAs(context.Context, *ReqCreateQAs, *emptypb.Empty) error
	// 更新QA
	UpdateQAs(context.Context, *ReqUpdateQAs, *emptypb.Empty) error
	// 删除Doc，包括QA，文本或文件
	DeleteQAs(context.Context, *ReqDeleteDocs, *RspDeleteDocs) error
	// 启用/禁用doc
	OnOffDocs(context.Context, *ReqOnOffDocs, *RspOnOffDocs) error
	// id查询文本/文件详情
	GetTextFile(context.Context, *ReqGetTextFile, *RspGetTextFile) error
	// 批量更新doc的特定字段值
	BatchUpdateDocAttr(context.Context, *ReqBatchUpdateDocAttr, *RspBatchUpdateDocAttr) error
	// 重新解析文件
	ReparseTextFiles(context.Context, *ReqReparseTextFiles, *RspReparseTextFiles) error
	// 创建doc查询
	CreateDocQuery(context.Context, *ReqCreateDocQuery, *RspCreateDocQuery) error
	// 查询文本文件的知识提示（解析失败，文件名重复，表头过长）等信息
	GetTextFileTip(context.Context, *ReqGetTextFileTip, *RspGetTextFileTip) error
	// 查询QA的知识提示（问题超长，内容重复）等信息
	GetQaTip(context.Context, *ReqGetQaTip, *RspGetQaTip) error
	// collection向量查询
	SearchCollection(context.Context, *ReqSearchCollection, *RspSearchCollection) error
	// 校验待创建QA
	ValidateQAs(context.Context, *ReqValidateQAs, *RspValidateQAs) error
	// 导入文本/文件
	ImportQAs(context.Context, *ReqImportQAs, *RspImportQAs) error
	// 查询collection用户端列表
	ListCollection(context.Context, *emptypb.Empty, *RspListCollection) error
	// 查询ai助手列表
	ListAssistant(context.Context, *ReqListAssistant, *RspListAssistant) error
	// 查询ai助手列表
	ListTanliveAssistant(context.Context, *ReqListTanliveAssistant, *RspListTanliveAssistant) error
	// 查询已经设置并开启知识库接收的助手ID列表
	ListMyAssistantIds(context.Context, *ReqListMyAssistantIds, *RspListMyAssistantIds) error
	// 查询贡献者列表
	ListContributor(context.Context, *ReqListContributor, *RspListContributor) error
	// 查询更新人列表
	ListOperator(context.Context, *ReqListOperator, *RspListOperator) error
	// 查询文件列表
	ListCollectionFileName(context.Context, *ReqListCollectionFileName, *RspListCollectionFileName) error
	// 克隆QA/文本/文件
	CloneDoc(context.Context, *ReqCloneDoc, *RspCloneDoc) error
	// 根据文档ref_id查询文档
	ListDocByRef(context.Context, *ReqListDocByRef, *RspListDocByRef) error
	// 创建/更新碳LIVE反馈
	UpsertMgmtFeedback(context.Context, *ReqUpsertMgmtFeedback, *RspCreateFeedback) error
	// 查询用户反馈列表
	GetFeedbacks(context.Context, *ReqGetFeedbacks, *RspGetFeedbacks) error
	// 查询用户反馈详情
	FindFeedback(context.Context, *ReqFindFeedback, *RspFindFeedback) error
	// 采用用户反馈
	AcceptFeedback(context.Context, *ReqAcceptFeedback, *RspAcceptFeedback) error
	// 查询用户反馈日志列表
	GetFeedbackLogs(context.Context, *ReqGetFeedbackLogs, *RspGetFeedbackLogs) error
	// 更新助手的横幅信息
	UpdateAssistantNoticeConf(context.Context, *ReqUpdateChatNoticeConf, *emptypb.Empty) error
	// 获取所有会话的地区编码
	DescribeChatRegionCode(context.Context, *ReqDescribeChatRegionCode, *RspDescribeChatRegionCode) error
	// 获取所有教学反馈的地区编码
	DescribeFeedbackRegionCode(context.Context, *ReqDescribeFeedbackRegionCode, *RspDescribeFeedbackRegionCode) error
	// AI对话管理列表
	ListChat(context.Context, *ReqListChat, *RspListChat) error
	// AI对话详情
	GetChatDetail(context.Context, *ReqGetChatDetail, *RspGetChatDetail) error
	// 搜索chat
	SearchChat(context.Context, *ReqSearchChat, *RspSearchChat) error
	// 获取消息详情
	GetChatMessageDetail(context.Context, *ReqGetChatMessageDetail, *RspGetChatMessageDetail) error
	// 获取自定义标签列表
	ListCustomLabel(context.Context, *ReqListCustomLabel, *RspListCustomLabel) error
	// 插入或更新自定义标签
	ModifyCustomLabels(context.Context, *ReqModifyCustomLabels, *RspModifyCustomLabels) error
	// 删除自定义标签
	DeleteCustomLabels(context.Context, *ReqDeleteCustomLabels, *emptypb.Empty) error
	// 更新对象的自定义标签
	UpdateObjectCustomLabels(context.Context, *ReqUpdateObjectCustomLabels, *emptypb.Empty) error
	// 获取人工坐席列表
	ListChatLiveAgent(context.Context, *ReqListChatLiveAgent, *RspListChatLiveAgent) error
	// 切换人工坐席
	SwitchChatLiveAgent(context.Context, *ReqSwitchChatLiveAgent, *RspSwitchChatLiveAgent) error
	// 修复message collection
	SyncFixChatMessageCollection(context.Context, *ReqSyncFixChatMessageCollection, *emptypb.Empty) error
	// 查询可分享的助手列表
	ListAssistantCanShareDoc(context.Context, *ReqListAssistantCanShareDoc, *RspListAssistantCanShareDoc) error
	// 创建文档分享（支持助手、个人、团队）
	CreateAssistantShare(context.Context, *ReqCreateAssistantShare, *RspCreateDocShare) error
	// 创建助手发送方设置
	CreateDocShareConfigSender(context.Context, *ReqCreateDocShareConfigSender, *emptypb.Empty) error
	// 查询助手发送方设置
	ListeDocShareConfigSender(context.Context, *ReqListeDocShareConfigSender, *RspListeDocShareConfigSender) error
	// 查询可分享的团队列表
	ListTeamCanShareDoc(context.Context, *ReqListTeamCanShareDoc, *RspListTeamCanShareDoc) error
	// 查询可分享的个人列表
	ListUserCanShareDoc(context.Context, *ReqListUserCanShareDoc, *RspListUserCanShareDoc) error
	// 创建个人/团队接收方设置
	CreateDocShareConfigReceiverUserTeam(context.Context, *ReqCreateDocShareConfigReceiverUserTeam, *RspCreateDocShareConfigReceiverUserTeam) error
	// 查询个人/团队接收方设置
	ListDocShareConfigReceiverUserTeam(context.Context, *ReqListDocShareConfigReceiverUserTeam, *RspListDocShareConfigReceiverUserTeam) error
	// 创建助手接收方设置
	CreateDocShareConfigReceiverAssistant(context.Context, *ReqCreateDocShareConfigReceiverAssistant, *RspCreateDocShareConfigReceiverAssistant) error
	// 查询助手接收方设置
	ListDocShareConfigReceiverAssistant(context.Context, *ReqListDocShareConfigReceiverAssistant, *RspListDocShareConfigReceiverAssistant) error
	// 获取助手url网页title
	ProxyChatHtmlUrl(context.Context, *ReqProxyChatHtmlUrl, *RspProxyChatHtmlUrl) error
	// 查询助手分页列表
	GetAssistantsPage(context.Context, *ReqGetAssistantsPage, *RspGetAssistantsPage) error
	// 创建助手
	CreateAssistant(context.Context, *ReqCreateAssistant, *RspCreateAssistant) error
	// 批量创建助手
	BatchCreateAssistant(context.Context, *ReqBatchCreateAssistant, *RspBatchCreateAssistant) error
	// 更新助手
	UpdateAssistant(context.Context, *ReqUpdateAssistant, *emptypb.Empty) error
	// 批量更新助手
	BatchUpdateAssistant(context.Context, *ReqBatchUpdateAssistant, *emptypb.Empty) error
	// 删除助手
	DeleteAssistant(context.Context, *ReqDeleteAssistant, *emptypb.Empty) error
	// 查询助手日志分页列表
	GetAssistantLogsPage(context.Context, *ReqGetAssistantLogsPage, *RspGetAssistantLogsPage) error
	// 获取助手下拉选项
	GetAssistantOptions(context.Context, *emptypb.Empty, *RspGetAssistantOptions) error
	// 自动文档分段
	AutoChunkDoc(context.Context, *ReqAutoChunkDoc, *RspAutoChunkDoc) error
	// 手动文档分段
	ManualChunkDoc(context.Context, *ReqManualChunkDoc, *RspManualChunkDoc) error
	// 查询文档分段信息
	GetDocChunks(context.Context, *ReqGetDocChunks, *RspGetDocChunks) error
	// 查询文档分段任务列表
	GetChunkDocTasks(context.Context, *ReqGetChunkDocTasks, *RspGetChunkDocTasks) error
	// 查询文档的向量化模型
	GetDocEmbeddingModels(context.Context, *ReqGetDocEmbeddingModels, *RspGetDocEmbeddingModels) error
	// 获取建议问题日志
	DescribeChatSuggestLog(context.Context, *ReqDescribeChatSuggestLog, *RspDescribeChatSuggestLog) error
	// 查询导出任务列表
	DescribeExportTasks(context.Context, *ReqDescribeExportTasks, *RspDescribeExportTasks) error
	// 知识星云，获取助手列表
	ListNebulaAssistants(context.Context, *ReqListNebulaAssistants, *RspListNebulaAssistants) error
	// 知识星云，获取助手下的贡献者筛选项
	ListNebulaContributors(context.Context, *ReqListNebulaContributors, *RspListNebulaContributors) error
}

func RegisterAiBff(s bff.Server, h AiBffHandler) bff.Group {
	group := s.Group(xhttp.GroupName("Ai"), xhttp.GroupPrefix(""))
	bff.AddRoute(group, http.MethodPost, "/ai/collection/create_text_files", h.CreateTextFiles).Name("CreateTextFiles")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_text_files", h.ListTextFiles).Name("ListTextFiles")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/update_text_files", h.UpdateTextFiles).Name("UpdateTextFiles")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/create_system_doc_copy", h.CreateSystemDocCopy).Name("CreateSystemDocCopy")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/enable_system_doc", h.EnableSystemDoc).Name("EnableSystemDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/disable_system_doc", h.DisableSystemDoc).Name("DisableSystemDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/delete_system_doc", h.DeleteSystemDoc).Name("DeleteSystemDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_qa", h.ListQA).Name("ListQA")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/create_qas", h.CreateQAs).Name("CreateQAs")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/update_qas", h.UpdateQAs).Name("UpdateQAs")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/delete_docs", h.DeleteQAs).Name("DeleteQAs")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/onoff_docs", h.OnOffDocs).Name("OnOffDocs")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/get_text_file", h.GetTextFile).Name("GetTextFile")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/batch_update_docs", h.BatchUpdateDocAttr).Name("BatchUpdateDocAttr")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/reparse_text_files", h.ReparseTextFiles).Name("ReparseTextFiles")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/create_doc_query", h.CreateDocQuery).Name("CreateDocQuery")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/get_text_file_tip", h.GetTextFileTip).Name("GetTextFileTip")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/get_qa_tip", h.GetQaTip).Name("GetQaTip")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/search_collection", h.SearchCollection).Name("SearchCollection")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/validate_qas", h.ValidateQAs).Name("ValidateQAs")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/import_qas", h.ImportQAs).Name("ImportQAs")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_collection", h.ListCollection).Name("ListCollection")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_assistant", h.ListAssistant).Name("ListAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/list_tanlive_assistant", h.ListTanliveAssistant).Name("ListTanliveAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/list_my_assistant_ids", h.ListMyAssistantIds).Name("ListMyAssistantIds")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_contributor", h.ListContributor).Name("ListContributor")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_operator", h.ListOperator).Name("ListOperator")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_filename", h.ListCollectionFileName).Name("ListCollectionFileName")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/clone_doc", h.CloneDoc).Name("CloneDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/collection/list_doc_by_ref", h.ListDocByRef).Name("ListDocByRef")
	bff.AddRoute(group, http.MethodPost, "/ai/upsert_mgmt_feedback", h.UpsertMgmtFeedback).Name("UpsertMgmtFeedback")
	bff.AddRoute(group, http.MethodPost, "/ai/get_feedbacks", h.GetFeedbacks).Name("GetFeedbacks")
	bff.AddRoute(group, http.MethodPost, "/ai/find_feedback", h.FindFeedback).Name("FindFeedback")
	bff.AddRoute(group, http.MethodPost, "/ai/accept_feedback", h.AcceptFeedback).Name("AcceptFeedback")
	bff.AddRoute(group, http.MethodPost, "/ai/get_feedback_logs", h.GetFeedbackLogs).Name("GetFeedbackLogs")
	bff.AddRoute(group, http.MethodPost, "/ai/update_assistant_notice_conf", h.UpdateAssistantNoticeConf).Name("UpdateAssistantNoticeConf")
	bff.AddRoute(group, http.MethodPost, "/ai/describe_chat_region_code", h.DescribeChatRegionCode).Name("DescribeChatRegionCode")
	bff.AddRoute(group, http.MethodPost, "/ai/describe_feedback_region_code", h.DescribeFeedbackRegionCode).Name("DescribeFeedbackRegionCode")
	bff.AddRoute(group, http.MethodPost, "/ai/list_chat", h.ListChat).Name("ListChat")
	bff.AddRoute(group, http.MethodPost, "/ai/get_chat_detail", h.GetChatDetail).Name("GetChatDetail")
	bff.AddRoute(group, http.MethodPost, "/ai/search_chat", h.SearchChat).Name("SearchChat")
	bff.AddRoute(group, http.MethodPost, "/ai/get_chat_message_detail", h.GetChatMessageDetail).Name("GetChatMessageDetail")
	bff.AddRoute(group, http.MethodPost, "/ai/get_custom_labels", h.ListCustomLabel).Name("ListCustomLabel")
	bff.AddRoute(group, http.MethodPost, "/ai/modify_custom_labels", h.ModifyCustomLabels).Name("ModifyCustomLabels")
	bff.AddRoute(group, http.MethodPost, "/ai/delete_custom_labels", h.DeleteCustomLabels).Name("DeleteCustomLabels")
	bff.AddRoute(group, http.MethodPost, "/ai/update_object_custom_labels", h.UpdateObjectCustomLabels).Name("UpdateObjectCustomLabels")
	bff.AddRoute(group, http.MethodPost, "/ai/list_chat_live_agent", h.ListChatLiveAgent).Name("ListChatLiveAgent")
	bff.AddRoute(group, http.MethodPost, "/ai/switch_chat_live_agent", h.SwitchChatLiveAgent).Name("SwitchChatLiveAgent")
	bff.AddRoute(group, http.MethodPost, "/ai/sync_fix_message_collection", h.SyncFixChatMessageCollection).Name("SyncFixChatMessageCollection")
	bff.AddRoute(group, http.MethodPost, "/ai/list_shared_assistant", h.ListAssistantCanShareDoc).Name("ListAssistantCanShareDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/create_assistant_share", h.CreateAssistantShare).Name("CreateAssistantShare")
	bff.AddRoute(group, http.MethodPost, "/ai/create_assistant_sender", h.CreateDocShareConfigSender).Name("CreateDocShareConfigSender")
	bff.AddRoute(group, http.MethodPost, "/ai/list_assistant_sender", h.ListeDocShareConfigSender).Name("ListeDocShareConfigSender")
	bff.AddRoute(group, http.MethodPost, "/ai/list_team_to_share_doc", h.ListTeamCanShareDoc).Name("ListTeamCanShareDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/list_user_to_share_doc", h.ListUserCanShareDoc).Name("ListUserCanShareDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/create_user_team_receiver", h.CreateDocShareConfigReceiverUserTeam).Name("CreateDocShareConfigReceiverUserTeam")
	bff.AddRoute(group, http.MethodPost, "/ai/list_user_team_receiver", h.ListDocShareConfigReceiverUserTeam).Name("ListDocShareConfigReceiverUserTeam")
	bff.AddRoute(group, http.MethodPost, "/ai/create_assistant_receiver", h.CreateDocShareConfigReceiverAssistant).Name("CreateDocShareConfigReceiverAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/list_assistant_receiver", h.ListDocShareConfigReceiverAssistant).Name("ListDocShareConfigReceiverAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/proxy_chat_url", h.ProxyChatHtmlUrl).Name("ProxyChatHtmlUrl")
	bff.AddRoute(group, http.MethodPost, "/ai/get_assistants_page", h.GetAssistantsPage).Name("GetAssistantsPage")
	bff.AddRoute(group, http.MethodPost, "/ai/create_assistant", h.CreateAssistant).Name("CreateAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/batch_create_assistant", h.BatchCreateAssistant).Name("BatchCreateAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/update_assistant", h.UpdateAssistant).Name("UpdateAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/batch_update_assistant", h.BatchUpdateAssistant).Name("BatchUpdateAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/delete_assistant", h.DeleteAssistant).Name("DeleteAssistant")
	bff.AddRoute(group, http.MethodPost, "/ai/get_assistant_logs_page", h.GetAssistantLogsPage).Name("GetAssistantLogsPage")
	bff.AddRoute(group, http.MethodPost, "/ai/get_assistant_options", h.GetAssistantOptions).Name("GetAssistantOptions")
	bff.AddRoute(group, http.MethodPost, "/ai/auto_chunk_doc", h.AutoChunkDoc).Name("AutoChunkDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/manual_chunk_doc", h.ManualChunkDoc).Name("ManualChunkDoc")
	bff.AddRoute(group, http.MethodPost, "/ai/get_doc_chunks", h.GetDocChunks).Name("GetDocChunks")
	bff.AddRoute(group, http.MethodPost, "/ai/get_chunk_doc_tasks", h.GetChunkDocTasks).Name("GetChunkDocTasks")
	bff.AddRoute(group, http.MethodPost, "/ai/get_doc_embedding_models", h.GetDocEmbeddingModels).Name("GetDocEmbeddingModels")
	bff.AddRoute(group, http.MethodPost, "/ai/describe_chat_suggest_log", h.DescribeChatSuggestLog).Name("DescribeChatSuggestLog")
	bff.AddRoute(group, http.MethodPost, "/ai/describe_export_tasks", h.DescribeExportTasks).Name("DescribeExportTasks")
	bff.AddRoute(group, http.MethodPost, "/ai/nebula/list_assistants", h.ListNebulaAssistants).Name("ListNebulaAssistants")
	bff.AddRoute(group, http.MethodPost, "/ai/nebula/list_contributors", h.ListNebulaContributors).Name("ListNebulaContributors")
	return group
}
