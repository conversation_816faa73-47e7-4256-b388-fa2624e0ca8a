// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/bff-mgmt/ai/ai.proto

package ai

import (
	ai "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iam "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Operator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type base.IdentityType `protobuf:"varint,1,opt,name=type,proto3,enum=tanlive.base.IdentityType" json:"type,omitempty"`
	// 用户id
	Id uint64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// 用户名称
	Username string `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	// 用户所属团队名称
	TeamName string `protobuf:"bytes,4,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"`
	// 用户id，只有为团队用户时，才需要
	UserId uint64 `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *Operator) Reset() {
	*x = Operator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Operator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Operator) ProtoMessage() {}

func (x *Operator) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Operator.ProtoReflect.Descriptor instead.
func (*Operator) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{0}
}

func (x *Operator) GetType() base.IdentityType {
	if x != nil {
		return x.Type
	}
	return base.IdentityType(0)
}

func (x *Operator) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Operator) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Operator) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

func (x *Operator) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 知识分享统计信息
type DocShareStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分享给的助手列表
	SharedAssistants []*SharedAssistant `protobuf:"bytes,1,rep,name=shared_assistants,json=sharedAssistants,proto3" json:"shared_assistants,omitempty"`
	// 分享给的团队列表
	SharedTeams []*SharedTeam `protobuf:"bytes,2,rep,name=shared_teams,json=sharedTeams,proto3" json:"shared_teams,omitempty"`
	// 分享给的用户列表
	SharedUsers []*SharedUser `protobuf:"bytes,3,rep,name=shared_users,json=sharedUsers,proto3" json:"shared_users,omitempty"`
}

func (x *DocShareStats) Reset() {
	*x = DocShareStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocShareStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocShareStats) ProtoMessage() {}

func (x *DocShareStats) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocShareStats.ProtoReflect.Descriptor instead.
func (*DocShareStats) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{1}
}

func (x *DocShareStats) GetSharedAssistants() []*SharedAssistant {
	if x != nil {
		return x.SharedAssistants
	}
	return nil
}

func (x *DocShareStats) GetSharedTeams() []*SharedTeam {
	if x != nil {
		return x.SharedTeams
	}
	return nil
}

func (x *DocShareStats) GetSharedUsers() []*SharedUser {
	if x != nil {
		return x.SharedUsers
	}
	return nil
}

// 分享的助手信息
type SharedAssistant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name   string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	NameEn string `protobuf:"bytes,3,opt,name=name_en,json=nameEn,proto3" json:"name_en,omitempty"`
}

func (x *SharedAssistant) Reset() {
	*x = SharedAssistant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SharedAssistant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SharedAssistant) ProtoMessage() {}

func (x *SharedAssistant) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SharedAssistant.ProtoReflect.Descriptor instead.
func (*SharedAssistant) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{2}
}

func (x *SharedAssistant) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SharedAssistant) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SharedAssistant) GetNameEn() string {
	if x != nil {
		return x.NameEn
	}
	return ""
}

// 分享的团队信息
type SharedTeam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SharedTeam) Reset() {
	*x = SharedTeam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SharedTeam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SharedTeam) ProtoMessage() {}

func (x *SharedTeam) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SharedTeam.ProtoReflect.Descriptor instead.
func (*SharedTeam) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{3}
}

func (x *SharedTeam) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SharedTeam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 分享的用户信息
type SharedUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SharedUser) Reset() {
	*x = SharedUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SharedUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SharedUser) ProtoMessage() {}

func (x *SharedUser) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SharedUser.ProtoReflect.Descriptor instead.
func (*SharedUser) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{4}
}

func (x *SharedUser) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SharedUser) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CollectionTextFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 文件/文本名称
	FileName string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// 文件/文本内容
	Text string `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	// 用户端
	Assistants []*ai.Assistant `protobuf:"bytes,4,rep,name=assistants,proto3" json:"assistants,omitempty"`
	// 状态(不包含助手对应的状态)
	State ai.DocState `protobuf:"varint,5,opt,name=state,proto3,enum=tanlive.ai.DocState" json:"state,omitempty"`
	// 贡献者
	Contributor []*ai.Contributor `protobuf:"bytes,6,rep,name=contributor,proto3" json:"contributor,omitempty"`
	// 文件url
	Url string `protobuf:"bytes,7,opt,name=url,proto3" json:"url,omitempty"`
	// 命中次数
	HitCount uint32 `protobuf:"varint,8,opt,name=hit_count,json=hitCount,proto3" json:"hit_count,omitempty"`
	// ugc类型
	UgcType base.DataType `protobuf:"varint,9,opt,name=ugc_type,json=ugcType,proto3,enum=tanlive.base.DataType" json:"ugc_type,omitempty"`
	// ugc的id
	UgcId      uint64                 `protobuf:"varint,10,opt,name=ugc_id,json=ugcId,proto3" json:"ugc_id,omitempty"`
	CreateBy   *Operator              `protobuf:"bytes,11,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	UpdateBy   *Operator              `protobuf:"bytes,12,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	UpdateDate *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 同步至ai侧的版本滞后数，为0代表已同步
	VersionLag uint64 `protobuf:"varint,15,opt,name=version_lag,json=versionLag,proto3" json:"version_lag,omitempty"`
	// 解析进度，0.5 = 50%
	ParseProgress float32 `protobuf:"fixed32,16,opt,name=parse_progress,json=parseProgress,proto3" json:"parse_progress,omitempty"`
	// 是否显示贡献者
	ShowContributor uint32                  `protobuf:"varint,17,opt,name=show_contributor,json=showContributor,proto3" json:"show_contributor,omitempty"`
	States          []*ai.DocAssistantState `protobuf:"bytes,18,rep,name=states,proto3" json:"states,omitempty"`
	// 是否为副本
	IsCopy bool `protobuf:"varint,19,opt,name=is_copy,json=isCopy,proto3" json:"is_copy,omitempty"`
	// UGC标题
	UgcTitle string `protobuf:"bytes,20,opt,name=ugc_title,json=ugcTitle,proto3" json:"ugc_title,omitempty"`
	// 是否为系统数据
	IsSystem bool `protobuf:"varint,21,opt,name=is_system,json=isSystem,proto3" json:"is_system,omitempty"`
	// 内容状态
	ContentState ai.DocContentState `protobuf:"varint,22,opt,name=content_state,json=contentState,proto3,enum=tanlive.ai.DocContentState" json:"content_state,omitempty"`
	// 副本列表
	Copies []*CollectionTextFile `protobuf:"bytes,23,rep,name=copies,proto3" json:"copies,omitempty"`
	// ugc的hashid
	UgcHashid     string                  `protobuf:"bytes,24,opt,name=ugc_hashid,json=ugcHashid,proto3" json:"ugc_hashid,omitempty"`
	SharedStates  []*ai.DocAssistantState `protobuf:"bytes,25,rep,name=shared_states,json=sharedStates,proto3" json:"shared_states,omitempty"`
	Labels        []*ai.CustomLabel       `protobuf:"bytes,26,rep,name=labels,proto3" json:"labels,omitempty"`
	Reference     []*ai.DocReference      `protobuf:"bytes,27,rep,name=reference,proto3" json:"reference,omitempty"`
	DownloadAsRef ai.DocFileDownloadAsRef `protobuf:"varint,28,opt,name=download_as_ref,json=downloadAsRef,proto3,enum=tanlive.ai.DocFileDownloadAsRef" json:"download_as_ref,omitempty"`
	ParseMode     ai.DocParseMode         `protobuf:"varint,29,opt,name=parse_mode,json=parseMode,proto3,enum=tanlive.ai.DocParseMode" json:"parse_mode,omitempty"`
	// 知识提示
	// 是否有超长标题表格
	HasOverSizedTables bool `protobuf:"varint,30,opt,name=has_over_sized_tables,json=hasOverSizedTables,proto3" json:"has_over_sized_tables,omitempty"`
	// 是否内容重复（租户内）
	HasRepeated bool             `protobuf:"varint,31,opt,name=has_repeated,json=hasRepeated,proto3" json:"has_repeated,omitempty"`
	DataSource  ai.DocDataSource `protobuf:"varint,32,opt,name=data_source,json=dataSource,proto3,enum=tanlive.ai.DocDataSource" json:"data_source,omitempty"`
	// 外部数据源信息
	DataSourceState uint32 `protobuf:"varint,33,opt,name=data_source_state,json=dataSourceState,proto3" json:"data_source_state,omitempty"`
	// 分享统计信息
	ShareStats *DocShareStats `protobuf:"bytes,34,opt,name=share_stats,json=shareStats,proto3" json:"share_stats,omitempty"`
}

func (x *CollectionTextFile) Reset() {
	*x = CollectionTextFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionTextFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionTextFile) ProtoMessage() {}

func (x *CollectionTextFile) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionTextFile.ProtoReflect.Descriptor instead.
func (*CollectionTextFile) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{5}
}

func (x *CollectionTextFile) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CollectionTextFile) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *CollectionTextFile) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *CollectionTextFile) GetAssistants() []*ai.Assistant {
	if x != nil {
		return x.Assistants
	}
	return nil
}

func (x *CollectionTextFile) GetState() ai.DocState {
	if x != nil {
		return x.State
	}
	return ai.DocState(0)
}

func (x *CollectionTextFile) GetContributor() []*ai.Contributor {
	if x != nil {
		return x.Contributor
	}
	return nil
}

func (x *CollectionTextFile) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CollectionTextFile) GetHitCount() uint32 {
	if x != nil {
		return x.HitCount
	}
	return 0
}

func (x *CollectionTextFile) GetUgcType() base.DataType {
	if x != nil {
		return x.UgcType
	}
	return base.DataType(0)
}

func (x *CollectionTextFile) GetUgcId() uint64 {
	if x != nil {
		return x.UgcId
	}
	return 0
}

func (x *CollectionTextFile) GetCreateBy() *Operator {
	if x != nil {
		return x.CreateBy
	}
	return nil
}

func (x *CollectionTextFile) GetUpdateBy() *Operator {
	if x != nil {
		return x.UpdateBy
	}
	return nil
}

func (x *CollectionTextFile) GetUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateDate
	}
	return nil
}

func (x *CollectionTextFile) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *CollectionTextFile) GetVersionLag() uint64 {
	if x != nil {
		return x.VersionLag
	}
	return 0
}

func (x *CollectionTextFile) GetParseProgress() float32 {
	if x != nil {
		return x.ParseProgress
	}
	return 0
}

func (x *CollectionTextFile) GetShowContributor() uint32 {
	if x != nil {
		return x.ShowContributor
	}
	return 0
}

func (x *CollectionTextFile) GetStates() []*ai.DocAssistantState {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *CollectionTextFile) GetIsCopy() bool {
	if x != nil {
		return x.IsCopy
	}
	return false
}

func (x *CollectionTextFile) GetUgcTitle() string {
	if x != nil {
		return x.UgcTitle
	}
	return ""
}

func (x *CollectionTextFile) GetIsSystem() bool {
	if x != nil {
		return x.IsSystem
	}
	return false
}

func (x *CollectionTextFile) GetContentState() ai.DocContentState {
	if x != nil {
		return x.ContentState
	}
	return ai.DocContentState(0)
}

func (x *CollectionTextFile) GetCopies() []*CollectionTextFile {
	if x != nil {
		return x.Copies
	}
	return nil
}

func (x *CollectionTextFile) GetUgcHashid() string {
	if x != nil {
		return x.UgcHashid
	}
	return ""
}

func (x *CollectionTextFile) GetSharedStates() []*ai.DocAssistantState {
	if x != nil {
		return x.SharedStates
	}
	return nil
}

func (x *CollectionTextFile) GetLabels() []*ai.CustomLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CollectionTextFile) GetReference() []*ai.DocReference {
	if x != nil {
		return x.Reference
	}
	return nil
}

func (x *CollectionTextFile) GetDownloadAsRef() ai.DocFileDownloadAsRef {
	if x != nil {
		return x.DownloadAsRef
	}
	return ai.DocFileDownloadAsRef(0)
}

func (x *CollectionTextFile) GetParseMode() ai.DocParseMode {
	if x != nil {
		return x.ParseMode
	}
	return ai.DocParseMode(0)
}

func (x *CollectionTextFile) GetHasOverSizedTables() bool {
	if x != nil {
		return x.HasOverSizedTables
	}
	return false
}

func (x *CollectionTextFile) GetHasRepeated() bool {
	if x != nil {
		return x.HasRepeated
	}
	return false
}

func (x *CollectionTextFile) GetDataSource() ai.DocDataSource {
	if x != nil {
		return x.DataSource
	}
	return ai.DocDataSource(0)
}

func (x *CollectionTextFile) GetDataSourceState() uint32 {
	if x != nil {
		return x.DataSourceState
	}
	return 0
}

func (x *CollectionTextFile) GetShareStats() *DocShareStats {
	if x != nil {
		return x.ShareStats
	}
	return nil
}

type CollectionQA struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 问题
	Question string `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	// 答案
	Answer string `protobuf:"bytes,3,opt,name=answer,proto3" json:"answer,omitempty"`
	// 用户端
	Assistants []*ai.Assistant `protobuf:"bytes,4,rep,name=assistants,proto3" json:"assistants,omitempty"`
	// 状态(不包含助手对应的状态)
	State ai.DocState `protobuf:"varint,5,opt,name=state,proto3,enum=tanlive.ai.DocState" json:"state,omitempty"`
	// 贡献者
	Contributor []*ai.Contributor `protobuf:"bytes,6,rep,name=contributor,proto3" json:"contributor,omitempty"`
	// 参考资料
	Reference []*ai.DocReference `protobuf:"bytes,7,rep,name=reference,proto3" json:"reference,omitempty"`
	// 命中次数
	HitCount   uint32                 `protobuf:"varint,8,opt,name=hit_count,json=hitCount,proto3" json:"hit_count,omitempty"`
	CreateBy   *Operator              `protobuf:"bytes,9,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	UpdateBy   *Operator              `protobuf:"bytes,10,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	UpdateDate *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	// 同步至ai侧的版本滞后数，为0代表已同步
	VersionLag uint64 `protobuf:"varint,13,opt,name=version_lag,json=versionLag,proto3" json:"version_lag,omitempty"`
	// 是否显示贡献者
	ShowContributor uint32                  `protobuf:"varint,14,opt,name=show_contributor,json=showContributor,proto3" json:"show_contributor,omitempty"`
	States          []*ai.DocAssistantState `protobuf:"bytes,15,rep,name=states,proto3" json:"states,omitempty"`
	Labels          []*ai.CustomLabel       `protobuf:"bytes,16,rep,name=labels,proto3" json:"labels,omitempty"`
	// 匹配模式
	MatchPatterns []ai.DocMatchPattern `protobuf:"varint,17,rep,packed,name=match_patterns,json=matchPatterns,proto3,enum=tanlive.ai.DocMatchPattern" json:"match_patterns,omitempty"`
	// 问题是否超长
	QuestionOversize bool `protobuf:"varint,18,opt,name=question_oversize,json=questionOversize,proto3" json:"question_oversize,omitempty"`
	// 是否有重复
	HasRepeated bool `protobuf:"varint,19,opt,name=has_repeated,json=hasRepeated,proto3" json:"has_repeated,omitempty"`
	// 分享统计信息
	ShareStats *DocShareStats `protobuf:"bytes,20,opt,name=share_stats,json=shareStats,proto3" json:"share_stats,omitempty"`
}

func (x *CollectionQA) Reset() {
	*x = CollectionQA{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionQA) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionQA) ProtoMessage() {}

func (x *CollectionQA) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionQA.ProtoReflect.Descriptor instead.
func (*CollectionQA) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{6}
}

func (x *CollectionQA) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CollectionQA) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *CollectionQA) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *CollectionQA) GetAssistants() []*ai.Assistant {
	if x != nil {
		return x.Assistants
	}
	return nil
}

func (x *CollectionQA) GetState() ai.DocState {
	if x != nil {
		return x.State
	}
	return ai.DocState(0)
}

func (x *CollectionQA) GetContributor() []*ai.Contributor {
	if x != nil {
		return x.Contributor
	}
	return nil
}

func (x *CollectionQA) GetReference() []*ai.DocReference {
	if x != nil {
		return x.Reference
	}
	return nil
}

func (x *CollectionQA) GetHitCount() uint32 {
	if x != nil {
		return x.HitCount
	}
	return 0
}

func (x *CollectionQA) GetCreateBy() *Operator {
	if x != nil {
		return x.CreateBy
	}
	return nil
}

func (x *CollectionQA) GetUpdateBy() *Operator {
	if x != nil {
		return x.UpdateBy
	}
	return nil
}

func (x *CollectionQA) GetUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateDate
	}
	return nil
}

func (x *CollectionQA) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *CollectionQA) GetVersionLag() uint64 {
	if x != nil {
		return x.VersionLag
	}
	return 0
}

func (x *CollectionQA) GetShowContributor() uint32 {
	if x != nil {
		return x.ShowContributor
	}
	return 0
}

func (x *CollectionQA) GetStates() []*ai.DocAssistantState {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *CollectionQA) GetLabels() []*ai.CustomLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CollectionQA) GetMatchPatterns() []ai.DocMatchPattern {
	if x != nil {
		return x.MatchPatterns
	}
	return nil
}

func (x *CollectionQA) GetQuestionOversize() bool {
	if x != nil {
		return x.QuestionOversize
	}
	return false
}

func (x *CollectionQA) GetHasRepeated() bool {
	if x != nil {
		return x.HasRepeated
	}
	return false
}

func (x *CollectionQA) GetShareStats() *DocShareStats {
	if x != nil {
		return x.ShareStats
	}
	return nil
}

type Chat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	CreateBy      *iam.UserInfo          `protobuf:"bytes,3,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	UpdateDate    *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`
	CreateDate    *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	ChatType      ai.ChatType            `protobuf:"varint,6,opt,name=chat_type,json=chatType,proto3,enum=tanlive.ai.ChatType" json:"chat_type,omitempty"`
	AssistantId   uint64                 `protobuf:"varint,7,opt,name=assistant_id,json=assistantId,proto3" json:"assistant_id,omitempty"`
	AssistantName string                 `protobuf:"bytes,8,opt,name=assistant_name,json=assistantName,proto3" json:"assistant_name,omitempty"`
	ChatState     ai.ChatCurrentState    `protobuf:"varint,9,opt,name=chat_state,json=chatState,proto3,enum=tanlive.ai.ChatCurrentState" json:"chat_state,omitempty"`
	// 当前服务状态
	SupportType ai.ChatSupportType `protobuf:"varint,10,opt,name=support_type,json=supportType,proto3,enum=tanlive.ai.ChatSupportType" json:"support_type,omitempty"`
	// 对话中问题数量
	QuestionCnt uint32 `protobuf:"varint,11,opt,name=question_cnt,json=questionCnt,proto3" json:"question_cnt,omitempty"`
	// 自定义标签kv对
	Labels          []*ai.CustomLabel `protobuf:"bytes,12,rep,name=labels,proto3" json:"labels,omitempty"`
	RejectJobResult uint32            `protobuf:"varint,13,opt,name=reject_job_result,json=rejectJobResult,proto3" json:"reject_job_result,omitempty"`
	RegionCode      string            `protobuf:"bytes,14,opt,name=region_code,json=regionCode,proto3" json:"region_code,omitempty"`
	// 是否转过人工服务
	IsManual    int32          `protobuf:"varint,15,opt,name=is_manual,json=isManual,proto3" json:"is_manual,omitempty"`
	RatingScale ai.RatingScale `protobuf:"varint,16,opt,name=rating_scale,json=ratingScale,proto3,enum=tanlive.ai.RatingScale" json:"rating_scale,omitempty"`
	DocHits     float32        `protobuf:"fixed32,17,opt,name=doc_hits,json=docHits,proto3" json:"doc_hits,omitempty"`
	AvgDuration float32        `protobuf:"fixed32,18,opt,name=avg_duration,json=avgDuration,proto3" json:"avg_duration,omitempty"`
}

func (x *Chat) Reset() {
	*x = Chat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Chat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chat) ProtoMessage() {}

func (x *Chat) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chat.ProtoReflect.Descriptor instead.
func (*Chat) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{7}
}

func (x *Chat) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Chat) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Chat) GetCreateBy() *iam.UserInfo {
	if x != nil {
		return x.CreateBy
	}
	return nil
}

func (x *Chat) GetUpdateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateDate
	}
	return nil
}

func (x *Chat) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *Chat) GetChatType() ai.ChatType {
	if x != nil {
		return x.ChatType
	}
	return ai.ChatType(0)
}

func (x *Chat) GetAssistantId() uint64 {
	if x != nil {
		return x.AssistantId
	}
	return 0
}

func (x *Chat) GetAssistantName() string {
	if x != nil {
		return x.AssistantName
	}
	return ""
}

func (x *Chat) GetChatState() ai.ChatCurrentState {
	if x != nil {
		return x.ChatState
	}
	return ai.ChatCurrentState(0)
}

func (x *Chat) GetSupportType() ai.ChatSupportType {
	if x != nil {
		return x.SupportType
	}
	return ai.ChatSupportType(0)
}

func (x *Chat) GetQuestionCnt() uint32 {
	if x != nil {
		return x.QuestionCnt
	}
	return 0
}

func (x *Chat) GetLabels() []*ai.CustomLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Chat) GetRejectJobResult() uint32 {
	if x != nil {
		return x.RejectJobResult
	}
	return 0
}

func (x *Chat) GetRegionCode() string {
	if x != nil {
		return x.RegionCode
	}
	return ""
}

func (x *Chat) GetIsManual() int32 {
	if x != nil {
		return x.IsManual
	}
	return 0
}

func (x *Chat) GetRatingScale() ai.RatingScale {
	if x != nil {
		return x.RatingScale
	}
	return ai.RatingScale(0)
}

func (x *Chat) GetDocHits() float32 {
	if x != nil {
		return x.DocHits
	}
	return 0
}

func (x *Chat) GetAvgDuration() float32 {
	if x != nil {
		return x.AvgDuration
	}
	return 0
}

type ChatDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title    string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Messages []*ai.EventChatMessage `protobuf:"bytes,3,rep,name=messages,proto3" json:"messages,omitempty"`
	// 地区
	Region     base.Region            `protobuf:"varint,4,opt,name=region,proto3,enum=tanlive.base.Region" json:"region,omitempty"`
	CreateBy   *iam.UserInfo          `protobuf:"bytes,5,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	FinishDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=finish_date,json=finishDate,proto3" json:"finish_date,omitempty"`
	CreateDate *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	ChatType   ai.ChatType            `protobuf:"varint,8,opt,name=chat_type,json=chatType,proto3,enum=tanlive.ai.ChatType" json:"chat_type,omitempty"`
	ChatState  ai.ChatCurrentState    `protobuf:"varint,9,opt,name=chat_state,json=chatState,proto3,enum=tanlive.ai.ChatCurrentState" json:"chat_state,omitempty"`
	// 当前服务状态
	SupportType ai.ChatSupportType `protobuf:"varint,10,opt,name=support_type,json=supportType,proto3,enum=tanlive.ai.ChatSupportType" json:"support_type,omitempty"`
	// 微信客服助手头像
	AssistantAvatar string `protobuf:"bytes,11,opt,name=assistant_avatar,json=assistantAvatar,proto3" json:"assistant_avatar,omitempty"`
	// 非web端时通过次字段返回消息详情
	Records     []*ai.ChatSendRecordInfo `protobuf:"bytes,12,rep,name=records,proto3" json:"records,omitempty"`
	AssistantId uint64                   `protobuf:"varint,13,opt,name=assistant_id,json=assistantId,proto3" json:"assistant_id,omitempty"`
}

func (x *ChatDetail) Reset() {
	*x = ChatDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatDetail) ProtoMessage() {}

func (x *ChatDetail) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatDetail.ProtoReflect.Descriptor instead.
func (*ChatDetail) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{8}
}

func (x *ChatDetail) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChatDetail) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ChatDetail) GetMessages() []*ai.EventChatMessage {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *ChatDetail) GetRegion() base.Region {
	if x != nil {
		return x.Region
	}
	return base.Region(0)
}

func (x *ChatDetail) GetCreateBy() *iam.UserInfo {
	if x != nil {
		return x.CreateBy
	}
	return nil
}

func (x *ChatDetail) GetFinishDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FinishDate
	}
	return nil
}

func (x *ChatDetail) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *ChatDetail) GetChatType() ai.ChatType {
	if x != nil {
		return x.ChatType
	}
	return ai.ChatType(0)
}

func (x *ChatDetail) GetChatState() ai.ChatCurrentState {
	if x != nil {
		return x.ChatState
	}
	return ai.ChatCurrentState(0)
}

func (x *ChatDetail) GetSupportType() ai.ChatSupportType {
	if x != nil {
		return x.SupportType
	}
	return ai.ChatSupportType(0)
}

func (x *ChatDetail) GetAssistantAvatar() string {
	if x != nil {
		return x.AssistantAvatar
	}
	return ""
}

func (x *ChatDetail) GetRecords() []*ai.ChatSendRecordInfo {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *ChatDetail) GetAssistantId() uint64 {
	if x != nil {
		return x.AssistantId
	}
	return 0
}

type ChatMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint64                             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ChatId      uint64                             `protobuf:"varint,2,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"`
	Text        string                             `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	CreateDate  *timestamppb.Timestamp             `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`
	Type        int32                              `protobuf:"varint,6,opt,name=type,proto3" json:"type,omitempty"`
	RatingScale ai.RatingScale                     `protobuf:"varint,7,opt,name=rating_scale,json=ratingScale,proto3,enum=tanlive.ai.RatingScale" json:"rating_scale,omitempty"`
	Docs        []*ChatMessage_ChatMessageDoc      `protobuf:"bytes,8,rep,name=docs,proto3" json:"docs,omitempty"`
	Link        string                             `protobuf:"bytes,9,opt,name=link,proto3" json:"link,omitempty"`
	IsUgcLink   bool                               `protobuf:"varint,10,opt,name=is_ugc_link,json=isUgcLink,proto3" json:"is_ugc_link,omitempty"`
	UgcType     base.DataType                      `protobuf:"varint,11,opt,name=ugc_type,json=ugcType,proto3,enum=tanlive.base.DataType" json:"ugc_type,omitempty"`
	QuestionId  uint64                             `protobuf:"varint,12,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	SqlQuery    string                             `protobuf:"bytes,13,opt,name=sql_query,json=sqlQuery,proto3" json:"sql_query,omitempty"`
	Filter      []*ai.ChatMessageContentFilterItem `protobuf:"bytes,14,rep,name=filter,proto3" json:"filter,omitempty"`
	Ugcs        []*ChatMessage_ChatMessageUgc      `protobuf:"bytes,15,rep,name=ugcs,proto3" json:"ugcs,omitempty"`
	ProcessTime *base.TimeRange                    `protobuf:"bytes,20,opt,name=process_time,json=processTime,proto3" json:"process_time,omitempty"`
}

func (x *ChatMessage) Reset() {
	*x = ChatMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessage) ProtoMessage() {}

func (x *ChatMessage) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessage.ProtoReflect.Descriptor instead.
func (*ChatMessage) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{9}
}

func (x *ChatMessage) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChatMessage) GetChatId() uint64 {
	if x != nil {
		return x.ChatId
	}
	return 0
}

func (x *ChatMessage) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ChatMessage) GetCreateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateDate
	}
	return nil
}

func (x *ChatMessage) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ChatMessage) GetRatingScale() ai.RatingScale {
	if x != nil {
		return x.RatingScale
	}
	return ai.RatingScale(0)
}

func (x *ChatMessage) GetDocs() []*ChatMessage_ChatMessageDoc {
	if x != nil {
		return x.Docs
	}
	return nil
}

func (x *ChatMessage) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *ChatMessage) GetIsUgcLink() bool {
	if x != nil {
		return x.IsUgcLink
	}
	return false
}

func (x *ChatMessage) GetUgcType() base.DataType {
	if x != nil {
		return x.UgcType
	}
	return base.DataType(0)
}

func (x *ChatMessage) GetQuestionId() uint64 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

func (x *ChatMessage) GetSqlQuery() string {
	if x != nil {
		return x.SqlQuery
	}
	return ""
}

func (x *ChatMessage) GetFilter() []*ai.ChatMessageContentFilterItem {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ChatMessage) GetUgcs() []*ChatMessage_ChatMessageUgc {
	if x != nil {
		return x.Ugcs
	}
	return nil
}

func (x *ChatMessage) GetProcessTime() *base.TimeRange {
	if x != nil {
		return x.ProcessTime
	}
	return nil
}

type ChatMessage_ChatMessageDoc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UgcId       uint64             `protobuf:"varint,1,opt,name=ugc_id,json=ugcId,proto3" json:"ugc_id,omitempty"`
	UgcType     uint32             `protobuf:"varint,2,opt,name=ugc_type,json=ugcType,proto3" json:"ugc_type,omitempty"`
	Contributor []*ai.Contributor  `protobuf:"bytes,5,rep,name=contributor,proto3" json:"contributor,omitempty"`
	DataType    uint32             `protobuf:"varint,6,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"`
	Reference   []*ai.DocReference `protobuf:"bytes,7,rep,name=reference,proto3" json:"reference,omitempty"`
}

func (x *ChatMessage_ChatMessageDoc) Reset() {
	*x = ChatMessage_ChatMessageDoc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatMessage_ChatMessageDoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessage_ChatMessageDoc) ProtoMessage() {}

func (x *ChatMessage_ChatMessageDoc) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessage_ChatMessageDoc.ProtoReflect.Descriptor instead.
func (*ChatMessage_ChatMessageDoc) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ChatMessage_ChatMessageDoc) GetUgcId() uint64 {
	if x != nil {
		return x.UgcId
	}
	return 0
}

func (x *ChatMessage_ChatMessageDoc) GetUgcType() uint32 {
	if x != nil {
		return x.UgcType
	}
	return 0
}

func (x *ChatMessage_ChatMessageDoc) GetContributor() []*ai.Contributor {
	if x != nil {
		return x.Contributor
	}
	return nil
}

func (x *ChatMessage_ChatMessageDoc) GetDataType() uint32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

func (x *ChatMessage_ChatMessageDoc) GetReference() []*ai.DocReference {
	if x != nil {
		return x.Reference
	}
	return nil
}

type ChatMessage_ChatMessageUgcCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	LogoUrl string `protobuf:"bytes,2,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	Id      uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	Tags    string `protobuf:"bytes,4,opt,name=tags,proto3" json:"tags,omitempty"`
	HashId  string `protobuf:"bytes,5,opt,name=hash_id,json=hashId,proto3" json:"hash_id,omitempty"`
}

func (x *ChatMessage_ChatMessageUgcCard) Reset() {
	*x = ChatMessage_ChatMessageUgcCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatMessage_ChatMessageUgcCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessage_ChatMessageUgcCard) ProtoMessage() {}

func (x *ChatMessage_ChatMessageUgcCard) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessage_ChatMessageUgcCard.ProtoReflect.Descriptor instead.
func (*ChatMessage_ChatMessageUgcCard) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{9, 1}
}

func (x *ChatMessage_ChatMessageUgcCard) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChatMessage_ChatMessageUgcCard) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *ChatMessage_ChatMessageUgcCard) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChatMessage_ChatMessageUgcCard) GetTags() string {
	if x != nil {
		return x.Tags
	}
	return ""
}

func (x *ChatMessage_ChatMessageUgcCard) GetHashId() string {
	if x != nil {
		return x.HashId
	}
	return ""
}

type ChatMessage_ChatMessageUgc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UgcType   base.DataType                      `protobuf:"varint,2,opt,name=ugc_type,json=ugcType,proto3,enum=tanlive.base.DataType" json:"ugc_type,omitempty"`
	Filter    []*ai.ChatMessageContentFilterItem `protobuf:"bytes,3,rep,name=filter,proto3" json:"filter,omitempty"`
	Cards     []*ChatMessage_ChatMessageUgcCard  `protobuf:"bytes,4,rep,name=cards,proto3" json:"cards,omitempty"`
	IsUgcLink bool                               `protobuf:"varint,5,opt,name=is_ugc_link,json=isUgcLink,proto3" json:"is_ugc_link,omitempty"`
	UgcIds    []uint64                           `protobuf:"varint,6,rep,packed,name=ugc_ids,json=ugcIds,proto3" json:"ugc_ids,omitempty"`
}

func (x *ChatMessage_ChatMessageUgc) Reset() {
	*x = ChatMessage_ChatMessageUgc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatMessage_ChatMessageUgc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessage_ChatMessageUgc) ProtoMessage() {}

func (x *ChatMessage_ChatMessageUgc) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessage_ChatMessageUgc.ProtoReflect.Descriptor instead.
func (*ChatMessage_ChatMessageUgc) Descriptor() ([]byte, []int) {
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP(), []int{9, 2}
}

func (x *ChatMessage_ChatMessageUgc) GetUgcType() base.DataType {
	if x != nil {
		return x.UgcType
	}
	return base.DataType(0)
}

func (x *ChatMessage_ChatMessageUgc) GetFilter() []*ai.ChatMessageContentFilterItem {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ChatMessage_ChatMessageUgc) GetCards() []*ChatMessage_ChatMessageUgcCard {
	if x != nil {
		return x.Cards
	}
	return nil
}

func (x *ChatMessage_ChatMessageUgc) GetIsUgcLink() bool {
	if x != nil {
		return x.IsUgcLink
	}
	return false
}

func (x *ChatMessage_ChatMessageUgc) GetUgcIds() []uint64 {
	if x != nil {
		return x.UgcIds
	}
	return nil
}

var File_tanlive_bff_mgmt_ai_ai_proto protoreflect.FileDescriptor

var file_tanlive_bff_mgmt_ai_ai_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x66, 0x66, 0x2d, 0x6d, 0x67,
	0x6d, 0x74, 0x2f, 0x61, 0x69, 0x2f, 0x61, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x61, 0x69, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x61, 0x69,
	0x2f, 0x61, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x69, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x75,
	0x67, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x9c, 0x01, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x2e,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65,
	0x61, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x65, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x22, 0xea, 0x01, 0x0a, 0x0d, 0x44, 0x6f, 0x63, 0x53, 0x68, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x73, 0x12, 0x51, 0x0a, 0x11, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74,
	0x2e, 0x61, 0x69, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x52, 0x10, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x41, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x0c, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x5f,
	0x74, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x61,
	0x69, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x0b, 0x73, 0x68,
	0x61, 0x72, 0x65, 0x64, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x42, 0x0a, 0x0c, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x61, 0x69, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x0b, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x22, 0x4e, 0x0a,
	0x0f, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x22, 0x30, 0x0a,
	0x0a, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x30, 0x0a, 0x0a, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0xb0, 0x0c, 0x0a, 0x12, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x61, 0x73, 0x73,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73,
	0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0b,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x69, 0x74,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x68, 0x69,
	0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x08, 0x75, 0x67, 0x63, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x75, 0x67, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x75, 0x67, 0x63,
	0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x75, 0x67, 0x63, 0x49, 0x64,
	0x12, 0x3a, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66,
	0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x61, 0x69, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3a, 0x0a, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x61, 0x69, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x61,
	0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x4c, 0x61, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x70, 0x61, 0x72,
	0x73, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x68,
	0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x73, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x6f, 0x72, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18,
	0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07,
	0x69, 0x73, 0x5f, 0x63, 0x6f, 0x70, 0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69,
	0x73, 0x43, 0x6f, 0x70, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x67, 0x63, 0x5f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x67, 0x63, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12,
	0x40, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x3f, 0x0a, 0x06, 0x63, 0x6f, 0x70, 0x69, 0x65, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x06, 0x63, 0x6f, 0x70, 0x69,
	0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x67, 0x63, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x69, 0x64,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x67, 0x63, 0x48, 0x61, 0x73, 0x68, 0x69,
	0x64, 0x12, 0x42, 0x0a, 0x0d, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18,
	0x1a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x61, 0x69, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x36, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x48,
	0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x61, 0x73, 0x5f, 0x72, 0x65,
	0x66, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76,
	0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x41, 0x73, 0x52, 0x65, 0x66, 0x52, 0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x41, 0x73, 0x52, 0x65, 0x66, 0x12, 0x37, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x73,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x50, 0x61, 0x72,
	0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x70, 0x61, 0x72, 0x73, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x31, 0x0a, 0x15, 0x68, 0x61, 0x73, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x64, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x12, 0x68, 0x61, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x64, 0x54, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x61, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68, 0x61, 0x73, 0x52,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x3a, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x44, 0x61, 0x74,
	0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f,
	0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x43, 0x0a, 0x0b, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x22,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62,
	0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x53, 0x68,
	0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x0a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x22, 0xc4, 0x07, 0x0a, 0x0c, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x51, 0x41, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x0a, 0x61, 0x73, 0x73,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73,
	0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0b,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x12, 0x36, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x68, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67,
	0x6d, 0x74, 0x2e, 0x61, 0x69, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3a, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x61,
	0x69, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x61, 0x67, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x67, 0x12,
	0x29, 0x0a, 0x10, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x6f, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x73, 0x68, 0x6f, 0x77, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x41, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x2f, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x42, 0x0a, 0x0e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x70, 0x61, 0x74, 0x74,
	0x65, 0x72, 0x6e, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x44, 0x6f, 0x63, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x52, 0x0d, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x6e, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x10, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x61, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68, 0x61, 0x73, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x43, 0x0a, 0x0b, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x61,
	0x69, 0x2e, 0x44, 0x6f, 0x63, 0x53, 0x68, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52,
	0x0a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x22, 0x8c, 0x06, 0x0a, 0x04,
	0x43, 0x68, 0x61, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x32, 0x0a, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x08, 0x63, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x63, 0x68, 0x61, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x3e, 0x0a, 0x0c, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18,
	0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x61, 0x69, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0f, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x73, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x12, 0x3a, 0x0a, 0x0c, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2e, 0x61, 0x69, 0x2e, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x52,
	0x0b, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x64, 0x6f, 0x63, 0x5f, 0x68, 0x69, 0x74, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07,
	0x64, 0x6f, 0x63, 0x48, 0x69, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x76, 0x67, 0x5f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x61,
	0x76, 0x67, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x80, 0x05, 0x0a, 0x0a, 0x43,
	0x68, 0x61, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x38, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c,
	0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x66,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x66, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x63, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x63, 0x68, 0x61, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3e, 0x0a, 0x0c, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x53, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x38, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43,
	0x68, 0x61, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x73,
	0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0b, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xe8, 0x09,
	0x0a, 0x0b, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06,
	0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x72,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x52,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x52, 0x0b, 0x72, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x43, 0x0a, 0x04, 0x64, 0x6f, 0x63, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x66, 0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x44, 0x6f, 0x63, 0x52, 0x04, 0x64, 0x6f, 0x63, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b,
	0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x75, 0x67, 0x63, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x55, 0x67, 0x63, 0x4c, 0x69, 0x6e, 0x6b,
	0x12, 0x31, 0x0a, 0x08, 0x75, 0x67, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x75, 0x67, 0x63, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x71, 0x6c, 0x5f, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x71, 0x6c, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x12, 0x40, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43,
	0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x04, 0x75, 0x67, 0x63, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66, 0x66, 0x5f,
	0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x55,
	0x67, 0x63, 0x52, 0x04, 0x75, 0x67, 0x63, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x69, 0x6d, 0x65, 0x1a, 0xd2, 0x01, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x44, 0x6f, 0x63, 0x12, 0x15, 0x0a, 0x06, 0x75, 0x67, 0x63, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x75, 0x67, 0x63, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x75, 0x67, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x75, 0x67, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x36, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61,
	0x69, 0x2e, 0x44, 0x6f, 0x63, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x09,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x1a, 0x80, 0x01, 0x0a, 0x12, 0x43, 0x68,
	0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x55, 0x67, 0x63, 0x43, 0x61, 0x72, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x61, 0x73, 0x68, 0x49, 0x64, 0x1a, 0x89, 0x02, 0x0a,
	0x0e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x55, 0x67, 0x63, 0x12,
	0x31, 0x0a, 0x08, 0x75, 0x67, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x75, 0x67, 0x63, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x40, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x61, 0x69, 0x2e,
	0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x66,
	0x66, 0x5f, 0x6d, 0x67, 0x6d, 0x74, 0x2e, 0x61, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x55, 0x67, 0x63, 0x43, 0x61, 0x72, 0x64, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x12,
	0x1e, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x75, 0x67, 0x63, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x55, 0x67, 0x63, 0x4c, 0x69, 0x6e, 0x6b, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x67, 0x63, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x06, 0x75, 0x67, 0x63, 0x49, 0x64, 0x73, 0x42, 0x3d, 0x5a, 0x3b, 0x65, 0x2e, 0x63, 0x6f,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74,
	0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x66, 0x66, 0x2d,
	0x6d, 0x67, 0x6d, 0x74, 0x2f, 0x61, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_bff_mgmt_ai_ai_proto_rawDescOnce sync.Once
	file_tanlive_bff_mgmt_ai_ai_proto_rawDescData = file_tanlive_bff_mgmt_ai_ai_proto_rawDesc
)

func file_tanlive_bff_mgmt_ai_ai_proto_rawDescGZIP() []byte {
	file_tanlive_bff_mgmt_ai_ai_proto_rawDescOnce.Do(func() {
		file_tanlive_bff_mgmt_ai_ai_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_bff_mgmt_ai_ai_proto_rawDescData)
	})
	return file_tanlive_bff_mgmt_ai_ai_proto_rawDescData
}

var file_tanlive_bff_mgmt_ai_ai_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_tanlive_bff_mgmt_ai_ai_proto_goTypes = []interface{}{
	(*Operator)(nil),                        // 0: tanlive.bff_mgmt.ai.Operator
	(*DocShareStats)(nil),                   // 1: tanlive.bff_mgmt.ai.DocShareStats
	(*SharedAssistant)(nil),                 // 2: tanlive.bff_mgmt.ai.SharedAssistant
	(*SharedTeam)(nil),                      // 3: tanlive.bff_mgmt.ai.SharedTeam
	(*SharedUser)(nil),                      // 4: tanlive.bff_mgmt.ai.SharedUser
	(*CollectionTextFile)(nil),              // 5: tanlive.bff_mgmt.ai.CollectionTextFile
	(*CollectionQA)(nil),                    // 6: tanlive.bff_mgmt.ai.CollectionQA
	(*Chat)(nil),                            // 7: tanlive.bff_mgmt.ai.Chat
	(*ChatDetail)(nil),                      // 8: tanlive.bff_mgmt.ai.ChatDetail
	(*ChatMessage)(nil),                     // 9: tanlive.bff_mgmt.ai.ChatMessage
	(*ChatMessage_ChatMessageDoc)(nil),      // 10: tanlive.bff_mgmt.ai.ChatMessage.ChatMessageDoc
	(*ChatMessage_ChatMessageUgcCard)(nil),  // 11: tanlive.bff_mgmt.ai.ChatMessage.ChatMessageUgcCard
	(*ChatMessage_ChatMessageUgc)(nil),      // 12: tanlive.bff_mgmt.ai.ChatMessage.ChatMessageUgc
	(base.IdentityType)(0),                  // 13: tanlive.base.IdentityType
	(*ai.Assistant)(nil),                    // 14: tanlive.ai.Assistant
	(ai.DocState)(0),                        // 15: tanlive.ai.DocState
	(*ai.Contributor)(nil),                  // 16: tanlive.ai.Contributor
	(base.DataType)(0),                      // 17: tanlive.base.DataType
	(*timestamppb.Timestamp)(nil),           // 18: google.protobuf.Timestamp
	(*ai.DocAssistantState)(nil),            // 19: tanlive.ai.DocAssistantState
	(ai.DocContentState)(0),                 // 20: tanlive.ai.DocContentState
	(*ai.CustomLabel)(nil),                  // 21: tanlive.ai.CustomLabel
	(*ai.DocReference)(nil),                 // 22: tanlive.ai.DocReference
	(ai.DocFileDownloadAsRef)(0),            // 23: tanlive.ai.DocFileDownloadAsRef
	(ai.DocParseMode)(0),                    // 24: tanlive.ai.DocParseMode
	(ai.DocDataSource)(0),                   // 25: tanlive.ai.DocDataSource
	(ai.DocMatchPattern)(0),                 // 26: tanlive.ai.DocMatchPattern
	(*iam.UserInfo)(nil),                    // 27: tanlive.iam.UserInfo
	(ai.ChatType)(0),                        // 28: tanlive.ai.ChatType
	(ai.ChatCurrentState)(0),                // 29: tanlive.ai.ChatCurrentState
	(ai.ChatSupportType)(0),                 // 30: tanlive.ai.ChatSupportType
	(ai.RatingScale)(0),                     // 31: tanlive.ai.RatingScale
	(*ai.EventChatMessage)(nil),             // 32: tanlive.ai.EventChatMessage
	(base.Region)(0),                        // 33: tanlive.base.Region
	(*ai.ChatSendRecordInfo)(nil),           // 34: tanlive.ai.ChatSendRecordInfo
	(*ai.ChatMessageContentFilterItem)(nil), // 35: tanlive.ai.ChatMessageContentFilterItem
	(*base.TimeRange)(nil),                  // 36: tanlive.base.TimeRange
}
var file_tanlive_bff_mgmt_ai_ai_proto_depIdxs = []int32{
	13, // 0: tanlive.bff_mgmt.ai.Operator.type:type_name -> tanlive.base.IdentityType
	2,  // 1: tanlive.bff_mgmt.ai.DocShareStats.shared_assistants:type_name -> tanlive.bff_mgmt.ai.SharedAssistant
	3,  // 2: tanlive.bff_mgmt.ai.DocShareStats.shared_teams:type_name -> tanlive.bff_mgmt.ai.SharedTeam
	4,  // 3: tanlive.bff_mgmt.ai.DocShareStats.shared_users:type_name -> tanlive.bff_mgmt.ai.SharedUser
	14, // 4: tanlive.bff_mgmt.ai.CollectionTextFile.assistants:type_name -> tanlive.ai.Assistant
	15, // 5: tanlive.bff_mgmt.ai.CollectionTextFile.state:type_name -> tanlive.ai.DocState
	16, // 6: tanlive.bff_mgmt.ai.CollectionTextFile.contributor:type_name -> tanlive.ai.Contributor
	17, // 7: tanlive.bff_mgmt.ai.CollectionTextFile.ugc_type:type_name -> tanlive.base.DataType
	0,  // 8: tanlive.bff_mgmt.ai.CollectionTextFile.create_by:type_name -> tanlive.bff_mgmt.ai.Operator
	0,  // 9: tanlive.bff_mgmt.ai.CollectionTextFile.update_by:type_name -> tanlive.bff_mgmt.ai.Operator
	18, // 10: tanlive.bff_mgmt.ai.CollectionTextFile.update_date:type_name -> google.protobuf.Timestamp
	18, // 11: tanlive.bff_mgmt.ai.CollectionTextFile.create_date:type_name -> google.protobuf.Timestamp
	19, // 12: tanlive.bff_mgmt.ai.CollectionTextFile.states:type_name -> tanlive.ai.DocAssistantState
	20, // 13: tanlive.bff_mgmt.ai.CollectionTextFile.content_state:type_name -> tanlive.ai.DocContentState
	5,  // 14: tanlive.bff_mgmt.ai.CollectionTextFile.copies:type_name -> tanlive.bff_mgmt.ai.CollectionTextFile
	19, // 15: tanlive.bff_mgmt.ai.CollectionTextFile.shared_states:type_name -> tanlive.ai.DocAssistantState
	21, // 16: tanlive.bff_mgmt.ai.CollectionTextFile.labels:type_name -> tanlive.ai.CustomLabel
	22, // 17: tanlive.bff_mgmt.ai.CollectionTextFile.reference:type_name -> tanlive.ai.DocReference
	23, // 18: tanlive.bff_mgmt.ai.CollectionTextFile.download_as_ref:type_name -> tanlive.ai.DocFileDownloadAsRef
	24, // 19: tanlive.bff_mgmt.ai.CollectionTextFile.parse_mode:type_name -> tanlive.ai.DocParseMode
	25, // 20: tanlive.bff_mgmt.ai.CollectionTextFile.data_source:type_name -> tanlive.ai.DocDataSource
	1,  // 21: tanlive.bff_mgmt.ai.CollectionTextFile.share_stats:type_name -> tanlive.bff_mgmt.ai.DocShareStats
	14, // 22: tanlive.bff_mgmt.ai.CollectionQA.assistants:type_name -> tanlive.ai.Assistant
	15, // 23: tanlive.bff_mgmt.ai.CollectionQA.state:type_name -> tanlive.ai.DocState
	16, // 24: tanlive.bff_mgmt.ai.CollectionQA.contributor:type_name -> tanlive.ai.Contributor
	22, // 25: tanlive.bff_mgmt.ai.CollectionQA.reference:type_name -> tanlive.ai.DocReference
	0,  // 26: tanlive.bff_mgmt.ai.CollectionQA.create_by:type_name -> tanlive.bff_mgmt.ai.Operator
	0,  // 27: tanlive.bff_mgmt.ai.CollectionQA.update_by:type_name -> tanlive.bff_mgmt.ai.Operator
	18, // 28: tanlive.bff_mgmt.ai.CollectionQA.update_date:type_name -> google.protobuf.Timestamp
	18, // 29: tanlive.bff_mgmt.ai.CollectionQA.create_date:type_name -> google.protobuf.Timestamp
	19, // 30: tanlive.bff_mgmt.ai.CollectionQA.states:type_name -> tanlive.ai.DocAssistantState
	21, // 31: tanlive.bff_mgmt.ai.CollectionQA.labels:type_name -> tanlive.ai.CustomLabel
	26, // 32: tanlive.bff_mgmt.ai.CollectionQA.match_patterns:type_name -> tanlive.ai.DocMatchPattern
	1,  // 33: tanlive.bff_mgmt.ai.CollectionQA.share_stats:type_name -> tanlive.bff_mgmt.ai.DocShareStats
	27, // 34: tanlive.bff_mgmt.ai.Chat.create_by:type_name -> tanlive.iam.UserInfo
	18, // 35: tanlive.bff_mgmt.ai.Chat.update_date:type_name -> google.protobuf.Timestamp
	18, // 36: tanlive.bff_mgmt.ai.Chat.create_date:type_name -> google.protobuf.Timestamp
	28, // 37: tanlive.bff_mgmt.ai.Chat.chat_type:type_name -> tanlive.ai.ChatType
	29, // 38: tanlive.bff_mgmt.ai.Chat.chat_state:type_name -> tanlive.ai.ChatCurrentState
	30, // 39: tanlive.bff_mgmt.ai.Chat.support_type:type_name -> tanlive.ai.ChatSupportType
	21, // 40: tanlive.bff_mgmt.ai.Chat.labels:type_name -> tanlive.ai.CustomLabel
	31, // 41: tanlive.bff_mgmt.ai.Chat.rating_scale:type_name -> tanlive.ai.RatingScale
	32, // 42: tanlive.bff_mgmt.ai.ChatDetail.messages:type_name -> tanlive.ai.EventChatMessage
	33, // 43: tanlive.bff_mgmt.ai.ChatDetail.region:type_name -> tanlive.base.Region
	27, // 44: tanlive.bff_mgmt.ai.ChatDetail.create_by:type_name -> tanlive.iam.UserInfo
	18, // 45: tanlive.bff_mgmt.ai.ChatDetail.finish_date:type_name -> google.protobuf.Timestamp
	18, // 46: tanlive.bff_mgmt.ai.ChatDetail.create_date:type_name -> google.protobuf.Timestamp
	28, // 47: tanlive.bff_mgmt.ai.ChatDetail.chat_type:type_name -> tanlive.ai.ChatType
	29, // 48: tanlive.bff_mgmt.ai.ChatDetail.chat_state:type_name -> tanlive.ai.ChatCurrentState
	30, // 49: tanlive.bff_mgmt.ai.ChatDetail.support_type:type_name -> tanlive.ai.ChatSupportType
	34, // 50: tanlive.bff_mgmt.ai.ChatDetail.records:type_name -> tanlive.ai.ChatSendRecordInfo
	18, // 51: tanlive.bff_mgmt.ai.ChatMessage.create_date:type_name -> google.protobuf.Timestamp
	31, // 52: tanlive.bff_mgmt.ai.ChatMessage.rating_scale:type_name -> tanlive.ai.RatingScale
	10, // 53: tanlive.bff_mgmt.ai.ChatMessage.docs:type_name -> tanlive.bff_mgmt.ai.ChatMessage.ChatMessageDoc
	17, // 54: tanlive.bff_mgmt.ai.ChatMessage.ugc_type:type_name -> tanlive.base.DataType
	35, // 55: tanlive.bff_mgmt.ai.ChatMessage.filter:type_name -> tanlive.ai.ChatMessageContentFilterItem
	12, // 56: tanlive.bff_mgmt.ai.ChatMessage.ugcs:type_name -> tanlive.bff_mgmt.ai.ChatMessage.ChatMessageUgc
	36, // 57: tanlive.bff_mgmt.ai.ChatMessage.process_time:type_name -> tanlive.base.TimeRange
	16, // 58: tanlive.bff_mgmt.ai.ChatMessage.ChatMessageDoc.contributor:type_name -> tanlive.ai.Contributor
	22, // 59: tanlive.bff_mgmt.ai.ChatMessage.ChatMessageDoc.reference:type_name -> tanlive.ai.DocReference
	17, // 60: tanlive.bff_mgmt.ai.ChatMessage.ChatMessageUgc.ugc_type:type_name -> tanlive.base.DataType
	35, // 61: tanlive.bff_mgmt.ai.ChatMessage.ChatMessageUgc.filter:type_name -> tanlive.ai.ChatMessageContentFilterItem
	11, // 62: tanlive.bff_mgmt.ai.ChatMessage.ChatMessageUgc.cards:type_name -> tanlive.bff_mgmt.ai.ChatMessage.ChatMessageUgcCard
	63, // [63:63] is the sub-list for method output_type
	63, // [63:63] is the sub-list for method input_type
	63, // [63:63] is the sub-list for extension type_name
	63, // [63:63] is the sub-list for extension extendee
	0,  // [0:63] is the sub-list for field type_name
}

func init() { file_tanlive_bff_mgmt_ai_ai_proto_init() }
func file_tanlive_bff_mgmt_ai_ai_proto_init() {
	if File_tanlive_bff_mgmt_ai_ai_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Operator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocShareStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SharedAssistant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SharedTeam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SharedUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionTextFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionQA); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Chat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatMessage_ChatMessageDoc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatMessage_ChatMessageUgcCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_bff_mgmt_ai_ai_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatMessage_ChatMessageUgc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_bff_mgmt_ai_ai_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanlive_bff_mgmt_ai_ai_proto_goTypes,
		DependencyIndexes: file_tanlive_bff_mgmt_ai_ai_proto_depIdxs,
		MessageInfos:      file_tanlive_bff_mgmt_ai_ai_proto_msgTypes,
	}.Build()
	File_tanlive_bff_mgmt_ai_ai_proto = out.File
	file_tanlive_bff_mgmt_ai_ai_proto_rawDesc = nil
	file_tanlive_bff_mgmt_ai_ai_proto_goTypes = nil
	file_tanlive_bff_mgmt_ai_ai_proto_depIdxs = nil
}
