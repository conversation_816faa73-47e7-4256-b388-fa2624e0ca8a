// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: tanlive/events/ai.proto

package events

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 动作
type SystemDocChanged_Action int32

const (
	SystemDocChanged_ACTION_UNSPECIFIED SystemDocChanged_Action = 0
	// 发布
	SystemDocChanged_ACTION_PUBLISH SystemDocChanged_Action = 1
	// 下架
	SystemDocChanged_ACTION_UNPUBLISH SystemDocChanged_Action = 2
	// 删除
	SystemDocChanged_ACTION_DELETE SystemDocChanged_Action = 3
)

// Enum value maps for SystemDocChanged_Action.
var (
	SystemDocChanged_Action_name = map[int32]string{
		0: "ACTION_UNSPECIFIED",
		1: "ACTION_PUBLISH",
		2: "ACTION_UNPUBLISH",
		3: "ACTION_DELETE",
	}
	SystemDocChanged_Action_value = map[string]int32{
		"ACTION_UNSPECIFIED": 0,
		"ACTION_PUBLISH":     1,
		"ACTION_UNPUBLISH":   2,
		"ACTION_DELETE":      3,
	}
)

func (x SystemDocChanged_Action) Enum() *SystemDocChanged_Action {
	p := new(SystemDocChanged_Action)
	*p = x
	return p
}

func (x SystemDocChanged_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SystemDocChanged_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_tanlive_events_ai_proto_enumTypes[0].Descriptor()
}

func (SystemDocChanged_Action) Type() protoreflect.EnumType {
	return &file_tanlive_events_ai_proto_enumTypes[0]
}

func (x SystemDocChanged_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SystemDocChanged_Action.Descriptor instead.
func (SystemDocChanged_Action) EnumDescriptor() ([]byte, []int) {
	return file_tanlive_events_ai_proto_rawDescGZIP(), []int{0, 0}
}

type SystemDocChanged struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 动作
	Action SystemDocChanged_Action `protobuf:"varint,1,opt,name=action,proto3,enum=tanlive.events.SystemDocChanged_Action" json:"action,omitempty"`
	// 文档内容
	Doc *SystemDocChanged_Doc `protobuf:"bytes,2,opt,name=doc,proto3" json:"doc,omitempty"`
}

func (x *SystemDocChanged) Reset() {
	*x = SystemDocChanged{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_events_ai_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemDocChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemDocChanged) ProtoMessage() {}

func (x *SystemDocChanged) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_events_ai_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemDocChanged.ProtoReflect.Descriptor instead.
func (*SystemDocChanged) Descriptor() ([]byte, []int) {
	return file_tanlive_events_ai_proto_rawDescGZIP(), []int{0}
}

func (x *SystemDocChanged) GetAction() SystemDocChanged_Action {
	if x != nil {
		return x.Action
	}
	return SystemDocChanged_ACTION_UNSPECIFIED
}

func (x *SystemDocChanged) GetDoc() *SystemDocChanged_Doc {
	if x != nil {
		return x.Doc
	}
	return nil
}

type CollectionReindex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务ID
	TaskId uint64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *CollectionReindex) Reset() {
	*x = CollectionReindex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_events_ai_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionReindex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionReindex) ProtoMessage() {}

func (x *CollectionReindex) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_events_ai_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionReindex.ProtoReflect.Descriptor instead.
func (*CollectionReindex) Descriptor() ([]byte, []int) {
	return file_tanlive_events_ai_proto_rawDescGZIP(), []int{1}
}

func (x *CollectionReindex) GetTaskId() uint64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

type DocToCollection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务ID
	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// 文档ID
	DocId uint64 `protobuf:"varint,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// Collection ID
	CollectionId uint64 `protobuf:"varint,3,opt,name=collection_id,json=collectionId,proto3" json:"collection_id,omitempty"`
	// 是否为删除操作
	IsDelete bool `protobuf:"varint,4,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
}

func (x *DocToCollection) Reset() {
	*x = DocToCollection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_events_ai_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocToCollection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocToCollection) ProtoMessage() {}

func (x *DocToCollection) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_events_ai_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocToCollection.ProtoReflect.Descriptor instead.
func (*DocToCollection) Descriptor() ([]byte, []int) {
	return file_tanlive_events_ai_proto_rawDescGZIP(), []int{2}
}

func (x *DocToCollection) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *DocToCollection) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *DocToCollection) GetCollectionId() uint64 {
	if x != nil {
		return x.CollectionId
	}
	return 0
}

func (x *DocToCollection) GetIsDelete() bool {
	if x != nil {
		return x.IsDelete
	}
	return false
}

// 文件
type SystemDocChanged_File struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文件名
	FileName string `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// 文件路径
	FilePath string `protobuf:"bytes,2,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
}

func (x *SystemDocChanged_File) Reset() {
	*x = SystemDocChanged_File{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_events_ai_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemDocChanged_File) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemDocChanged_File) ProtoMessage() {}

func (x *SystemDocChanged_File) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_events_ai_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemDocChanged_File.ProtoReflect.Descriptor instead.
func (*SystemDocChanged_File) Descriptor() ([]byte, []int) {
	return file_tanlive_events_ai_proto_rawDescGZIP(), []int{0, 0}
}

func (x *SystemDocChanged_File) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *SystemDocChanged_File) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

// 文档内容
type SystemDocChanged_Doc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// UGC ID
	UgcId uint64 `protobuf:"varint,1,opt,name=ugc_id,json=ugcId,proto3" json:"ugc_id,omitempty"`
	// UGC类型
	UgcType base.DataType `protobuf:"varint,2,opt,name=ugc_type,json=ugcType,proto3,enum=tanlive.base.DataType" json:"ugc_type,omitempty"`
	// UGC标题
	UgcTitle string `protobuf:"bytes,3,opt,name=ugc_title,json=ugcTitle,proto3" json:"ugc_title,omitempty"`
	// 文档名称
	DocName string `protobuf:"bytes,4,opt,name=doc_name,json=docName,proto3" json:"doc_name,omitempty"`
	// 文档内容
	DocContent string `protobuf:"bytes,5,opt,name=doc_content,json=docContent,proto3" json:"doc_content,omitempty"`
	// 文件列表
	Files []*SystemDocChanged_File `protobuf:"bytes,6,rep,name=files,proto3" json:"files,omitempty"`
	// 贡献者
	Contributors []*base.Identity `protobuf:"bytes,7,rep,name=contributors,proto3" json:"contributors,omitempty"`
	// 更新者
	UpdateBy *base.Identity `protobuf:"bytes,8,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
}

func (x *SystemDocChanged_Doc) Reset() {
	*x = SystemDocChanged_Doc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanlive_events_ai_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemDocChanged_Doc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemDocChanged_Doc) ProtoMessage() {}

func (x *SystemDocChanged_Doc) ProtoReflect() protoreflect.Message {
	mi := &file_tanlive_events_ai_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemDocChanged_Doc.ProtoReflect.Descriptor instead.
func (*SystemDocChanged_Doc) Descriptor() ([]byte, []int) {
	return file_tanlive_events_ai_proto_rawDescGZIP(), []int{0, 1}
}

func (x *SystemDocChanged_Doc) GetUgcId() uint64 {
	if x != nil {
		return x.UgcId
	}
	return 0
}

func (x *SystemDocChanged_Doc) GetUgcType() base.DataType {
	if x != nil {
		return x.UgcType
	}
	return base.DataType(0)
}

func (x *SystemDocChanged_Doc) GetUgcTitle() string {
	if x != nil {
		return x.UgcTitle
	}
	return ""
}

func (x *SystemDocChanged_Doc) GetDocName() string {
	if x != nil {
		return x.DocName
	}
	return ""
}

func (x *SystemDocChanged_Doc) GetDocContent() string {
	if x != nil {
		return x.DocContent
	}
	return ""
}

func (x *SystemDocChanged_Doc) GetFiles() []*SystemDocChanged_File {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *SystemDocChanged_Doc) GetContributors() []*base.Identity {
	if x != nil {
		return x.Contributors
	}
	return nil
}

func (x *SystemDocChanged_Doc) GetUpdateBy() *base.Identity {
	if x != nil {
		return x.UpdateBy
	}
	return nil
}

var File_tanlive_events_ai_proto protoreflect.FileDescriptor

var file_tanlive_events_ai_proto_rawDesc = []byte{
	0x0a, 0x17, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73,
	0x2f, 0x61, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f,
	0x61, 0x69, 0x2f, 0x61, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x62, 0x61,
	0x73, 0x65, 0x2f, 0x75, 0x67, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x63, 0x72, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x85,
	0x05, 0x0a, 0x10, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x44, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x64, 0x12, 0x3f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x73, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x44, 0x6f, 0x63, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x64, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x03, 0x64, 0x6f, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x73, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x44, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x64, 0x2e, 0x44, 0x6f, 0x63, 0x52, 0x03, 0x64, 0x6f, 0x63, 0x1a, 0x40, 0x0a, 0x04,
	0x46, 0x69, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x1a, 0xd6,
	0x02, 0x0a, 0x03, 0x44, 0x6f, 0x63, 0x12, 0x15, 0x0a, 0x06, 0x75, 0x67, 0x63, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x75, 0x67, 0x63, 0x49, 0x64, 0x12, 0x31, 0x0a,
	0x08, 0x75, 0x67, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x75, 0x67, 0x63, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x75, 0x67, 0x63, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x67, 0x63, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x64, 0x6f, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x64, 0x6f, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x63, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x6f, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x05, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69,
	0x76, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x44, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74,
	0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f,
	0x72, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x08, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x22, 0x5d, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x10, 0x01, 0x12, 0x14, 0x0a,
	0x10, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53,
	0x48, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45,
	0x4c, 0x45, 0x54, 0x45, 0x10, 0x03, 0x22, 0x2c, 0x0a, 0x11, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x17, 0x0a, 0x07, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x22, 0x83, 0x01, 0x0a, 0x0f, 0x44, 0x6f, 0x63, 0x54, 0x6f, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x32, 0xef, 0x01, 0x0a, 0x07, 0x41,
	0x69, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x3c, 0x0a, 0x09, 0x4f, 0x6e, 0x43, 0x72, 0x6f, 0x6e,
	0x4a, 0x6f, 0x62, 0x12, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x73, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x4e, 0x0a, 0x12, 0x4f, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x44, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x12, 0x20, 0x2e, 0x74, 0x61, 0x6e,
	0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x44, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x50, 0x0a, 0x13, 0x4f, 0x6e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x21, 0x2e, 0x74, 0x61,
	0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x04, 0xc8, 0xc6, 0x27, 0x01, 0x32, 0x5e, 0x0a, 0x08,
	0x52, 0x61, 0x67, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x4c, 0x0a, 0x11, 0x4f, 0x6e, 0x44, 0x6f,
	0x63, 0x54, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44,
	0x6f, 0x63, 0x54, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x04, 0xc8, 0xc6, 0x27, 0x01, 0x42, 0x40, 0x5a, 0x3e,
	0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x74, 0x65, 0x6e,
	0x63, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x73, 0x76, 0x2f, 0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x74, 0x61, 0x6e, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tanlive_events_ai_proto_rawDescOnce sync.Once
	file_tanlive_events_ai_proto_rawDescData = file_tanlive_events_ai_proto_rawDesc
)

func file_tanlive_events_ai_proto_rawDescGZIP() []byte {
	file_tanlive_events_ai_proto_rawDescOnce.Do(func() {
		file_tanlive_events_ai_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanlive_events_ai_proto_rawDescData)
	})
	return file_tanlive_events_ai_proto_rawDescData
}

var file_tanlive_events_ai_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tanlive_events_ai_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_tanlive_events_ai_proto_goTypes = []interface{}{
	(SystemDocChanged_Action)(0),  // 0: tanlive.events.SystemDocChanged.Action
	(*SystemDocChanged)(nil),      // 1: tanlive.events.SystemDocChanged
	(*CollectionReindex)(nil),     // 2: tanlive.events.CollectionReindex
	(*DocToCollection)(nil),       // 3: tanlive.events.DocToCollection
	(*SystemDocChanged_File)(nil), // 4: tanlive.events.SystemDocChanged.File
	(*SystemDocChanged_Doc)(nil),  // 5: tanlive.events.SystemDocChanged.Doc
	(base.DataType)(0),            // 6: tanlive.base.DataType
	(*base.Identity)(nil),         // 7: tanlive.base.Identity
	(*CronJob)(nil),               // 8: tanlive.events.CronJob
	(*emptypb.Empty)(nil),         // 9: google.protobuf.Empty
}
var file_tanlive_events_ai_proto_depIdxs = []int32{
	0,  // 0: tanlive.events.SystemDocChanged.action:type_name -> tanlive.events.SystemDocChanged.Action
	5,  // 1: tanlive.events.SystemDocChanged.doc:type_name -> tanlive.events.SystemDocChanged.Doc
	6,  // 2: tanlive.events.SystemDocChanged.Doc.ugc_type:type_name -> tanlive.base.DataType
	4,  // 3: tanlive.events.SystemDocChanged.Doc.files:type_name -> tanlive.events.SystemDocChanged.File
	7,  // 4: tanlive.events.SystemDocChanged.Doc.contributors:type_name -> tanlive.base.Identity
	7,  // 5: tanlive.events.SystemDocChanged.Doc.update_by:type_name -> tanlive.base.Identity
	8,  // 6: tanlive.events.AiTopic.OnCronJob:input_type -> tanlive.events.CronJob
	1,  // 7: tanlive.events.AiTopic.OnSystemDocChanged:input_type -> tanlive.events.SystemDocChanged
	2,  // 8: tanlive.events.AiTopic.OnCollectionReindex:input_type -> tanlive.events.CollectionReindex
	3,  // 9: tanlive.events.RagTopic.OnDocToCollection:input_type -> tanlive.events.DocToCollection
	9,  // 10: tanlive.events.AiTopic.OnCronJob:output_type -> google.protobuf.Empty
	9,  // 11: tanlive.events.AiTopic.OnSystemDocChanged:output_type -> google.protobuf.Empty
	9,  // 12: tanlive.events.AiTopic.OnCollectionReindex:output_type -> google.protobuf.Empty
	9,  // 13: tanlive.events.RagTopic.OnDocToCollection:output_type -> google.protobuf.Empty
	10, // [10:14] is the sub-list for method output_type
	6,  // [6:10] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_tanlive_events_ai_proto_init() }
func file_tanlive_events_ai_proto_init() {
	if File_tanlive_events_ai_proto != nil {
		return
	}
	file_tanlive_events_cron_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_tanlive_events_ai_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemDocChanged); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_events_ai_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionReindex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_events_ai_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocToCollection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_events_ai_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemDocChanged_File); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanlive_events_ai_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemDocChanged_Doc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanlive_events_ai_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_tanlive_events_ai_proto_goTypes,
		DependencyIndexes: file_tanlive_events_ai_proto_depIdxs,
		EnumInfos:         file_tanlive_events_ai_proto_enumTypes,
		MessageInfos:      file_tanlive_events_ai_proto_msgTypes,
	}.Build()
	File_tanlive_events_ai_proto = out.File
	file_tanlive_events_ai_proto_rawDesc = nil
	file_tanlive_events_ai_proto_goTypes = nil
	file_tanlive_events_ai_proto_depIdxs = nil
}
