// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package events

import (
	context "context"
	event "e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/event"
	tanlive "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	v3 "github.com/asim/go-micro/v3"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
	proto "google.golang.org/protobuf/proto"
)

func (x *SystemDocChanged) MaskInLog() any {
	if x == nil {
		return (*SystemDocChanged)(nil)
	}

	y := proto.Clone(x).(*SystemDocChanged)
	if v, ok := any(y.Doc).(interface{ MaskInLog() any }); ok {
		y.Doc = v.MaskInLog().(*SystemDocChanged_Doc)
	}

	return y
}

func (x *SystemDocChanged) MaskInRpc() any {
	if x == nil {
		return (*SystemDocChanged)(nil)
	}

	y := x
	if v, ok := any(y.Doc).(interface{ MaskInRpc() any }); ok {
		y.Doc = v.MaskInRpc().(*SystemDocChanged_Doc)
	}

	return y
}

func (x *SystemDocChanged) MaskInBff() any {
	if x == nil {
		return (*SystemDocChanged)(nil)
	}

	y := x
	if v, ok := any(y.Doc).(interface{ MaskInBff() any }); ok {
		y.Doc = v.MaskInBff().(*SystemDocChanged_Doc)
	}

	return y
}

func (x *SystemDocChanged_Doc) MaskInLog() any {
	if x == nil {
		return (*SystemDocChanged_Doc)(nil)
	}

	y := proto.Clone(x).(*SystemDocChanged_Doc)
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Files[k] = vv.MaskInLog().(*SystemDocChanged_File)
		}
	}
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributors[k] = vv.MaskInLog().(*base.Identity)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *SystemDocChanged_Doc) MaskInRpc() any {
	if x == nil {
		return (*SystemDocChanged_Doc)(nil)
	}

	y := x
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Files[k] = vv.MaskInRpc().(*SystemDocChanged_File)
		}
	}
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributors[k] = vv.MaskInRpc().(*base.Identity)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *SystemDocChanged_Doc) MaskInBff() any {
	if x == nil {
		return (*SystemDocChanged_Doc)(nil)
	}

	y := x
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Files[k] = vv.MaskInBff().(*SystemDocChanged_File)
		}
	}
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributors[k] = vv.MaskInBff().(*base.Identity)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *SystemDocChanged) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Doc).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *SystemDocChanged_Doc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Files {
		if sanitizer, ok := any(x.Files[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributors {
		if sanitizer, ok := any(x.Contributors[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

type AiTopicListener interface {
	OnCronJob(context.Context, *CronJob) error
	OnSystemDocChanged(context.Context, *SystemDocChanged) error
	OnCollectionReindex(context.Context, *CollectionReindex) error
}

func SubscribeAiTopic(topic string, s server.Server, l AiTopicListener, queue string) error {
	return v3.RegisterSubscriber(topic, s, func(ctx context.Context, evt *tanlive.Event) error {
		if evt == nil || evt.Metadata == nil {
			return nil
		}
		ctx = event.MetadataToContext(ctx, evt.Metadata)
		switch evt.Metadata.Name {
		case "tanlive.events.CronJob":
			data := &CronJob{}
			if err := evt.Data.UnmarshalTo(data); err != nil {
				return err
			}
			return l.OnCronJob(ctx, data)
		case "tanlive.events.SystemDocChanged":
			data := &SystemDocChanged{}
			if err := evt.Data.UnmarshalTo(data); err != nil {
				return err
			}
			return l.OnSystemDocChanged(ctx, data)
		case "tanlive.events.CollectionReindex":
			data := &CollectionReindex{}
			if err := evt.Data.UnmarshalTo(data); err != nil {
				return err
			}
			return l.OnCollectionReindex(ctx, data)
		}
		return nil
	}, server.SubscriberQueue(queue))
}

type AiTopicPublisher interface {
	PublishCronJobEvent(context.Context, *CronJob) error
	PublishSystemDocChangedEvent(context.Context, *SystemDocChanged) error
	PublishCollectionReindexEvent(context.Context, *CollectionReindex) error
}

func NewAiTopicPublisher(topic string, c client.Client) AiTopicPublisher {
	return &aiTopicPublisher{
		event: event.NewEvent(topic, c),
	}
}

type aiTopicPublisher struct {
	event event.Event
}

func (p *aiTopicPublisher) PublishCronJobEvent(ctx context.Context, data *CronJob) error {
	return p.event.PublishEvent(ctx, "tanlive.events.CronJob", data)
}

func (p *aiTopicPublisher) PublishSystemDocChangedEvent(ctx context.Context, data *SystemDocChanged) error {
	return p.event.PublishEvent(ctx, "tanlive.events.SystemDocChanged", data)
}

func (p *aiTopicPublisher) PublishCollectionReindexEvent(ctx context.Context, data *CollectionReindex) error {
	return p.event.PublishEvent(ctx, "tanlive.events.CollectionReindex", data)
}

type RagTopicListener interface {
	OnDocToCollection(context.Context, *DocToCollection) error
}

func SubscribeRagTopic(topic string, s server.Server, l RagTopicListener, queue string) error {
	return v3.RegisterSubscriber(topic, s, func(ctx context.Context, evt *tanlive.Event) error {
		if evt == nil || evt.Metadata == nil {
			return nil
		}
		ctx = event.MetadataToContext(ctx, evt.Metadata)
		switch evt.Metadata.Name {
		case "tanlive.events.DocToCollection":
			data := &DocToCollection{}
			if err := evt.Data.UnmarshalTo(data); err != nil {
				return err
			}
			return l.OnDocToCollection(ctx, data)
		}
		return nil
	}, server.SubscriberQueue(queue))
}

type RagTopicPublisher interface {
	PublishDocToCollectionEvent(context.Context, *DocToCollection) error
}

func NewRagTopicPublisher(topic string, c client.Client) RagTopicPublisher {
	return &ragTopicPublisher{
		event: event.NewEvent(topic, c),
	}
}

type ragTopicPublisher struct {
	event event.Event
}

func (p *ragTopicPublisher) PublishDocToCollectionEvent(ctx context.Context, data *DocToCollection) error {
	return p.event.PublishEvent(ctx, "tanlive.events.DocToCollection", data)
}
