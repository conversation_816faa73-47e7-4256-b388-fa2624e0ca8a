syntax = "proto3";

package tanlive.events;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/events";

import "google/protobuf/empty.proto";
import "tanlive/ai/ai.proto";
import "tanlive/base/base.proto";
import "tanlive/base/ugc.proto";
import "tanlive/events/cron.proto";
import "tanlive/options.proto";

message SystemDocChanged {
  // 动作
  enum Action {
    ACTION_UNSPECIFIED = 0;
    // 发布
    ACTION_PUBLISH = 1;
    // 下架
    ACTION_UNPUBLISH = 2;
    // 删除
    ACTION_DELETE = 3;
  }
  // 文件
  message File {
    // 文件名
    string file_name = 1;
    // 文件路径
    string file_path = 2;
  }
  // 文档内容
  message Doc {
    // UGC ID
    uint64 ugc_id = 1;
    // UGC类型
    base.DataType ugc_type = 2;
    // UGC标题
    string ugc_title = 3;
    // 文档名称
    string doc_name = 4;
    // 文档内容
    string doc_content = 5;
    // 文件列表
    repeated File files = 6;
    // 贡献者
    repeated base.Identity contributors = 7;
    // 更新者
    base.Identity update_by = 8;
  }
  // 动作
  Action action = 1;
  // 文档内容
  Doc doc = 2;
}

message CollectionReindex {
  // 任务ID
  uint64 task_id = 1;
}

message DocToCollection {
  // 任务ID
  string task_id = 1;
  // 文档ID
  uint64 doc_id = 2;
  // Collection ID
  uint64 collection_id = 3;
  // 是否为删除操作
  bool is_delete = 4;
}

// Ai主题
service AiTopic {
  option (tanlive.event) = true;

  // 处理定时任务
  rpc OnCronJob(CronJob) returns (google.protobuf.Empty);
  // 系统数据文档变化
  rpc OnSystemDocChanged(SystemDocChanged) returns (google.protobuf.Empty);
  // 重建知识库索引
  rpc OnCollectionReindex(CollectionReindex) returns (google.protobuf.Empty);
}

// Rag主题
service RagTopic {
  option (tanlive.event) = true;

  // 将文档写入Collection
  rpc OnDocToCollection(DocToCollection) returns (google.protobuf.Empty);
}
