syntax = "proto3";

package tanlive.ai;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai";

import "google/protobuf/timestamp.proto";
import "tanlive/options.proto";
import "tanlive/base/ugc.proto";
import "tanlive/tag/tag.proto";
import "tanlive/base/base.proto";
import "tanlive/team/team.proto";

enum SearchCollectionType {
  SEARCH_COLLECTION_TYPE_UNSPECIFIED = 0;
  // 向量搜索
  SEARCH_COLLECTION_TYPE_VECTOR = 1;
  // 文本搜索
  SEARCH_COLLECTION_TYPE_TEXT = 2;
}

enum PipelineTaskFetchType {
  PIPELINE_TASK_FETCH_TYPE_UNSPECIFIED = 0;
  // 文本chat
  PIPELINE_TASK_FETCH_TYPE_TEXT = 1;
  // 多模态chat
  PIPELINE_TASK_FETCH_TYPE_VISION = 2;
  // 匹配QA
  PIPELINE_TASK_FETCH_TYPE_MATCH_QA = 3;
  // 直接回答默认回复
  PIPELINE_TASK_FETCH_TYPE_DEFAULT_ANSWER = 4;
}

enum ChatMessageType {
  CHAT_MESSAGE_TYPE_UNSPECIFIED = 0;
  // 用户消息
  CHAT_MESSAGE_TYPE_USER = 1;
  // 数据库查询
  CHAT_MESSAGE_TYPE_SQL_QUERY = 2;
  // collection查询
  CHAT_MESSAGE_TYPE_COLLECTION = 3;
  // 搜索引擎查询
  CHAT_MESSAGE_TYPE_SEARCH = 4;
  // 系统错误
  CHAT_MESSAGE_TYPE_SYSTEM_ERROR = 5;
  // 敏感信息错误
  CHAT_MESSAGE_TYPE_AUDIT_ERROR = 6;
  // 超时错误
  CHAT_MESSAGE_TYPE_TIMEOUT_ERROR = 7;
  // 取消回复
  CHAT_MESSAGE_TYPE_CANCEL_ERROR = 8;
  // 预定会话聊天回复
  CHAT_MESSAGE_TYPE_SET_CHART_ANSWER = 9;
  // 人工客服消息
  CHAT_MESSAGE_TYPE_LIVE_AGENT = 10;
  // 多模态消息
  CHAT_MESSAGE_TYPE_VISION = 11;
  // 清空上下文
  CHAT_MESSAGE_TYPE_CLEAR_HISTORY = 12;
  //  建议问题
  CHAT_MESSAGE_TYPE_SUGGESTION = 13;
  // 转人工二维码
  CHAT_MESSAGE_TYPE_LIVE_AGENT_CODE = 14;
  // 回答草稿
  CHAT_MESSAGE_TYPE_DRAFT = 15;
}

// 后续会改为使用助手表的channel，如果是新功能就不要使用这个枚举了
enum ChatType {
  CHAT_TYPE_UNSPECIFIED = 0;
  // web
  CHAT_TYPE_WEB = 1;
  // 微信公众号
  CHAT_TYPE_WECHAT = 2;
  // whatsapp
  CHAT_TYPE_WHATSAPP = 3;
  // 小程序
  CHAT_TYPE_MINIPROGRAM = 4;
}

enum ChatOrigin {
  CHAT_ORIGIN_UNSPECIFIED = 0;
  // tanlive
  CHAT_ORIGIN_TANLIVE = 1;
  // 无毒先锋
  CHAT_TYPE_WUDU = 2;
  // 生环
  CHAT_TYPE_SHENGHUAN = 3;
  // tanlive 生产
  CHAT_ORIGIN_TANLIVE_PRO = 4;
  // 腾讯基金会
  CHAT_ORIGIN_TFOUNDATION = 5;
  // 洲明基金会
  CHAT_ORIGIN_ZHFOUNDATION = 6;
}


// 文件信息
message ChatMessageFile {
  uint64 id = 1;                    // 文件ID
  string url = 3;                   // 文件URL
  ChatMessageFileState state = 4;
  string parsed_url = 5;             // 解析后的URL
}

enum ChatMessageFileState {
  CHAT_MESSAGE_FILE_STATE_UNSPECIFIED = 0;
  // 文件解析中
  CHAT_MESSAGE_FILE_STATE_PARSING = 1;
  // 文件解析成功
  CHAT_MESSAGE_FILE_STATE_PARSE_SUCCESS = 2;
  // 文件解析失败
  CHAT_MESSAGE_FILE_STATE_PARSE_FAILED = 3;
}

// 任务匹配模式配置
message PipelineTaskMatchConfig {
  // 匹配模式
  DocMatchPattern doc_match_pattern = 2;
}



// 知识库文档状态
enum DocState{
  DOC_STATE_UNSPECIFIED = 0;
  // 启用
  DOC_STATE_ENABLED = 1;
  // 禁用
  DOC_STATE_DISABLED = 2;
  // 解析中
  DOC_STATE_PARSING = 3;
  // 解析失败
  DOC_STATE_PARES_FAILED = 4;
  // 文件上传中
  DOC_STATE_FILE_UPLOADING = 5;
  // 文件上传成功
  DOC_STATE_FILE_UPLOAD_SUCCESS = 6;
  // 删除中
  DOC_STATE_DELETING = 7;
  // 解除了助手绑定（在助手中已删除）
  DOC_STATE_UNBOUNDED = 8;
  // 重新解析中
  DOC_STATE_REPARSING = 9;
}

// 文本文件是否能作为参考资料下载
enum DocFileDownloadAsRef{
  DOC_FILE_DOWNLOAD_AS_REF_UNSPECIFIED = 0;
  // 可下载
  DOC_FILE_DOWNLOAD_AS_REF_SHOW_URL = 1;
  // 仅显示文件名
  DOC_FILE_DOWNLOAD_AS_REF_SHOW_NAME = 2;
  // 直接发送
  DOC_FILE_DOWNLOAD_AS_REF_SEND = 3;
  // 隐藏
  DOC_FILE_DOWNLOAD_AS_REF_HIDDEN = 4;
}

// 知识库文档收到分享状态
enum DocSharedState{
  DOC_SHARED_STATE_UNSPECIFIED = 0;
  // 启用
  DOC_SHARED_STATE_ENABLED = 1;
  // 禁用
  DOC_SHARED_STATE_DISABLED = 2;
  // 收到分享
  DOC_SHARED_STATE_RECEIVED = 3;
}

// 知识库分享状态
enum DocShareState{
  DOC_SHARE_STATE_UNSPECIFIED = 0;
  // 启用
  DOC_SHARE_STATE_ENABLED = 1;
  // 禁用
  DOC_SHARE_STATE_DISABLED = 2;
  // 不接收
  DOC_SHARE_STATE_NOT_ACCEPTED = 3;
}

// 是否接收知识库分享
enum DocShareAcceptState{
  DOC_SHARE_ACCEPT_STATE_UNSPECIFIED = 0;
  // 接收分享
  DOC_SHARE_ACCEPT_STATE_ENABLED = 1;
  // 不接收分享
  DOC_SHARE_ACCEPT_STATE_DISABLED = 2;
}

// 文档内容状态
enum DocContentState {
  DOC_CONTENT_STATE_UNSPECIFIED = 0;
  // 同步中
  DOC_CONTENT_STATE_SYNCING = 1;
  // 有更新
  DOC_CONTENT_STATE_HAS_UPDATE = 2;
  // 已下架
  DOC_CONTENT_STATE_UNPUBLISHED = 3;
  // 已删除
  DOC_CONTENT_STATE_DELETED = 4;
  // 人工
  DOC_CONTENT_STATE_MANUAL = 5;
}

enum ChatMessageState {
  CHAT_MESSAGE_STATE_UNSPECIFIED = 0;
  // 消息未发送标识
  CHAT_MESSAGE_STATE_UNSENT = 1;
  // 消息已经发送标识
  CHAT_MESSAGE_STATE_SEND = 2;
  // 默认消息已经发送标识
  CHAT_MESSAGE_STATE_SEND_DEFAULT = 3;
  // 努力思考
  CHAT_MESSAGE_STATE_SEND_THINKING = 4;
  // 整理答案
  CHAT_MESSAGE_STATE_SEND_IN_PREPARATION = 5;
  // 停止回答
  CHAT_MESSAGE_STATE_SEND_STOP = 6;

  // 回答推流中
  CHAT_MESSAGE_STATE_STREAM_CHUNK = 7;
  // 推流全部完成
  CHAT_MESSAGE_STATE_STREAM_DONE = 8;
  // 思考过程推流
  CHAT_MESSAGE_STATE_STREAM_THINK = 9;
  // 切片推流完成
  CHAT_MESSAGE_STATE_STREAM_CHUNK_DONE = 10;
  // 参考资料消息
  CHAT_MESSAGE_STATE_STREAM_REF = 12;
  // 建议问题
  CHAT_MESSAGE_STATE_SUGGESTION = 13;
}

// 文档解析模式
enum DocParseMode {
  // 未定义
  DOC_PARSE_MODE_UNSPECIFIED = 0;
  // 智能解析
  DOC_PARSE_MODE_SMART = 1;
  // 文件解析
  DOC_PARSE_MODE_FILE = 2;
  // 图像解析
  DOC_PARSE_MODE_IMAGE = 3;
  // 表格解析
  DOC_PARSE_MODE_TABLE = 4;
}

// 会话操作类型
enum ChatOperationType {
  CHAT_OPERATION_TYPE_UNSPECIFIED = 0;
  // 正常回答
  CHAT_OPERATION_TYPE_NORMAL = 1;
  // 停止回答
  CHAT_OPERATION_TYPE_STOP_TEXT = 2;
  // 停止思考
  CHAT_OPERATION_TYPE_STOP_THINK = 3;
  // 重新回答
  CHAT_OPERATION_TYPE_RESEND = 4;
}

enum DocBatchTaskType {
  // 未定义
  DOC_BATCH_TASK_TYPE_UNSPECIFIED = 0;
  // 复制
  DOC_BATCH_TASK_TYPE_CLONE = 1;
  // 删除
  DOC_BATCH_TASK_TYPE_DELETE = 2;
  // 启/停用
  DOC_BATCH_TASK_TYPE_ON_OFF = 3;
  // 更新
  DOC_BATCH_TASK_TYPE_UPDATE = 4;
  // 重新解析
  DOC_BATCH_TASK_TYPE_REPARSE = 5;
  // 分享
  DOC_BATCH_TASK_TYPE_SHARE = 6;
}

message MessageConfig {
  bool chat_or_sql = 1;
  int32 history_rounds = 2;
  string prompt_prefix = 3;
  string collection_name = 4;
  float threshold = 5;
  string search_engine = 6;
  int32 search_top_n = 7;
  int32 doc_top_n = 8;
  string model = 9;
  string sql_model = 10;
  string final_query = 11;
  string default_text = 12;
  string final_search_query = 13;
  float text_weight = 14;
  string chat_model = 15;
  string complex_model = 16;
  int32 text_recall_top_n = 17;
  string judge_prompt = 18;
  string simple_model = 19;
  bool clean_chunks = 20;
  float temperature = 21;
  // 关键词召回匹配目标
  TextRecallQuery text_recall_query = 22;
  // 关键词召回模式
  TextRecallPattern text_recall_pattern = 23;
  // 关键词召回允许平移距离允许平移距离
  int32 text_recall_slop = 24;
  bool is_stream = 25;
}

message ChatMessageContentFilterItem {
  string field = 1;
  string value = 2;
  repeated MessageTag tags = 3;
}

message ChatMessageContent {
  string text = 1;
  tanlive.base.DataType ugc_type = 2;
  repeated uint64 ugc_ids = 3;
  string link = 4;
  string sql_query = 5;
  repeated  ChatMessageContentFilterItem filter = 14;
  repeated MessageUgc ugcs = 15;
  repeated string image_url = 16;
  string final_query = 17;
}

message MessageUgcCard {
  string name = 1;
  string logo_url = 2;
  uint64 id = 3;
  string tags = 4;
}

message MessageTag {
  int32 taggable_type = 1;
  uint64 id = 2;
  string name = 3;
  uint64 taggable_id = 4;
  int32 type = 5;
  int32 data_type = 6;
}

message MessageUgc{
  tanlive.base.DataType ugc_type = 2;
  repeated  ChatMessageContentFilterItem filter = 3;
  repeated MessageUgcCard cards = 4;
  bool is_ugc_link = 5;
  repeated uint64 ugc_ids = 6;
}

message ChatPublishEvent {
  int32 event = 1;
  string  content = 2;
  uint64 user_id = 3;
  string hash_id = 4;
  int32 answer_index = 5;
  bool is_hash_event = 6;
}

message ChatPushMessage {
  string hash_id = 1;
  string  content = 2;
  ChatMessageState state = 3;
  int32 answer_index = 4;
  bool is_hash_event = 5;
  ChatMessage hash_msg = 6;
  ChatMessageType type = 7;
  repeated string suggests = 8;
}

message ChatMessageTask {
  uint64 task_id = 1;
  uint64 pipeline_id = 2;
  PipelineTaskState state = 3;
  int32 order = 4;
}

message ChatMessageOperator {
  ChatOperationType operation_type = 1;
  string stop_text = 2;
  string stop_think = 3;
  int32 stop_chunk_state = 4;
  string operation_params = 5;
  string hash_id = 6;
}

message ChatMessage {
  uint64 id = 1;
  uint64 chat_id = 2;
  string text = 4;
  google.protobuf.Timestamp create_date = 5;
  ChatMessageType type = 6;
  RatingScale rating_scale = 7;
  string link = 9;
  uint64 question_id = 12;
  string sql_query = 13;
  repeated MessageUgc ugcs = 15;
  uint64 create_by = 16;
  string reject_reason = 17;
  ChatMessageState state = 18;
  repeated string doc_names = 19;
  uint64 assistant_id = 22;
  string lang = 23;
  MessageCollectionSnapshot collection_snapshot = 24;
  QuestionAskType ask_type = 25;
  google.protobuf.Timestamp start_time = 26;
  google.protobuf.Timestamp end_time = 27;
  repeated string suggest_question = 29;
  string final_query = 30;
  string live_agent_name = 31;
  string prompt_prefix = 34;
  repeated string image_url = 35;
  int32 show_type = 36;
  string final_search_query = 37;
  DocMatchPattern doc_match_pattern = 38;
  AskSuggestionMode suggestion_mode = 39;
  repeated ChatMessageLog logs = 40;
  repeated string ref_file_names = 41;
  // 深度思考
  string think = 42;
  // 推送消息hash id
  string publish_hash_id = 43;
  uint64 history_ignore_id = 44;
  bool wait_answer = 45;
  int32 think_duration = 46;
  // 多任务索引
  int32 answer_index = 47;
  // 问题类型
  string prompt_type = 48;
  // 教学反馈ID
  uint64 feedback_id = 49;
  // 是否有用户反馈
  bool has_user_feedback = 50;
  // 最后一次操作
  ChatOperationType last_operation_type = 51;
  // answer草稿id
  uint64 answer_draft_id = 52;
  // 当前所执行的任务
  ChatMessageTask task = 53;
  // 文件解析成功
  bool is_file_ready = 54;
  // 问题附件
  repeated ChatMessageFile files = 55;
  // 最后一次操作
  ChatMessageOperator last_operator = 56;
  // doc 快照
  MessageDocSnapshot doc_snapshot = 57;
}

message QuestionsWithAnswer{
  repeated ChatMessage questions = 1;
  map<uint64, ChatMessage> answers = 2;
  string final_search_query = 31;
  string prompt_prefix = 32;
  repeated string image_url = 33;
}

message ChatSendRecordInfo {
  // 记录id
  uint64 id = 1;
  // 消息id
  uint64 message_id = 2;
  // 消息类型
  ChatMessageType message_type = 3;
  // 记录内容
  string content = 4;
  // 发送时间
  google.protobuf.Timestamp send_date = 5;
  // 类型 1 用户询问 2 助手回答-text 3 助手回答-menu 4 助手回答建议问题菜单消息
  AiRecordType record_type = 6;
  // 记录对应的消息的评价信息
  RatingScale message_rating_scale = 7;
  // 显示状态
  int32 show_type = 8;
  string reject_reason = 9;
  repeated string suggest_questions = 10;
  repeated string image_url = 11;
  DocMatchPattern doc_match_pattern = 12;
  AskSuggestionMode suggestion_mode = 32;
}

message EventChatMessage {
  uint64 id = 1;
  uint64 chat_id = 2;
  string text = 4;
  google.protobuf.Timestamp create_date = 5;
  ChatMessageType type = 6;
  RatingScale rating_scale = 7;
  repeated ChatMessageDoc docs = 8;
  string link = 9;
  uint64 question_id = 12;
  string sql_query = 13;
  repeated MessageUgc ugcs = 15;
  uint64 create_by = 16;
  string reject_reason = 17;
  ChatMessageState state = 18;
  base.TimeRange process_time = 20;
  string lang = 21;
  uint64 assistant_id = 22;
  string doc_final_query = 23;
  QuestionAskType ask_type = 24;
  repeated string suggest_questions = 25;
  int32 suggest_count = 26;
  google.protobuf.Timestamp start_time = 27;
  google.protobuf.Timestamp end_time = 28;
  repeated string image_url = 29;
  int32 show_type = 30;
  DocMatchPattern doc_match_pattern = 31;
  AskSuggestionMode suggestion_mode = 32;
  string think = 33;
  bool wait_answer = 34;
  int32 think_duration = 35;
  // 多任务索引
  int32 answer_index = 36;
  // 问题类型
  string prompt_type = 37;
  // 关联的回答
  repeated EventChatMessage answers = 38;
  // 最后一次操作
  ChatOperationType last_operation_type = 39;
  // 教学反馈ID
  uint64 feedback_id = 40;
  ai.DocDataSource data_source = 41;
  // 是否有用户反馈
  bool has_user_feedback = 42;
  // 文件解析成功
  bool is_file_ready = 43;
  repeated ChatMessageFile files = 44;
  // 是否是agent回答
  bool is_agent_command = 45;
}

message EventChatHashMessage {
  message EventMessageUgc{
    repeated string ugc_ids = 1;
    tanlive.base.DataType ugc_type = 2;
    repeated  EventMessageFilter filter = 3;
    repeated EventMessageUgcCard cards = 4;
    bool is_ugc_link = 5;
  }
  message EventMessageUgcCard {
    string name = 1;
    string logo_url = 2;
    string id = 3;
    string tags = 4;
  }
  message EventMessageFilter {
    string field = 1;
    string value = 2;
    repeated EventMessageTag tags = 3;
  }
  message EventMessageTag {
    int32 taggable_type = 1;
    string id = 2;
    string name = 3;
    int32 type = 4;
  }
  message EventMessageDoc {
    uint64 ugc_id = 1;
    base.DataType ugc_type = 2;
    uint64 id = 3;
    string rag_filename = 4;
    repeated EventContributor contributor = 5;
    uint32 data_type = 6;
    repeated DocReference reference = 7;
    string file_name = 8;
  }
  message EventContributor{
    base.IdentityType type = 1;
    string text = 2;
    string id = 3;
    // 共创等级
    team.TeamLevel level = 4;
    // 是否已发布
    bool is_published = 5;
    // team full name
    string full_name = 6;
  }
  string id = 1;
  string chat_id = 2;
  string text = 4;
  google.protobuf.Timestamp create_date = 5;
  ChatMessageType type = 6;
  RatingScale rating_scale = 7;
  repeated EventMessageDoc docs = 8;
  string link = 9;
  string question_id = 10;
  repeated EventMessageUgc ugcs = 11;
  int32 state = 12;
  string lang = 13;
  string assistant_id = 14;
  string reject_reason = 15;
  base.TimeRange process_time = 16;
  uint64 custom_question_id = 17;
  string sql_query = 18;
  string doc_final_query = 19;
  int32 suggest_count = 20;
  repeated string suggest_questions = 21;
  int32 show_type = 22;
  DocMatchPattern doc_match_pattern = 23;
  AskSuggestionMode suggestion_mode = 24;
  string think = 25;
  bool wait_answer = 26;
  int32 think_duration = 27;
  // 多任务索引
  int32 answer_index = 28;
  // 问题类型
  string prompt_type = 29;
  // 最后一次操作
  ChatOperationType last_operation_type = 30;
}

message ChatMessageDoc {
  uint64 ugc_id = 1;
  base.DataType ugc_type = 2;
  uint64 id = 3;
  string rag_filename = 4;
  repeated Contributor contributor = 5;
  uint32 data_type = 6;
  repeated DocReference reference = 7;
  string file_name = 8;
  string index_text = 9;
  string text = 10;
  DocMatchPattern doc_match_pattern = 11;
  string url = 12;
  Operator update_by = 13;
  DocDataSource data_source = 14;
}

message ChatMessageLog {
  uint64 message_id = 1;
  string sql_query = 2;
  string enhancement = 3;
  string gpt = 5;
  string ref = 6;
  uint64 code = 7;
  google.protobuf.Timestamp create_date = 8;
  google.protobuf.Timestamp start_time = 9;
  google.protobuf.Timestamp end_time = 10;
  string config_snapshot = 11;
  ChatMessageType type = 12;
  string request_text = 13;
  google.protobuf.Timestamp fetch_resp_time = 14;
}

enum PipelineTaskState{
  CHAT_AGENT_TASK_STATE_UNSPECIFIED = 0;
  // 进行中
  CHAT_AGENT_TASK_STATE_RUNNING = 1;
  // 已完成
  CHAT_AGENT_TASK_STATE_FINISHED = 2;
  // 失败
  CHAT_AGENT_TASK_STATE_FAILED = 3;
}

message ChatAgentTask {
  uint64 id = 1;
  uint64 assistant_id = 2;
  string command = 3;
  string prompt_prefix = 4;
  uint64 pre_task_id = 5;
  PipelineTaskFetchType fetch_type = 6;
  string default_text = 7;
  string prompt = 8;
  uint64 pipeline_id = 9;
  DocMatchPattern match_pattern = 10;
  AssistantAskSuggestionConfig suggestion_config = 11;
  string vision_model = 12;
  int32 start_answer_index = 13;
}

message ChatSuggestLog {
  uint64 message_id = 1;
  string collections = 2;
  string gpt = 3;
  string config_snapshot = 4;
  google.protobuf.Timestamp create_date = 5;
  string request_type = 6;
}

message Chat {
  uint64 id = 1;
  string title = 2;
  uint64 create_by = 3;
  google.protobuf.Timestamp create_date = 4;
  google.protobuf.Timestamp update_date = 5;
  ChatType chat_type = 6;
  // 发起会话的微信用户头像
  //  string user_image = 7;
  // 发起会话的微信用户昵称
  string nickname = 8;
  ChatOrigin origin = 9;
  uint64 assistant_id = 10;
  ChatCurrentState chat_state = 11;
  ChatSupportType support_type = 12;
  // 对话中问题数量
  uint32 question_cnt = 13;
  // 自定义标签
  repeated CustomLabel labels = 14;
  // 平均用时
  float avg_duration = 15;
  // 文档命中率
  float doc_hits = 16;
  // 平均评分
  float rating_scale = 17;
  uint32 reject_job_result = 18;
  string region_code = 19;
  // 是否转过人工服务
  int32 is_manual = 20;
  // 是否是分享会话
  bool is_shared = 21;
}

message ChatWeChat {
  uint64 id = 1;
  string app_id = 2;
  string external_user_id = 3;
  string nickname = 4;
  ChatCurrentState chat_state = 5;
  ChatSupportType support_type = 6;
  string live_agent_name = 7;
  string title = 8;
  uint64 assistant_id = 9;
  tanlive.ai.ChatType chat_type = 10;
  // 服务商对应的公司凭证id，仅在发送微信消息时使用，不涉及数据库存储
  string corp_id = 11;
  // 默认答案记录
  uint32 default_answer_record = 12;
  // 会话已结束
  bool end = 13;
}

message ChatDetail {
  uint64 id = 1;
  string title = 2;
  uint64 create_by = 3;
  // 发起会话的微信用户昵称
  string nickname = 4;
  ChatType chat_type = 5;
  // 小助手id
  uint64 assistant_id = 6;
  // 人工坐席名称
  string live_agent_name = 7;
  // 微信客服内的唯一id
  string external_user_id = 8;
  ChatCurrentState chat_state = 9;
  // 当前服务状态
  ChatSupportType support_type = 10;
  // 开始时间
  google.protobuf.Timestamp create_date = 11;
  // 更新时间
  google.protobuf.Timestamp update_date = 12;
}

message Collection{
  uint64 id = 1;
  string name = 2;
}

message Operator{
  base.IdentityType type = 1;
  // 用户账户: 用户id
  // 团队账户: 用户id
  // 运营端账户: 运营端用户id
  uint64 id = 2;
  // type为团队用户时，可选的传入个人的 id
  uint64 user_id = 3;
}

message Contributor{
  base.IdentityType type = 1;
  // 自定义纯文本 或者 个人/团队/运营端账户名称
  string text = 2;
  // 账户id
  // 个人账户: 个人账户id
  // 团队账户: 团队账户id
  // 运营端: 运营端账户id
  uint64 id = 3;
  // 共创等级
  team.TeamLevel level = 4;
  // 是否已发布
  bool is_published = 5;
  // team full name
  string full_name = 6;

  // 贡献者所在的助手
  repeated uint64 assistant_ids = 7;
}

message TextFile{
  uint64 id = 1;
  string name = 2;
  string text = 3;
  string url = 4;
  repeated Assistant assistants = 5;
  DocState state = 6;
  repeated Contributor contributor = 7;
  Operator update_by = 8;
  google.protobuf.Timestamp update_date = 9;
  google.protobuf.Timestamp create_date = 10;
  Operator create_by = 11;
  uint32 hit_count = 12;
  base.DataType ugc_type = 13;
  uint64 ugc_id = 14;
  uint32 type = 15;
  string parsedUrl = 16;
  uint64 version_lag = 17;
  float parse_progress = 18;
  uint32 show_contributor = 19;
  repeated DocAssistantState states = 20;
  // 是否为副本
  bool is_copy = 21;
  // 主数据ID
  uint64 main_id = 22;
  // UGC标题
  string ugc_title = 23;
  // 是否为系统数据
  // 已废弃，使用 data_source
  //  bool is_system = 24;
  ai.DocDataSource data_source = 24;
  // 内容状态
  DocContentState content_state = 25;
  repeated CustomLabel labels = 26;
  map<uint64, uint32> embedding_version_lag = 27;
  repeated DocReference reference = 28;
  ai.DocFileDownloadAsRef download_as_ref = 29;
  tanlive.ai.DocParseMode parse_mode = 30;

  // 知识提示
  // 是否有超长标题表格
  bool has_over_sized_tables = 31;
  // 是否内容重复（租户内）
  bool has_repeated = 32;

  // 外部数据源信息
  uint32 data_source_state = 33;

  // 分享目标
  repeated DocShareReceiver share_receivers = 34;
}

// 完整的TextFile
message FullTextFile {
  // 文档详情
  TextFile doc = 1;
  // 副本
  repeated TextFile copies = 2;
}

message TencentDoc{
  string title = 1;
  string url = 2;
  string file_name = 3;
  string file_id = 4;
  string file_type = 5;
  string file_create_user = 6;
  string file_owner_name = 7;
  string file_create_time = 8;
  string file_modify_time = 9;
  string file_browse_time = 10;
  string file_url = 12;
}

message DocAssistantState{
  uint64 assistant_id = 1;
  DocState state = 2;
  // 1否 2是
  uint32 is_shared = 3;
}

message QA{
  uint64 id = 1;
  string question = 2;
  string answer = 3;
  repeated DocReference reference = 4;
  repeated Assistant assistants = 5;
  repeated DocAssistantState states = 6;
  repeated Contributor contributor = 7;
  Operator update_by = 8;
  google.protobuf.Timestamp update_date = 9;
  google.protobuf.Timestamp create_date = 10;
  Operator create_by = 11;
  uint32 hit_count = 12;
  uint64 version_lag = 13;
  uint32 show_contributor = 14;
  DocState state = 15;
  repeated CustomLabel labels = 16;
  map<uint64, uint32> embedding_version_lag = 17;
  repeated DocMatchPattern match_patterns = 18;
  bool question_oversize = 19;
  bool has_repeated = 20;
  // 分享目标
  repeated DocShareReceiver share_receivers = 30;
}

// 文档类型
enum DocType{
  DOCTYPE_UNSPECIFIED = 0;
  // QA
  DOCTYPE_QA = 1;
  // 文本
  DOCTYPE_TEXT = 2;
  // 文件
  DOCTYPE_FILE = 3;
}

// 参考文献
message DocReference{
  // 引用doc的参考文献
  uint64 id = 1;
  string name = 2;
  string url = 3;
  // 纯文本参考文献
  string text = 4;
  // 仅用于控制对话展示
  DocFileDownloadAsRef show_type = 5;
}

// 反馈状态
enum FeedbackState {
  FEEDBACK_STATE_UNSPECIFIED = 0;
  // 未读
  FEEDBACK_STATE_UNREAD = 1;
  // 已读
  FEEDBACK_STATE_READ = 2;
  // 已采用
  FEEDBACK_STATE_ACCEPTED = 3;
}

// 回答评价
enum FeedbackAnswerRating {
  FEEDBACK_ANSWER_RATING_UNSPECIFIED = 0;
  // 好
  FEEDBACK_ANSWER_RATING_GOOD = 1;
  // 坏
  FEEDBACK_ANSWER_RATING_BAD = 2;
}

// 反馈备注
message FeedbackComment {
  // 文件
  message File {
    // 路径
    string file_path = 1;
    // 文件名称
    string file_name = 2;
  }
  // 内容
  string content = 1;
  // 文件列表
  repeated File files = 2;
}

// 完整反馈信息
message FullFeedback {
  // 反馈详情
  Feedback feedback = 1;
  // 参考文献
  repeated FeedbackReference references = 2;
  // 原始问题
  ChatMessage original_question = 3;
  // 原始回答
  ChatMessage original_answer = 4;
  // 预期命中的知识
  repeated ChatMessageDoc expected_docs = 5;
  // 预期命中的知识ID
  repeated uint64 expected_doc_id = 6;
  // 预期命中的知识（碳LIVE运营）
  repeated ChatMessageDoc expected_mgmt_docs = 7;
  // 预期命中的知识ID（碳LIVE运营）
  repeated uint64 expected_mgmt_doc_id = 8;
}

// 用户反馈
message Feedback {
  // 反馈ID
  uint64 id = 1;
  // 对话ID
  uint64 chat_id = 2;
  // 消息ID
  uint64 message_id = 3;
  // 问题
  string question = 4;
  // 答案
  string answer = 5;
  // 状态
  FeedbackState state = 6;
  // 创建时间
  google.protobuf.Timestamp create_date = 12;
  // 更新时间
  google.protobuf.Timestamp update_date = 14;
  // 助手id
  uint64 assistant_id = 15;
  // 助手名称
  string assistant_name = 16;
  // 原始问题ID
  uint64 question_id = 21;
  // 回答评价
  FeedbackAnswerRating answer_rating = 22;
  // 是否命中预期知识
  bool hit_expected_doc = 23;
  // 分析备注
  FeedbackComment op_comment = 24;
  // 碳LIVE反馈
  FeedbackComment mgmt_feedback = 25;
  // 碳LIVE备注
  FeedbackComment mgmt_comment = 26;
  // 是否有用户反馈
  bool has_user_feedback = 27;
  // 是否有运营反馈
  bool has_op_feedback = 28;
  // 是否有碳LIVE反馈
  bool has_mgmt_feedback = 29;
  // 用户反馈人
  base.Identity user_feedback_by = 30;
  // 运营反馈人
  base.Identity op_feedback_by = 31;
  // 碳LIVE反馈人
  base.Identity mgmt_feedback_by = 32;
  // 用户反馈时间
  google.protobuf.Timestamp user_feedback_at = 33;
  // 运营反馈时间
  google.protobuf.Timestamp op_feedback_at = 34;
  // 碳LIVE反馈时间
  google.protobuf.Timestamp mgmt_feedback_at = 35;
  // 创建人身份
  base.Identity create_identity = 36;
  // 更新人身份
  base.Identity update_identity = 37;
  // 原始答案ID
  uint64 answer_id = 38;
}

// 反馈文档类型
enum FeedbackDocType {
  FEEDBACK_DOC_TYPE_UNSPECIFIED = 0;
  // 门户运营
  FEEDBACK_DOC_TYPE_OP = 1;
  // 碳LIVE运营
  FEEDBACK_DOC_TYPE_MGMT = 2;
}

// 参考文献类型
enum ReferenceType {
  REFERENCE_TYPE_UNSPECIFIED = 0;
  // URL
  REFERENCE_TYPE_URL = 1;
  // 文本
  REFERENCE_TYPE_TEXT = 2;
  // 文件
  REFERENCE_TYPE_FILE = 3;
}

// 反馈参考文献
message FeedbackReference {
  // 参考文献ID
  uint64 id = 1;
  // 反馈ID
  uint64 feedback_id = 2;
  // 类型
  ReferenceType type = 3;
  // URL链接
  string url = 4;
  // 文本内容
  string text = 5;
  // 文件路径
  string file_path = 6;
  // 文件名称
  string file_name = 7;
  // 创建人
  uint64 create_by = 8;
  // 创建时间
  google.protobuf.Timestamp create_date = 9;
  // 更新人
  uint64 update_by = 10;
  // 更新时间
  google.protobuf.Timestamp update_date = 11;
}

// 类型化的参考文献
message TypedReference {
  oneof reference {
    // URL类型的参考文献
    UrlReference url = 1;
    // 文本类型的参考文献
    TextReference text = 2;
    // 文件类型的参考文献
    FileReference file = 3;
  }
}

// URL类型的参考文献
message UrlReference {
  // URL链接
  string url = 1 [(validator) = "required,max=1024,url"];
}

// 文本类型的参考文献
message TextReference {
  // 文本内容
  string text = 1 [(validator) = "required,max=1024"];
}

// 文件类型的参考文献
message FileReference {
  // 文件路径
  string file_path = 1 [(validator) = "required,max=1024"];
  // 文件名
  string file_name = 2 [(validator) = "required,max=4096"];
}

// 评价等级
enum RatingScale {
  RATING_SCALE_UNSPECIFIED = 0;
  // 满意
  RATING_SCALE_SATISFIED = 1;
  // 一般
  RATING_SCALE_AVERAGE = 2;
  // 不满意
  RATING_SCALE_DISSATISFIED = 3;
}

// 反馈操作
enum FeedbackAction {
  FEEDBACK_ACTION_UNSPECIFIED = 0;
  // 已读（已废弃）
  FEEDBACK_ACTION_READ = 1;
  // 采用
  FEEDBACK_ACTION_ACCEPT = 2;
  // 创建用户反馈
  FEEDBACK_ACTION_CREATE_USER_FEEDBACK = 3;
  // 创建运营反馈
  FEEDBACK_ACTION_CREATE_OP_FEEDBACK = 4;
  // 创建碳LIVE反馈
  FEEDBACK_ACTION_CREATE_MGMT_FEEDBACK = 5;
  // 更新用户反馈
  FEEDBACK_ACTION_UPDATE_USER_FEEDBACK = 6;
  // 更新运营反馈
  FEEDBACK_ACTION_UPDATE_OP_FEEDBACK = 7;
  // 更新碳LIVE反馈
  FEEDBACK_ACTION_UPDATE_MGMT_FEEDBACK = 8;
}

// 反馈操作日志
message FeedbackLog {
  // 主键
  uint64 id = 1;
  // 反馈ID
  uint64 feedback_id = 2;
  // 操作
  FeedbackAction action = 3;
  // 操作人
  base.Identity create_identity = 4;
  // 操作时间
  google.protobuf.Timestamp create_date = 5;
}

// 完整反馈
message FullFeedbackLog {
  // 日志
  FeedbackLog log = 1;
  // 反馈
  Feedback feedback = 2;
  // 原始问题
  ChatMessage original_question = 3;
}

// 标签取值
message LabelValue{
  // 标签值
  oneof any_value {
    // 整型值
    int64 int_value = 7;
    // 无符号整形
    uint64 uint_value = 8;
    // 浮点型
    double float_value = 10;
    // 任意纯文本
    string text_value = 9 [(validator) = "max=512"];
    // 字符串枚举(单选)
    string enum_value = 11;
    // 字符串枚举(多选)
    string enum_m_value = 12;
    // 年
    int64 y_value = 13;
    // 年月
    int64 ym_value = 14;
    // 年月日
    int64 ymd_value = 15;
    // 日期时间(年月日和时间)
    int64 datetime_value = 16;
    // 时间
    int64 time_value = 17;
  }
}

message LabelFilter{
  // 标签id
  uint64 id = 1;
  LabelValue eq = 2;
  LabelValue gte = 3;
  LabelValue lte = 4;
  repeated LabelValue in = 5;
  LabelValue like = 6;
  LabelFilterOp op = 7;
}

enum LabelFilterOp{
  LABEL_FILTER_OP_UNSPECIFIED = 0;
  // 等于
  LABEL_FILTER_OP_EQ = 1;
  // IN
  LABEL_FILTER_OP_IN = 2;
  // 大于等于
  LABEL_FILTER_OP_GTE = 3;
  // 小于等于
  LABEL_FILTER_OP_LTE = 4;
  // LIKE模糊搜索
  LABEL_FILTER_OP_LIKE = 5;
}

message OrderByLabel{
  uint64 id = 1;
  bool desc = 2;
}

// AI对话的标签
message CustomLabel{
  // 标签id
  uint64 id = 1;
  // 标签类型
  CustomLabelType type = 2;
  // 标签key
  string key = 3;
  LabelValue value = 4 [(validator) = "required,omitempty"];
  // 创建人
  uint64 create_by = 5;
  // 更新人
  uint64 update_by = 6;
  oneof next_label{
    string next_label_name = 18;
    uint64 next_label_id = 19;
  }
}

// 标签类型
enum CustomLabelType{
  CUSTOM_LABEL_TYPE_UNSPECIFIED = 0;
  // 任意纯文本
  CUSTOM_LABEL_TYPE_TEXT = 1;
  // 字符串枚举(单选)
  CUSTOM_LABEL_TYPE_ENUM = 2;
  // 字符串枚举(多选)
  CUSTOM_LABEL_TYPE_ENUM_M = 3;
  // int
  CUSTOM_LABEL_TYPE_INT = 4;
  // uint
  CUSTOM_LABEL_TYPE_UINT = 5;
  // float
  CUSTOM_LABEL_TYPE_FLOAT = 6;
  // 年
  CUSTOM_LABEL_TYPE_DATE_Y = 7;
  // 年月
  CUSTOM_LABEL_TYPE_DATE_YM = 8;
  // 年月日
  CUSTOM_LABEL_TYPE_DATE_YMD = 9;
  // 日期时间(年月日和时间)
  CUSTOM_LABEL_TYPE_DATETIME = 10;
  // 时间
  CUSTOM_LABEL_TYPE_TIME = 11;
}

// 标签对象类型
enum CustomLabelObjectType{
  CUSTOM_LABEL_OBJECT_TYPE_UNSPECIFIED = 0;
  // AI 对话
  CUSTOM_LABEL_OBJECT_TYPE_CHAT = 1;
  // 知识库文档文本与文件
  CUSTOM_LABEL_OBJECT_TYPE_FILE = 2;
  // 知识库文档QA
  CUSTOM_LABEL_OBJECT_TYPE_QA = 3;
  // 腾讯云文档导入的文本文件
  CUSTOM_LABEL_OBJECT_TYPE_TCLOUD_FILE = 4;
  // SQL数据导入的文本文件
  CUSTOM_LABEL_OBJECT_TYPE_SQL_FILE = 5;
}

message Assistant{
  uint64 id = 1;
  string name = 2;
  string name_en = 3;
  repeated Collection collections = 4;
  string website_route = 5;
  bool search_debug = 6;
  float threshold = 7;
  int32 top_n = 8;
  float text_weight = 9;
  // 关键词召回条数
  int32 text_recall_top_n = 10;
  // 温度
  float temperature = 11;
  bool clean_chunks = 12;
  // 关键词召回匹配目标
  TextRecallQuery text_recall_query = 13;
  // 关键词召回模式
  TextRecallPattern text_recall_pattern = 14;
  // 关键词召回允许平移距离允许平移距离
  int32 text_recall_slop = 15;
}

message AssistantDetail {
  uint64 id = 1;
  string name = 2;
  string name_en = 3;
  AssistantWelcomeMsg welcome_msg = 4;
  string wechat_auto_reply = 5;
  string prompt_prefix = 6;
  int32 chat_or_sql = 7;
  string app_code = 8;
  int32 show_type = 9;
  uint64 create_by = 10;
  uint64 last_update_by = 11;
  google.protobuf.Timestamp create_date = 12;
  google.protobuf.Timestamp last_update_date = 13;
  AssistantDefaultConfigMsg no_permission_msg = 14;
  string website_route = 15;
  string model = 16;
  int32 history_rounds = 17;
  string sql_model = 18;
  int32 search_top_n = 19;
  int32 doc_top_n = 20;
  string search_engine = 21;
  string collection_name = 22;
  float threshold = 23;
  string corp_id = 26;
  AssistantConfigMsg default_push_msg = 27;
  AssistantRatingScaleMsg rating_scale_msg = 28;
  string lang = 29;
  int32 search_rewrite = 30;
  string vision_model = 31;
  AssistantLiverAgentConfigMsg live_agent_msg = 32;
  AssistantMultimodalPrompt multimodal_prompt = 33;
  bool close_search = 34;
  bool enabled = 35;
  int64 chat_idle_duration = 36;
  repeated string chain_visible = 37;
  string es_ins = 39;
  float text_weight = 40;
  string miss_reply = 41;
  bool close_ref_mark = 43;
  // 关键词召回条数
  int32 text_recall_top_n = 45;
  // 温度
  float temperature = 46;
  AssistantQuestionTypeConfig question_type_config = 47;
  bool show_think = 48;
  bool clean_chunks = 49;
  // 关键词召回匹配目标
  TextRecallQuery text_recall_query = 50;
  // 关键词召回模式
  TextRecallPattern text_recall_pattern = 51;
  // 关键词召回允许平移距离允许平移距离
  int32 text_recall_slop = 52;
  AssistantChannel channel = 53;
  string nick_name = 54;
  AssistantAskSuggestionConfig suggestion_config = 55;
}

message AssistantAdmin{
  uint64 id = 1;
  base.IdentityType type = 2;
}

message AssistantMultimodalPrompt {
  string read_test_report = 1;// 读检测报告
  string read_bom = 2; //读配料表 Bill of Materials (BOM)
  string compliance_mark = 3; // 识别认证标识
  string compliance_bom = 4;// 识别配料表 // 暂时没有使用
  string bom_hazards = 5;// 配料表的危害
  string ask_mark = 6; // 询问认证标识
}

message AssistantWelcomeMsg {
  string head_msg = 1;
  repeated string question = 2;
  AssistantInteractiveCodeConfig code_config = 3;
  string tail_msg = 4;
}

message AssistantRatingScaleMsg {
  bool enable = 1;
  // 满意的回复
  string satisfied = 2;
  // 一般的回复
  string average = 3;
  // 不满意的回复
  string dissatisfied = 4;
}

message AssistantDefaultConfigMsg {
  bool enable = 1;
  string msg = 2;
}

message AssistantConfigMsg {
  string read_test_report = 1; // 读检测报告回复配置
  string read_bom = 2; // 读配料表回复配置 Bill of Materials (BOM)
}

message AssistantLiverAgentConfigMsg {
  bool enable = 1; // 是否开启人工客服
  string msg = 2; // 转人工的默认提示
  AssistantDefaultConfigMsg remind_push_msg = 3; // 提醒用户是否转人工相关配置
  // 客服自动回复配置
  AssistantKefuReply reply = 4;
}

message ChatLiveAgent {
  uint64 id = 1;
  uint64 assistant_id = 2;
  string username = 3;
  string nickname = 4;
  ChatLiveAgentStatus status = 5;
  google.protobuf.Timestamp create_date = 10;
  int32 order = 11;
}

message ChatLiveAgentInfo {
  // 人工客服列表
  repeated LiveAgentInfo live_agents = 1;
  // 当前正在会话中的客服
  LiveAgentInfo current_live_agent = 2;
  // 会话ID
  uint64 chat_id = 3;
  message LiveAgentInfo {
    uint64 id = 1;
    string nickname = 4;
  }
}

enum ChatLiveAgentStatus {
  CHAT_LIVE_AGENT_STATUS_UNSPECIFIED = 0;
  // "正在接待"
  CHAT_LIVE_AGENT_STATUS_CURRENTLY_SERVING = 1;
  // "停止接待"
  CHAT_LIVE_AGENT_STATUS_STOPPED_SERVING = 2;
  // "暂时挂起"
  CHAT_LIVE_AGENT_STATUS_STOPPED_TEMPORARILY_SUSPENDED = 3;
}

enum SwitchChatState {
  SWITCH_CHAT_STATE_UNSPECIFIED = 0;
  // 切换成功
  SWITCH_CHAT_STATE_SUCCESS = 1;
  // 会话已结束
  SWITCH_CHAT_STATE_CHAT_FINISHED = 2;
  // 人工坐席离线
  SWITCH_CHAT_STATE_LIVE_AGENT_OFFLINE = 3;
  // 会话信息错误（需要重新开启一个会话或者再问一个问题）
  SWITCH_CHAT_STATE_CHAT_ERROR = 4;
  // 人工坐席不存在
  SWITCH_CHAT_STATE_LIVE_AGENT_NOT_EXIST = 5;
}

message SearchCollectionItem{
  string text = 1;
  string question = 2;
  float score = 3;
  string file_name = 4;
  string url = 5;
  repeated Contributor contributor = 6;
  Operator update_by = 10;
  string id = 11;
  // 召回类型
  tanlive.ai.SearchCollectionType type = 12;
  // 是否相关
  bool is_related = 13;
  // 文件类型
  DocType doc_type = 14;
  DocDataSource data_source = 15;
  uint64 doc_id = 16;
  string ref_name = 17;
  string ref_url = 18;
  string doc_name = 19;
}

enum ChatSupportType {
  CHAT_SUPPORT_TYPE_UNSPECIFIED = 0;
  // ai聊天
  CHAT_SUPPORT_TYPE_AI = 1;
  // 客服聊天
  CHAT_SUPPORT_TYPE_LIVE_AGENT = 2;
}

enum ListDocFilterType{
  LIST_DOC_FILTER_TYPE_UNSPECIFIED = 0;
  // qa
  LIST_DOC_FILTER_TYPE_QA = 1;
  // 文本/文件
  LIST_DOC_FILTER_TYPE_TEXTFILE = 2;
  // 系统数据
  LIST_DOC_FILTER_TYPE_SYSTEM = 3;
}

enum ChatCurrentState{
  CHAT_CURRENT_STATE_UNSPECIFIED = 0;
  // 当前会话未结束
  CHAT_CURRENT_STATE_UNFINISHED = 1;
  // 当前会话已经被其他会话替代-已结束
  CHAT_CURRENT_STATE_REPLACED = 2;
  // 当前会话已经超过可聊天规定时限-已结束
  CHAT_CURRENT_STATE_TIMEOUT = 3;
}

enum QuestionAskType {
  QUESTION_ASK_TYPE_UNSPECIFIED = 0;
  // 正常问答
  QUESTION_ASK_TYPE_NORMAL = 1;
  // 重新回答(包括了用户输入的"重新回答"或者，用户输入了同样的问题)
  QUESTION_ASK_TYPE_REPETITION = 2;
  // 继续回答(包括被错误识别为了转人工之后确认为继续回答，或者 发送条数到达上限后的继续回答)
  QUESTION_ASK_TYPE_CONTINUE = 3;
  // 预设问答
  QUESTION_ASK_TYPE_DEFAULT_SET = 4;
  // 预设隐藏回答
  QUESTION_ASK_TYPE_DEFAULT_HIDE_SET = 5;
  // 文件问答
  QUESTION_ASK_TYPE_FILE = 6;
  // 语音问答
  QUESTION_ASK_TYPE_VOICE = 7;
  // 图片问答
  QUESTION_ASK_TYPE_IMAGE = 8;
  // 撤回问答（用户在微信端撤回消息）
  QUESTION_ASK_TYPE_RECALL = 9;
  // 匹配到QA的问答
  QUESTION_ASK_TYPE_MATCH_PATTERN = 10;
}

enum AIFileProcessing {
  AI_FILE_PROCESSING_UNSPECIFIED = 0;
  // 发送了文件但是不知道命令
  AI_FILE_PROCESSING_FILE_WITHOUT_CMD = 1;
  // 发送了读取配料表的命令但是没发送文件，当前agent把后续接收到的所有文件都按照读取配料表处理
  AI_FILE_PROCESSING_BOM_WITHOUT_FILE = 2;
  // 发送了读取检测报告的命令但是还没发送文件，当前agent把后续接收到的所有文件都按照读取检测报告处理
  AI_FILE_PROCESSING_REPORT_WITHOUT_FILE = 3;
  // 之前发送的文件都按照读取配料表处理
  AI_FILE_PROCESSING_BOM_WITH_FILE = 4;
  // 之前发送的文件都按照读取检测报告处理
  AI_FILE_PROCESSING_REPORT_WITH_FILE = 5;
  // 之前发送的文件按照用户输入的指令处理
  AI_FILE_PROCESSING_FILL_WITT_USER_CMD = 6;
}

enum AiRecordType {
  AI_RECORD_TYPE_UNSPECIFIED = 0;
  // 用户咨询
  AI_RECORD_TYPE_USER = 1;
  // 助手回答文本消息
  AI_RECORD_TYPE_AI_TEXT = 2;
  // 助手回答菜单消息
  AI_RECORD_TYPE_AI_MENU = 3;
  // 助手回答建议问题菜单消息
  AI_RECORD_TYPE_SUGGEST_AI_MENU = 4;
  // 助手回答贡献知识的小程序
  AI_RECORD_TYPE_KNOWLEDGE_MINI_PROGRAM = 5;
  // 发送转人工菜单信息
  AI_RECORD_TYPE_LIVE_AGENT_MENU = 6;
  // 助手回答图片
  AI_RECORD_TYPE_IMAGE = 7;
  // 助手回答音频
  AI_RECORD_TYPE_VOICE = 8;
  // 助手回答视频
  AI_RECORD_TYPE_VIDEO = 9;
  // 助手回答文件
  AI_RECORD_TYPE_FILE = 10;
}

enum AiRecordMessageType {
  AI_RECORD_MESSAGE_TYPE_UNSPECIFIED = 0;
  // 正常消息
  AI_RECORD_MESSAGE_TYPE_NORMAL = 1;
  // 重复的回答
  AI_RECORD_MESSAGE_TYPE_REPEAT_ANSWER = 2;
  // 隐藏的消息
  AI_RECORD_MESSAGE_TYPE_HIDE_ANSWER = 3;
  // 异常的消息
  AI_RECORD_MESSAGE_TYPE_EXCEPTION = 4;
}

message AiChatSendRecord {
  // record_id
  uint64 id = 1;
  // 发送消息后返回的uuid
  string uuid = 2;
  // 发送消息后返回的信息
  string info = 3;
  // 发送状态；1-未发送 2-成功发送 3-发送异常
  int32 state = 4;
  // 发送消息的文本
  string content = 5;
  AiRecordType type = 6;
  uint64 message_id = 7;
  AiRecordMessageType message_type = 8;
  uint64 chat_id = 9;
  google.protobuf.Timestamp create_date = 10;
  google.protobuf.Timestamp send_date = 11;
  // 0 不追加继续回答 1 追加继续回答
  uint32 send_type = 12;
}

message AiSendResultRecord {
  // record_id
  uint64 id = 1;
  // 发送消息后返回的uuid
  string uuid = 2;
  // 发送消息后返回的信息
  string info = 3;
  // 发送状态；1-未发送 2-成功发送 3-发送异常
  int32 state = 4;
  // 发送消息的文本
  string content = 5;
  AiRecordType type = 6;
  // 是否追加继续回答
  bool continue_answering = 7;
  // 查询发送记录时如果record是问题建议则有
  repeated string suggest_questions = 8;
  // record 对应的ID
  uint64 message_id = 9;
  AiRecordMessageType message_type = 10;
  // 扩展信息，现阶段用于 在发送媒体消息之前，获取的mediaId
  string extra_id = 11;
  // 扩展信息，现阶段用于 在发送媒体消息之前，获取mediaId时的记录信息
  string extra_info = 12;
}

enum ExportTaskState {
  EXPORT_TASK_STATE_UNSPECIFIED = 0;
  // 导出中
  EXPORT_TASK_STATE_RUNNING = 1;
  // 已完成
  EXPORT_TASK_STATE_COMPLETED = 2;
  // 失败
  EXPORT_TASK_STATE_FAILED = 3;
}

enum ExportTaskType {
  EXPORT_TASK_TYPE_UNSPECIFIED = 0;
  // QA1
  EXPORT_TASK_TYPE_QA = 1;
  // 文本文件
  EXPORT_TASK_TYPE_FILE = 2;
  // 会话消息
  EXPORT_TASK_TYPE_MESSAGE = 3;
  // 会话
  EXPORT_TASK_TYPE_CHAT = 4;
}

enum TaskOperationType{
  TASK_OPERATION_TYPE_UNSPECIFIED = 0;
  // 导出
  TASK_OPERATION_TYPE_EXPORT = 1;
  // 导入
  TASK_OPERATION_TYPE_IMPORT = 2;
}

message ExportTask {
  uint64 id = 1;
  ExportTaskState state = 2;
  ExportTaskType type = 3;
  string url = 4;
  uint64 create_by = 5;
  google.protobuf.Timestamp create_date = 6;
  google.protobuf.Timestamp last_update_date = 7;
  TaskOperationType operation_type = 8;
  string extra_info = 9;
  repeated string paths = 10;
  // 单次请求最大数量
  int32 max_batch_size = 11;
  // 返回数据最大阈值
  int32 max_response_threshold = 12;
  string filter_snapshot = 13;
  string fields_snapshot = 14;
}

message ExportMessage  {
  string    user_name = 1;
  string    assistant_name = 2;
  string    region_code = 3;
  string    live_agent_name = 4;
  string    question_text = 5;
  string    answer_text = 6;
  string    start_time = 7;
  string    end_time = 8;
  string    chat_duration = 9;
  string    doc_type_label = 13;
  string    search_link_label = 14;
  string    ugc_type_label = 15;
  string    rating_scale_label = 16;
  ChatMessageType type = 17;
  repeated  string  doc_names = 18;
}

// 助手渠道
enum AssistantChannel {
  ASSISTANT_CHANNEL_UNSPECIFIED = 0;
  // 碳LIVE-微信
  ASSISTANT_CHANNEL_TANLIVE_WEIXIN = 1;
  // 碳LIVE-Web
  ASSISTANT_CHANNEL_TANLIVE_WEB = 2;
  // 碳LIVE-应用
  ASSISTANT_CHANNEL_TANLIVE_APP = 3;
  // 碳LIVE-WhatsApp
  ASSISTANT_CHANNEL_TANLIVE_WHATSAPP = 4;
  // 第三方机构-微信
  ASSISTANT_CHANNEL_THIRD_PARTY_WEIXIN = 5;
  // 碳LIVE-小程序
  ASSISTANT_CHANNEL_TANLIVE_MINIPROGRAM = 6;
}

// 完整助手信息
message FullAssistant {
  // 助手详情
  AssistantV2 assistant = 1;
  // 是否确认协议
  bool terms_confirmed = 2;
}

// 助手信息
message AssistantV2 {
  // 主键
  uint64 id = 1;
  // 创建人
  base.Identity create_by = 2;
  // 更新人
  base.Identity update_by = 3;
  // 创建时间
  google.protobuf.Timestamp create_date = 4;
  // 更新时间
  google.protobuf.Timestamp update_date = 5;
  // 配置详情
  AssistantConfig config = 6;
  // 是否为草稿
  bool is_draft = 7;
  // 批次号
  string batch_no = 8;
}

// 助手配置
message AssistantConfig {
  // 搜索测试
  bool search_debug = 1;
  // 链路查询
  AssistantVisibleChainConfig visible_chain_config = 2;
  // 参数管理权限
  AssistantFieldManageConfig field_manage_config = 3;
  // AI助手名
  string name = 4;
  // AI助手名（英文）
  string name_en = 5;
  // 渠道
  AssistantChannel channel = 6;
  // 是否启用
  bool enabled = 7;
  // 助手管理员
  repeated base.Identity admins = 8;

  // 知识库语言
  string collection_lang = 9;
  // 知识库名称（自动生成）
  string collection_name = 10;
  // 提示词
  string prompt_prefix = 11;
  // 对话模型
  string model = 12;
  // 对话阈值
  float threshold = 13;
  // 对话轮数
  int32 history_rounds = 14;
  // 搜索引擎
  string search_engine = 15;
  // 知识库Top_N
  int32 doc_top_n = 16;
  // 互联网Top_N
  int32 search_top_n = 17;
  // ChatOrSql配置
  AssistantChatOrSqlConfig chat_or_sql_config = 18;
  // 问题建议配置
  AssistantAskSuggestionConfig ask_suggestion_config = 19;

  // 微信渠道配置
  AssistantWeixinChannelConfig weixin_channel_config = 20;
  // 碳LIVE Web渠道配置
  AssistantTanliveWebChannelConfig tanlive_web_channel_config = 21;
  // Web渠道配置
  AssistantTanliveAppChannelConfig tanlive_app_channel_config = 22;
  // WhatsApp渠道配置
  AssistantWhatsappChannelConfig whatsapp_channel_config = 23;
  // 小程序渠道配置
  AssistantMiniprogramChannelConfig miniprogram_channel_config = 28;

  // 使用地区（空不限制）
  string use_region_code = 24;
  // 是否关闭搜索增强
  bool close_search = 25;
  // 关键词搜索权重
  float text_weight = 26;
  // 未命中知识库自动回复
  string miss_reply = 27;
  // 一句话介绍
  string brief_intro = 29;
  // 助手介绍
  string detail_intro = 30;
  // 分段配置
  AssistantChunkConfig chunk_config = 31;
  // 是否展示思考过程
  bool show_think = 32;
  // 关键词召回条数
  int32 text_recall_top_n = 33;
  // 温度
  float temperature = 34;
  // 问题分类配置
  AssistantQuestionTypeConfig question_type_config = 35;
  // 是否在助手列表展示
  bool show_in_list = 36;
  // 白名单配置
  AssistantAllowlistConfig allowlist_config = 37;
  // 自动过滤
  bool clean_chunks = 38;
  // QA关键词召回目标
  TextRecallQuery text_recall_query = 39;
  // 关键词召回模式
  TextRecallPattern text_recall_pattern = 40;
  // 允许平移距离
  int32 text_recall_slop = 41;
  // 助手用户打标配置
  AssistantUserLabelConfig user_label_config = 42;
}

// 微信渠道配置
message AssistantWeixinChannelConfig {
  // 助手昵称
  string nickname = 1;
  // 头像（默认头像留空）
  string avatar_url = 2;
  // 系统语言（已废弃，请使用system_languages）
  string system_lang = 3;
  // 微信开发配置
  AssistantWeixinDevelopConfig weixin_develop_config = 4;
  // 欢迎语配置
  AssistantWelcomeMessageConfig welcome_message_config = 5;
  // 预设问题配置
  AssistantPresetQuestionConfig preset_question_config = 6;
  // 人工客服配置
  AssistantKefuConfig kefu_config = 7;
  // 满意度回复配置
  AssistantRatingScaleReplyConfig rating_scale_reply_config = 8;
  // 互动暗号配置
  AssistantInteractiveCodeConfig interactive_code_config = 9;
  // 会话闲置超时时间（分钟）
  int32 chat_idle_duration = 10;
  // 系统语言
  repeated string system_languages = 11;
}

// 碳LIVE Web助手渠道配置
message AssistantTanliveWebChannelConfig {
  // 助手昵称
  string nickname = 1;
  // 助手昵称（英文）
  string nickname_en = 2;
  // 头像（默认头像留空）
  string avatar_url = 3;
  // 网站配置
  AssistantWebsiteConfig website_config = 4;
  // 欢迎语配置
  AssistantWelcomeMessageConfig welcome_message_config = 5;
  // 预设问题配置
  AssistantPresetQuestionConfig preset_question_config = 6;
  // 满意度回复配置
  AssistantRatingScaleReplyConfig rating_scale_reply_config = 7;
  // 互动暗号配置
  AssistantInteractiveCodeConfig interactive_code_config = 8;
  // 用户教学反馈
  bool feedback_enabled = 9;
  // 图谱解析配置
  AssistantGraphParseConfig graph_parse_config = 10;
  // 助手语言（已废弃，请使用system_languages）
  string assistant_lang = 11;
  // 切换助手ID
  uint64 switch_assistant_id = 12;
  // 人工客服
  AssistantKefuConfig kefu_config = 13;
  // 系统语言
  repeated string system_languages = 14;
}

// 碳LIVE应用助手渠道配置
message AssistantTanliveAppChannelConfig {
  // 助手昵称
  string nickname = 1;
  // 助手昵称（英文）
  string nickname_en = 2;
  // 头像（默认头像留空）
  string avatar_url = 3;
  // 欢迎语配置
  AssistantWelcomeMessageConfig welcome_message_config = 4;
  // 预设问题配置
  AssistantPresetQuestionConfig preset_question_config = 5;
  // 满意度回复配置
  AssistantRatingScaleReplyConfig rating_scale_reply_config = 6;
  // 互动暗号配置
  AssistantInteractiveCodeConfig interactive_code_config = 7;
  // 用户教学反馈
  bool feedback_enabled = 8;
  // 图谱解析配置
  AssistantGraphParseConfig graph_parse_config = 9;
  // 助手语言（已废弃，请使用system_languages）
  string assistant_lang = 10;
  // 人工客服
  AssistantKefuConfig kefu_config = 11;
  // 系统语言
  repeated string system_languages = 12;
  // 应用ID
  string app_id = 13;
}

// Whatsapp助手渠道配置
message AssistantWhatsappChannelConfig {
  // 系统语言（已废弃，请使用system_languages）
  string system_lang = 1;
  // WhatsApp开发配置
  AssistantWhatsappDevelopConfig whatsapp_develop_config = 2;
  // 欢迎语配置
  AssistantWelcomeMessageConfig welcome_message_config = 3;
  // 预设问题配置
  AssistantPresetQuestionConfig preset_question_config = 4;
  // 满意度回复配置
  AssistantRatingScaleReplyConfig rating_scale_reply_config = 5;
  // 会话闲置超时时间（分钟）
  int32 chat_idle_duration = 10;
  // 昵称
  string nickname = 11;
  // 头像
  string avatar_url = 12;
  // 系统语言
  repeated string system_languages = 13;
}

// 助手小程序渠道配置
message AssistantMiniprogramChannelConfig {
  // 助手昵称
  string nickname = 1;
  // 助手昵称（英文）
  string nickname_en = 2;
  // 头像（默认头像留空）
  string avatar_url = 3;
  // 小程序配置
  AssistantMiniprogramConfig miniprogram_config = 4;
  // 欢迎语配置
  AssistantWelcomeMessageConfig welcome_message_config = 5;
  // 预设问题配置
  AssistantPresetQuestionConfig preset_question_config = 6;
  // 满意度回复配置
  AssistantRatingScaleReplyConfig rating_scale_reply_config = 7;
  // 互动暗号配置
  AssistantInteractiveCodeConfig interactive_code_config = 8;
  // 用户教学反馈
  bool feedback_enabled = 9;
  // 图谱解析配置
  AssistantGraphParseConfig graph_parse_config = 10;
  // 助手语言（已废弃，请使用system_languages）
  string assistant_lang = 11;
  // 人工客服
  AssistantKefuConfig kefu_config = 12;
  // 系统语言
  repeated string system_languages = 13;
}

// 小程序配置
message AssistantMiniprogramConfig {
  // 分享标题
  string share_title = 1;
  // 分享图片
  string share_image = 2;
  // 二维码URL
  string url = 3;
  // 小程序schema
  string schema = 4;
}

// 链路查询
message AssistantVisibleChainConfig {
  // 是否启用
  bool enabled = 1;
  // 可见字段
  repeated string visible = 2;
}

// 字段管理配置
message AssistantFieldManageConfig {
  // 是否启用（该字段暂不使用）
  bool enabled = 1;
  // 可读字段（为空全部可读）
  repeated string readable = 2;
  // 可写字段（为空全部不可写）
  repeated string writable = 3;
}

// 欢迎语配置
message AssistantWelcomeMessageConfig {
  // 配置列表
  repeated AssistantWelcomeMessage messages = 1;
}

// 欢迎语详情
message AssistantWelcomeMessage {
  // 语言
  string lang = 1;
  // 内容
  string content = 2;
}

// 预设问题配置
message AssistantPresetQuestionConfig {
  // 是否启用
  bool enabled = 1;
  // 配置列表
  repeated AssistantPresetQuestion questions = 2;
}

// 预设问题详情
message AssistantPresetQuestion {
  // 语言
  string lang = 1;
  // 内容
  string content = 2;
}

// 满意度回复配置
message AssistantRatingScaleReplyConfig {
  // 是否启用
  bool enabled = 1;
  // 满意度回复配置
  repeated AssistantRatingScaleReply replies = 2;
}

// 满意度回复详情
message AssistantRatingScaleReply {
  // 语言
  string lang = 1;
  // 评价等级
  RatingScale rating_scale = 2;
  // 内容
  string content = 3;
}

// 互动暗号
enum InteractiveCode {
  INTERACTIVE_CODE_UNSPECIFIED = 0;
  // 人工
  INTERACTIVE_CODE_MANUAL = 1;
  // 重新回答
  INTERACTIVE_CODE_ANSWER_AGAIN = 2;
  // 清空上下文
  INTERACTIVE_CODE_CLEAR_CONTEXT = 3;
  // 读配料表
  INTERACTIVE_CODE_READ_BOM = 4;
  // 读检测报告
  INTERACTIVE_CODE_READ_TEST_REPORT = 5;
  // 贡献知识库
  INTERACTIVE_CODE_CONTRIBUTE_KNOWLEDGE = 6;
}

// 互动暗号配置
message AssistantInteractiveCodeConfig {
  // 暗号配置
  repeated AssistantInteractiveCode codes = 1;
  // 开启回复发送暗号
  bool send_interactive_code = 2;
}

// 互动暗号详情
message AssistantInteractiveCode {
  // 语言
  string lang = 1;
  // 互动暗号
  InteractiveCode interactive_code = 2;
  // 内容
  string content = 3;
  // 是否在欢迎语里显示
  bool show_in_welcome = 4;
}

// 图谱解析配置
message AssistantGraphParseConfig {
  // 是否启用
  bool enabled = 1;
  // 模型
  string model = 2;
}

// ChatOrSql配置
message AssistantChatOrSqlConfig {
  // 是否启用
  bool enabled = 1;
  // 模型
  string model = 2;
}

// 问题建议模式
enum AskSuggestionMode {
  ASK_SUGGESTION_MODE_UNSPECIFIED = 0;
  // 模式1：根据历史问答生成问题建议
  ASK_SUGGESTION_MODE_1 = 1;
  // 模式2：根据历史问答生成，并仅显示知识库中有相关知识的问题建议
  ASK_SUGGESTION_MODE_2 = 2;
  // 模式3：根据问题在知识库中已命中的知识，生成问题建议
  ASK_SUGGESTION_MODE_3 = 3;
  // 模式4：根据问题在知识库中尚未命中但排名靠前的知识，生成问题建议
  ASK_SUGGESTION_MODE_4 = 4;
}

// 问题建议配置
message AssistantAskSuggestionConfig {
  // 是否启用
  bool enabled = 1;
  // 提示词
  string prompt = 2;
  // 问题建议数量
  int32 count = 3;
  // 问题建议模式
  AskSuggestionMode mode = 4;
  // 问题建议倍数
  uint32 times = 5;
  // 模型
  string model = 6;
}

// 网站配置
message AssistantWebsiteConfig {
  // 路由路径
  string route_path = 1;
  // 标题
  string title = 2;
}

// 人工客服配置
message AssistantKefuConfig {
  // 是否启用
  bool enabled = 1;
  // 员工信息
  repeated AssistantKefuStaff staffs = 2;
  // 转人工后的提示语
  string after_remind_message = 3;
  // 是否启用转人工前的提示语
  bool before_remind_enabled = 4;
  // 转人工前的提示语
  string before_remind_message = 5;
  // 客服自动回复配置
  AssistantKefuReply reply = 6;
}

// 人工客服信息
message AssistantKefuStaff {
  // 主键
  uint64 id = 1;
  // 账号
  string username = 2;
  // 昵称
  string nickname = 3;
}

// 助手客服自动回复
message AssistantKefuReply {
  // 大陆中文
  Reply mainland_zh = 1 ;
  // 大陆英文
  Reply mainland_en = 2 ;
  // 非中国大陆中文
  Reply non_mainland_zh = 3 ;
  // 非中国大陆英文
  Reply non_mainland_en = 4 ;
  // 是否启用自定义配置
  bool enable_custom = 5;
  // 自定义配置列表
  repeated Custom custom = 6;
  message Reply {
    //  回复消息
    string reply_message = 1 ;
    // 图片地址
    string img_url = 2 ;
  }
  message Custom {
    // url地址
    string url = 1 [(validator) = "required"];
    // 显示文本
    string text = 2 [(validator) = "required"];
  }
}

// 分段配置
message AssistantChunkConfig {
  // 最小字符数
  int32 min_char_count = 1;
  // 最小字符语言
  string min_char_lang = 2;
  // 最大字符数
  int32 max_char_count = 3;
  // 最大字符语言
  string max_char_lang = 4;
  // 重合字符数
  int32 overlap_count = 5;
  // 重合字符语言
  string overlap_lang = 6;
}

// 微信开发配置
message AssistantWeixinDevelopConfig {
  // 企业ID
  string corp_id = 1;
  // 客服账号ID
  string open_kfid = 2;
  // 客服URL
  string kf_url = 3;
}

// WhatsApp开发配置
message AssistantWhatsappDevelopConfig {
  // Business number
  string business_number = 1;
}

// 问题类型配置
message AssistantQuestionTypeConfig {
  // 是否启用
  bool enabled = 1;
  // 聊天问题的模型
  string chat_model = 2;
  // 简单问题的模型
  string simple_model = 3;
  // 复杂问题的模型
  string complex_model = 4;
  // 简单、复杂问题提示词
  string prompt = 5;
}

// 助手白名单类型
enum AssistantAllowlistType {
  ASSISTANT_ALLOWLIST_TYPE_UNSPECIFIED = 0;
  // 手机号码
  ASSISTANT_ALLOWLIST_TYPE_PHONE = 1;
  // 微信昵称
  ASSISTANT_ALLOWLIST_TYPE_WECHAT_NICKNAME = 2;
}

// 助手白名单配置
message AssistantAllowlistConfig {
  // 是否启用
  bool enabled = 1;
  // 白名单类型
  AssistantAllowlistType type = 2;
  // 手机列表
  repeated string phones = 3;
}

// 助手用户打标配置
message AssistantUserLabelConfig {
  // 绑定标签id
  repeated uint64 tag_ids = 1;
  // 绑定标签的名称
  repeated string tag_names = 2;
}

// 聊天模型选项
message ChatModelOption {
  // 模型
  string model = 1;
  // 是否在用户后台禁用
  bool disable_in_console = 2;
  // 是否支持思考
  bool support_think = 3;
  // 仅支持非流式
  bool only_non_stream = 4;
}

// 互动暗号选项
message InteractiveCodeOption {
  // 编号
  InteractiveCode code = 1;
  // 文本
  string text = 2;
  // 默认中文值
  string default_zh = 3;
  // 默认英文值
  string default_en = 4;
  // 是否可删除
  bool deletable = 5;
  // 默认中文前缀
  string default_pre_zh = 6;
  // 默认英文前缀
  string default_pre_en = 7;
}

// 链路查询选项
message VisibleChainOption {
  // 字段
  string field = 1;
  // 名称
  string name = 2;
  // 英文名称
  string name_en = 3;
  // 是否默认不选中
  bool uncheck = 4;
}

// 搜索引擎选项
message SearchEngineOption {
  // 值
  string value = 1;
  // 名称
  string name = 2;
  // 英文名称
  string name_en = 3;
}

// 助手urgent配置
message QuickAction {
  // assistantIds
  string assistants = 1;
  // 命令
  string content = 2;
  // 类型
  int32 type = 3;
  // 下一条问题不能携带image
  bool next_msg_file_forbidden = 4;
}

// 匹配模式
enum DocMatchPattern {
  DOC_MATCH_PATTERN_UNSPECIFIED = 0;
  // 大模型召回
  DOC_MATCH_PATTERN_LARGE_MODEL_RECALL = 1;
  // 完全匹配
  DOC_MATCH_PATTERN_FULL_MATCH = 2;
  // 忽略标点匹配
  DOC_MATCH_PATTERN_IGNORE_MARK_MATCH = 3;
  // 未命中
  DOC_MATCH_PATTERN_MISS_MATCH = 4;
  // 包含关键字
  DOC_MATCH_PATTERN_CONTAINS = 5;
}

// QA关键词召回目标
enum TextRecallQuery {
  TEXT_RECALL_QUERY_UNSPECIFIED = 0;
  // 在知识库的"QA"中召回
  TEXT_RECALL_QUERY_QA = 1;
  // 仅在知识库的"Q"中召回
  TEXT_RECALL_QUERY_Q = 2;
}

// 关键词召回模式
enum TextRecallPattern {
  TEXT_RECALL_PATTERN_UNSPECIFIED = 0;
  // 短语匹配
  TEXT_RECALL_PATTERN_MATCH_PHRASE = 1;
  // 字匹配
  TEXT_RECALL_PATTERN_MATCH = 2;
  // 英文模糊匹配
  TEXT_RECALL_PATTERN_FUZZY = 3;
}

// 向量化模型选项
message EmbeddingModelOption {
  // 模型名称
  string model = 1;
  // 技术最小分段长度（token）
  int32 tech_seg_min_tokens = 2;
  // 技术最大分段长度（token）
  int32 tech_seg_max_tokens = 3;
  // 技术overlap最小长度（token）
  int32 tech_overlap_min_tokens = 4;
  // 技术overlap最大长度（token）
  int32 tech_overlap_max_tokens = 5;
  // 用户最小分段长度默认值（token）
  int32 user_seg_min_tokens = 6;
  // 用户最大分段长度默认值（token）
  int32 user_seg_max_tokens = 7;
  // 用户overlap长度默认值（token）
  int32 user_overlap_tokens = 8;
  // 1个中文字符约对应多少个token（下限）
  float zh_min_tokens_per_char = 9;
  // 1个中文字符约对应多少个token（上限）
  float zh_max_tokens_per_char = 10;
  // 1个英文字符约对应多少个token（下限）
  float en_min_tokens_per_char = 11;
  // 1个英文字符约对应多少个token（上限）
  float en_max_tokens_per_char = 12;
  // embedding向量长度
  int32 embedding_vector_length = 13;
  // 是否推荐
  bool recommended = 14;
  // 名称
  string name = 15;
  // 中文默认配置
  AssistantChunkConfig zh_default = 16;
  // 英文默认配置
  AssistantChunkConfig en_default = 17;
}

// 向量化模型计数
message EmbeddingModelCount {
  // 向量化模型
  string collection_lang = 1;
  // 数量
  uint32 count = 2;
  // 向量化模型名称
  string embedding_model_name = 3;
}

// 助手操作类型
enum AssistantAction {
  ASSISTANT_ACTION_UNSPECIFIED = 0;
  // 创建
  ASSISTANT_ACTION_CREATE = 1;
  // 保存草稿
  ASSISTANT_ACTION_SAVE_DRAFT = 2;
  // 发布
  ASSISTANT_ACTION_PUBLISH = 3;
  // 删除
  ASSISTANT_ACTION_DELETE = 4;
}

// 助手字段变化
message AssistantChanges {
  // 变动字段
  repeated string fields = 1;
  // 变化前的配置
  AssistantConfig old = 2;
  // 变化后的配置
  AssistantConfig new = 3;
}

// 助手日志
message AssistantLog {
  // 日志ID
  uint64 id = 1;
  // 助手ID
  uint64 assistant_id = 2;
  // 操作类型
  AssistantAction action = 3;
  // 配置变化
  AssistantChanges changes = 4;
  // 操作人
  base.Identity create_by = 5;
  // 操作时间
  google.protobuf.Timestamp create_date = 6;
}

// 助手的分段信息
message AssistantChunks {
  // 助手ID
  repeated uint64 assistant_id = 1 [(validator) = "required,dive,required"];
  // 分段列表
  repeated ChunkItem chunks = 2 [(validator) = "required,dive,required"];
}

// 文档分段信息
message ChunkItem {
  // 起始索引位置
  uint32 start = 1;
  // 内容长度
  uint32 len = 2 [(validator) = "min=1"];
  // 分段内容
  string content = 3;
}

// 文档手动分段参数
message ManualChunkPara {
  // 新分段列表
  repeated AssistantChunks assistant_chunks = 2 [(validator) = "omitempty,dive,required"];
}

// 自动分段参数
message AutoChunkPara {
  // 助手ID
  repeated uint64 assistant_id = 1 [(validator) = "required,dive,required"];
  // 分段配置
  AssistantChunkConfig chunk_config = 2 [(validator) = "required"];
  // 仅预览
  bool dry_run = 3;
}

// 文档分段任务类型
enum DocChunkTaskType {
  DOC_CHUNK_TASK_TYPE_UNSPECIFIED = 0;
  // 手动分段
  DOC_CHUNK_TASK_TYPE_MANUAL = 1;
  // 自动分段
  DOC_CHUNK_TASK_TYPE_AUTO = 2;
}

// 文档分段任务状态
enum DocChunkTaskState {
  DOC_CHUNK_TASK_STATE_UNSPECIFIED = 0;
  // 执行中
  DOC_CHUNK_TASK_STATE_RUNNING = 1;
  // 已完成
  DOC_CHUNK_TASK_STATE_FINISHED = 2;
}

// 文档分段子任务状态
enum DocChunkSubtaskState {
  DOC_CHUNK_SUBTASK_STATE_UNSPECIFIED = 0;
  // 等待激活（不可执行）
  DOC_CHUNK_SUBTASK_STATE_UNACTIVATED = 1;
  // 已激活（可以执行）
  DOC_CHUNK_SUBTASK_STATE_ACTIVATED = 2;
  // 执行成功
  DOC_CHUNK_SUBTASK_STATE_SUCCESS = 3;
  // 执行失败
  DOC_CHUNK_SUBTASK_STATE_FAILED = 4;
}

// 文档分段子任务类型
enum DocChunkSubtaskType {
  DOC_CHUNK_SUBTASK_TYPE_UNSPECIFIED = 0;
  // 创建分段
  DOC_CHUNK_SUBTASK_TYPE_CREATE_CHUNK = 1;
  // 删除分段
  DOC_CHUNK_SUBTASK_TYPE_DELETE_CHUNK = 2;
}

// 文段分段任务
message DocChunkTask {
  // 任务ID
  uint64 id = 1;
  // 任务状态
  DocChunkTaskState state = 2;
}

// 重建索引状态
enum ReindexState {
  REINDEX_STATE_UNSPECIFIED = 0;
  // 等待执行
  REINDEX_STATE_WAITING = 1;
  // 运行中
  REINDEX_STATE_RUNNING = 2;
  // 已完成
  REINDEX_STATE_FINISHED = 3;
}

// 重建索引结果
enum ReindexResult {
  REINDEX_RESULT_UNSPECIFIED = 0;
  // 重建索引结果成功
  REINDEX_RESULT_SUCCESS = 1;
  // 重建索引结果失败
  REINDEX_RESULT_FAILED = 2;
}

// AiAssistantNoticeConf ai助手横幅提示配置
message AiAssistantNoticeConf {
  message Notice {
    string zh = 1 [(validator) = "required"];
    string en = 2 [(validator) = "required"];
  }
  Notice notice = 1;
  // 是否启用
  bool enable = 2;
  tanlive.base.TimeRange range_time = 3;
  repeated AssistantChannel channel = 4 [(validator) = "required"];
}

message TextFileTipTableOverSize{
  // 表头
  string header = 1;
  // 助手 id
  uint64 assistant_id = 2;
  // 助手中文名称
  string assistant_name = 3;
  // 助手英文名称
  string assistant_name_en = 4;
  // 表格标题
  string table_title = 5;
}
// 知识的数据来源
enum DocDataSource{
  DOC_DATA_SOURCE_UNSPECIFIED = 0;
  // tanlive ugc数据
  DOC_DATA_SOURCE_UGC = 1;
  // tanlive 知识库数据
  DOC_DATA_SOURCE_COLLECTION = 2;
  // 腾讯云文档
  DOC_DATA_SOURCE_TCLOUD_DOCUMENT = 3;
  // SQL 数据
  DOC_DATA_SOURCE_SQL = 4;
}



message MessageMatchQaFilter {
  int32 limit = 1;
  // 用来筛选的匹配模式
  DocMatchPattern match_patterns = 2;
}

message MessageCollectionSnapshot {
  google.protobuf.Timestamp start_time = 1;
  google.protobuf.Timestamp end_time = 2;
  repeated SearchCollectionItem items = 3;
  bool clean_chunks = 4;
  uint64 message_id = 5;
}

message MessageDocSnapshot {
  uint64 message_id  = 1;
  repeated ChatMessageDoc docs = 2;
}

// 分享类型
enum ShareType {
  SHARE_TYPE_UNSPECIFIED = 0;
  // 链接分享
  SHARE_TYPE_LINK = 1;
  // 二维码分享
  SHARE_TYPE_QR_CODE = 2;
  // 小程序码分享
  SHARE_TYPE_MINI_PROGRAM = 3;
}

// 分享状态
enum ShareStatus {
  SHARE_STATUS_UNSPECIFIED = 0;
  // 有效
  SHARE_STATUS_ACTIVE = 1;
  // 已失效（手动失效）
  SHARE_STATUS_INACTIVE = 2;
  // 已过期
  SHARE_STATUS_EXPIRED = 3;
}

// 聊天分享
message ChatShare {
  // 分享ID
  uint64 id = 1;
  // 分享唯一标识
  string share_id = 2;
  // 原会话ID
  uint64 chat_id = 3;
  // 分享类型
  ShareType share_type = 4;
  // 分享状态
  ShareStatus share_status = 5;
  // 分享者ID
  uint64 shared_by = 6;
  // 访问次数
  int32 access_count = 7;
  // 分享创建时间
  google.protobuf.Timestamp share_date = 8;
  // 分享过期时间
  google.protobuf.Timestamp expire_date = 9;
  // 最后访问时间
  google.protobuf.Timestamp last_access_time = 10;
  // 助手ID
  uint64 assistant_id = 11;
  // 分享URL
  string share_url = 12;
}

// 分享访问记录
message ChatShareAccess {
  // 访问记录ID
  uint64 id = 1;
  // 分享ID
  string share_id = 2;
  // 访问者ID
  uint64 access_by = 3;
  // 访问时间
  google.protobuf.Timestamp access_date = 4;
  // 是否点击了"接着聊"
  bool is_continued = 7;
  // 新会话ID
  uint64 new_chat_id = 8;
}

enum ExternalSourceUserAuthState{
  EXTERNAL_SOURCE_USER_AUTH_STATE_UNSPECIFIED = 0;
  // 已授权
  EXTERNAL_SOURCE_USER_AUTH_STATE_AUTHORIZED = 1;
  // 未授权
  EXTERNAL_SOURCE_USER_AUTH_STATE_UNAUTHORIZED = 2;
  // 已失效
  EXTERNAL_SOURCE_USER_AUTH_STATE_EXPIRED = 3;
  // 已取消
  EXTERNAL_SOURCE_USER_AUTH_STATE_CANCELED = 4;
}

message ExternalSourceUser{
  // 用户ID
  string user_id = 1;
  // 用户昵称
  string nickname = 2;
  // 用户头像
  string avatar = 3;
  // 用户状态
  ExternalSourceUserAuthState auth_state = 4;
  // 用户授权来源(xw qq)
  string auth_source = 5;
}

// 文档分享类型
enum DocShareReceiverType {
  DOC_SHARE_RECEIVER_TYPE_UNSPECIFIED = 0;
  // 分享到个人
  DOC_SHARE_RECEIVER_TYPE_USER = 1;
  // 分享到团队
  DOC_SHARE_RECEIVER_TYPE_TEAM = 2;
  // 分享到助手
  DOC_SHARE_RECEIVER_TYPE_ASSISTANT = 3;
}

message DocShareReceiver {
  // 分享类型
  DocShareReceiverType share_type = 1;
  // 接收方ID
  uint64 id = 2;

  string name = 3;
  string name_en = 4;
}

enum DocEmbeddingState{
  DOC_EMBEDDING_STATE_UNSPECIFIED = 0;
  // 同步中
  DOC_EMBEDDING_STATE_SYNCING = 1;
  // 同步完成
  DOC_EMBEDDING_STATE_SYNCED = 2;
  // 删除中
  DOC_EMBEDDING_STATE_DELETING = 3;
}
